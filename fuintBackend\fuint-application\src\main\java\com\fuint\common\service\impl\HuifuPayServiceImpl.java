package com.fuint.common.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fuint.utils.StringUtil;
import com.fuint.common.bean.HuifuPayBean;
import com.fuint.common.bean.WxPayBean;
import com.fuint.common.dto.OrderDto;
import com.fuint.common.dto.UserOrderDto;
import com.fuint.common.enums.OrderStatusEnum;
import com.fuint.common.enums.PayStatusEnum;
import com.fuint.common.service.HuifuPayService;
import com.fuint.common.service.OrderService;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.framework.web.ResponseObject;
import com.fuint.repository.mapper.MtOrderMapper;
import com.fuint.repository.model.MtOrder;
import com.fuint.repository.model.MtUser;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.huifu.bspay.sdk.opps.client.BasePayClient;
import com.huifu.bspay.sdk.opps.core.BasePay;
import com.huifu.bspay.sdk.opps.core.config.MerConfig;
import com.huifu.bspay.sdk.opps.core.exception.BasePayException;
import com.huifu.bspay.sdk.opps.core.net.BasePayRequest;
import com.huifu.bspay.sdk.opps.core.request.BaseRequest;
import com.huifu.bspay.sdk.opps.core.request.V2TradePaymentJspayRequest;
import com.huifu.bspay.sdk.opps.core.request.V2TradePaymentScanpayRefundRequest;
import com.huifu.bspay.sdk.opps.core.utils.DateTools;
import com.huifu.bspay.sdk.opps.core.utils.ObjectUtils;
import com.huifu.bspay.sdk.opps.core.utils.RsaUtils;
import com.huifu.bspay.sdk.opps.core.utils.SequenceTools;
import com.ijpay.core.enums.SignType;
import com.ijpay.core.kit.WxPayKit;
import com.ijpay.wxpay.WxPayApiConfig;
import com.ijpay.wxpay.WxPayApiConfigKit;

import cn.hutool.json.JSONArray;

import com.huifu.bspay.sdk.opps.core.request.V2TradePaymentMicropayRequest;
import com.huifu.bspay.sdk.opps.core.request.V2TradePaymentScanpayQueryRequest;

import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

import javax.annotation.PostConstruct;

/**
 * 汇付支付相关接口实现类
 */
@Service
@AllArgsConstructor
public class HuifuPayServiceImpl implements HuifuPayService {

    private static final Logger logger = LoggerFactory.getLogger(HuifuPayServiceImpl.class);

    private HuifuPayBean huifuPayBean;

    private OrderService orderService;

    
    private MtOrderMapper mtOrderMapper;
    
    private WxPayBean wxPayBean; 


    @PostConstruct
    public void init() {
        try {
            MerConfig merConfig = new MerConfig();
            merConfig.setProcutId(huifuPayBean.getProcutId());
            merConfig.setSysId(huifuPayBean.getSysId());
            merConfig.setRsaPrivateKey(huifuPayBean.getRsaPrivateKey());
            merConfig.setRsaPublicKey(huifuPayBean.getRsaPublicKey());
            BasePay.initWithMerConfig(merConfig);
            BasePay.debug = true;
        } catch (Exception e) {
            logger.error("Failed to initialize HuifuPayService", e);
            throw new IllegalStateException("HuifuPayService initialization failed", e);
        }
    }
    
    private static String getAcctSplitBunch() {
        JSONObject dto = new JSONObject();
        dto.put("percentage_flag", "Y");
        
        // 分账明细
        JSONArray acctInfos = new JSONArray();
        JSONObject acct1 = new JSONObject();
        acct1.put("huifu_id", "6666000163869956");
        acct1.put("percentage_div", "10.00");
        acctInfos.add(acct1);

        JSONObject acct2 = new JSONObject();
        acct2.put("huifu_id", "6666000163830613");
        acct2.put("percentage_div", "5.00");
        acctInfos.add(acct2);

        JSONObject acct3 = new JSONObject();
        acct3.put("huifu_id", "6666000163549307");
        acct3.put("percentage_div", "64.00");
        acctInfos.add(acct3); 

        JSONObject acct4 = new JSONObject();
        acct4.put("huifu_id", "6666000164089411");
        acct4.put("percentage_div", "1.00");
        acctInfos.add(acct4);

        JSONObject acct5 = new JSONObject();
        acct5.put("huifu_id", "66660***********");
        acct5.put("percentage_div", "20.00");
        acctInfos.add(acct5);

        dto.put("acct_infos",acctInfos);

        return dto.toJSONString();
    }
    
    /**
     * 创建JSAPI预支付订单（微信正扫）
     * 小程序、H5内发起支付使用
     * @param userInfo
     * @param orderInfo
     * @param payAmount
     * @param authCode
     * @param giveAmount
     * @param ip
     * @param platform
     * @return
     * @throws BusinessCheckException
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseObject createWechatJsPay(MtUser userInfo, MtOrder orderInfo, Integer payAmount, String authCode, Integer giveAmount, String ip, String platform) throws BusinessCheckException {
        try {
            // 创建JSAPI支付请求
            V2TradePaymentJspayRequest request = new V2TradePaymentJspayRequest();
            
            // 设置请求参数
            request.setReqDate(DateTools.getCurrentDateYYYYMMDD()); // 请求日期
            request.setReqSeqId(SequenceTools.getReqSeqId32()); // 请求流水号
            request.setHuifuId(huifuPayBean.getHuifuId()); // 商户号
            request.setTradeType("T_MINIAPP"); // 交易类型
            request.setTransAmt(String.format("%.2f", payAmount / 100.0)); // 交易金额（单位：元）
            
            // 设置订单信息
            request.setGoodsDesc(orderInfo.getOrderSn()); // 商品描述
            Map<String, Object> extendInfoMap = new HashMap<>();       
            // 安全信息
            JSONObject riskDto = new JSONObject();
            // ip地址
            riskDto.put("ip_addr", ip); 
            extendInfoMap.put("risk_check_data",riskDto);  
             
            // 异步通知地址
            extendInfoMap.put("notify_url", "https://hatea.zhijuchina.com/api/clientApi/pay/huifuCallback");
            // 备注
            extendInfoMap.put("remark", orderInfo.getOrderSn());

            // 聚合正扫微信拓展参数集合
            JSONObject wxDto = new JSONObject();
            wxDto.put("sub_appid", wxPayBean.getAppId());
            wxDto.put("sub_openid", userInfo.getOpenId());
            extendInfoMap.put("wx_data", wxDto);
 
            // 分账对象
            extendInfoMap.put("acct_split_bunch", getAcctSplitBunch());

            request.setExtendInfo(extendInfoMap);

            // 分账 
            
            // 发送请求
            Map<String, Object> response = BasePayClient.request(request);    
            logger.info("createWechatJsPay",JSON.toJSONString(response));    
 
            
            String resp_code = (String) response.get("resp_code");
            if (resp_code.equals("********")) {
                String pay_info = (String) response.get("pay_info"); 
                logger.info("小程序支付的参数:" + pay_info);    

                orderInfo.setHfOrder(JSON.toJSONString(response));
                mtOrderMapper.updateById(orderInfo);
                return new ResponseObject(200, "预支付订单创建成功", JSON.parseObject(pay_info));
            } else {
                logger.error("微信支付接口返回状态失败......" + JSON.toJSONString(response) + "...reason");
                throw new BusinessCheckException("支付下单失败：" + response.get("resp_desc"));
            }   

        } catch (Exception e) {
            logger.error("汇付支付下单失败", e);
            throw new BusinessCheckException("支付下单异常：" + e.getMessage());
        }
    }


    /**
     * 反扫
     * @param userInfo
     * @param orderInfo
     * @param payAmount
     * @param authCode
     * @param giveAmount
     * @param ip
     * @param platform
     * @return
     * @throws BusinessCheckException
     */
    @Transactional(rollbackFor = Exception.class)
    public ResponseObject createMicroPay(MtUser userInfo, MtOrder orderInfo, Integer payAmount, String authCode, Integer giveAmount, String ip, String platform) throws BusinessCheckException {
        try { 
            V2TradePaymentMicropayRequest request = new V2TradePaymentMicropayRequest();
            
            // 设置请求参数
            request.setReqDate(DateTools.getCurrentDateYYYYMMDD()); // 请求日期
            request.setReqSeqId(SequenceTools.getReqSeqId32()); // 请求流水号
            request.setHuifuId(huifuPayBean.getHuifuId()); // 商户号 
            request.setTransAmt(String.format("%.2f", payAmount / 100.0)); // 交易金额（单位：元）
            request.setAuthCode(authCode);
            
            // 设置订单信息
            request.setGoodsDesc(orderInfo.getOrderSn()); // 商品描述
            Map<String, Object> extendInfoMap = new HashMap<>();       
            // 安全信息
            JSONObject riskDto = new JSONObject();
            // ip地址
            riskDto.put("ip_addr", ip); 
            extendInfoMap.put("risk_check_data",riskDto);  
             
            // 异步通知地址
            extendInfoMap.put("notify_url", "https://hatea.zhijuchina.com/api/clientApi/pay/huifuCallback");
            // 备注
            extendInfoMap.put("remark", orderInfo.getOrderSn());  
            // 分账对象
            extendInfoMap.put("acct_split_bunch", getAcctSplitBunch());
            request.setExtendInfo(extendInfoMap);
            
            // 发送请求
            Map<String, Object> response = BasePayClient.request(request);    
            logger.info("createMicroPay",JSON.toJSONString(response));   
            
            String resp_code = (String) response.get("resp_code");                
            // String trans_stat = (String) response.get("trans_stat"); 

            if (resp_code.equals("********") || resp_code.equals("********")) { 
               
                orderInfo.setHfOrder(JSON.toJSONString(response));
                mtOrderMapper.updateById(orderInfo);
                if(resp_code.equals("********")){
                    String bank_desc = (String) response.get("bank_desc");
                    return new ResponseObject(200, bank_desc != null ? bank_desc: "预支付订单创建成功",response);
                }else{
                    return new ResponseObject(200, "预支付订单创建成功",response);
                }
                
            } else {
                logger.error("汇付支付接口返回状态失败......" + JSON.toJSONString(response) + "...reason");
                throw new BusinessCheckException("支付下单失败：" + response.get("resp_desc"));
            }   

        } catch (Exception e) {
            logger.error("汇付支付下单失败", e);
            throw new BusinessCheckException("支付下单异常：" + e.getMessage());
        }
    }
 
    // 小程序支付，微信支付宝反扫
    @Override
    @Transactional(rollbackFor = Exception.class)
    public ResponseObject createPrepayOrder(MtUser userInfo, MtOrder orderInfo, Integer payAmount, String authCode, Integer giveAmount, String ip, String platform) throws BusinessCheckException {
        
        if(authCode == null || authCode.equals("")){
            return createWechatJsPay(userInfo, orderInfo, payAmount, authCode, giveAmount, ip, platform);
        }else{
            return createMicroPay(userInfo, orderInfo, payAmount, authCode, giveAmount, ip, platform);
        } 
    }
     
    @Override
    public Boolean checkCallBack(Map<String, String> params) throws Exception {
        String respDataStr = params.get("resp_data").toString();    
        String signStr = params.get("sign").toString();        
        
        boolean verifyResult = RsaUtils.verify(respDataStr, huifuPayBean.getRsaPublicKey(), signStr);
        return verifyResult;
    }

    @Override
    public Map<String, String> queryPaidOrder(Integer storeId, String tradeNo, String orderSn) throws BusinessCheckException {        
        try {
            MtOrder orderInfo = mtOrderMapper.findByOrderSn(orderSn);
            if (orderInfo == null) {
                return null;
            }
            JSONObject hfOrder = JSONObject.parseObject(orderInfo.getHfOrder());
            if (hfOrder == null) {
                return null;
            }

            V2TradePaymentScanpayQueryRequest request = new V2TradePaymentScanpayQueryRequest();
            // 原机构请求日期

            String org_req_date = hfOrder.getString("req_date");
            if(StringUtil.isEmpty(org_req_date)){
                org_req_date = hfOrder.getString("org_req_date");
            }
            
            String org_req_seq_id = hfOrder.getString("req_seq_id");
            if(StringUtil.isEmpty(org_req_seq_id)){
                org_req_seq_id = hfOrder.getString("org_req_seq_id");
            }
            

            request.setOrgReqDate(org_req_date);
            request.setHuifuId(huifuPayBean.getHuifuId()); 
            request.setOrgReqSeqId(org_req_seq_id); 

            
            // 3. 发起API调用
            Map<String, Object> response = BasePayClient.request(request); 
            System.out.println("返回数据:" + JSONObject.toJSONString(response));
            String transStat = (String) response.get("trans_stat");
            if(!transStat.equals("S")) {
                return null;
            }

            if(!transStat.equals( hfOrder.getString("trans_stat"))){     
               
                orderInfo.setHfOrder(JSON.toJSONString(response));
                mtOrderMapper.updateById(orderInfo);
            }

            Map<String, String> result = new HashMap<>();
            result.put("out_trans_id", (String) response.get("out_trans_id"));
            result.put("party_order_id",(String) response.get("party_order_id"));
            result.put("org_hf_seq_id",(String) response.get("org_hf_seq_id"));
            result.put("trans_amt",(String) response.get("trans_amt"));
            result.put("pay_amt",(String) response.get("pay_amt"));
            
            return result;
        } catch (Exception e) {
            logger.error("查询汇付订单失败", e);
            throw new BusinessCheckException("查询订单异常：" + e.getMessage());
        }
    }

    @Override
    public Boolean doRefund(Integer storeId, String orderSn, BigDecimal totalAmount, BigDecimal refundAmount, String platform, String ip) throws BusinessCheckException {
        try {
            logger.info("HuifuPayService.doRefund orderSn = {}, totalAmount = {}, refundAmount = {}", orderSn, totalAmount, refundAmount);

            // 参数校验
            if (StringUtil.isEmpty(orderSn)) {
                throw new BusinessCheckException("退款订单号不能为空");
            }
            if (refundAmount.compareTo(totalAmount) > 0) {
                throw new BusinessCheckException("退款金额不能大于总金额");
            }
            if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
                throw new BusinessCheckException("退款金额必须大于0");
            }

            // 查询原订单信息
            MtOrder orderInfo = mtOrderMapper.selectOne(new QueryWrapper<MtOrder>().eq("order_sn", orderSn));
            if (orderInfo == null) {
                throw new BusinessCheckException("未找到原订单信息");
            }

            // 解析原订单的汇付支付信息
            if (StringUtil.isEmpty(orderInfo.getHfOrder())) {
                throw new BusinessCheckException("原订单缺少汇付支付信息，无法退款");
            }

            JSONObject hfOrder = JSON.parseObject(orderInfo.getHfOrder());
            String orgHfSeqId = hfOrder.getString("org_hf_seq_id");
            String orgReqDate = hfOrder.getString("req_date");

            if (StringUtil.isEmpty(orgHfSeqId) || StringUtil.isEmpty(orgReqDate)) {
                throw new BusinessCheckException("原订单汇付支付信息不完整，无法退款");
            }

            // 创建退款请求
            V2TradePaymentScanpayRefundRequest request = new V2TradePaymentScanpayRefundRequest();

            // 设置基本参数
            request.setReqDate(DateTools.getCurrentDateYYYYMMDD()); // 请求日期
            request.setReqSeqId(SequenceTools.getReqSeqId32()); // 请求流水号
            request.setHuifuId(huifuPayBean.getHuifuId()); // 商户号

            // 设置原交易信息
            request.setOrgReqDate(orgReqDate); // 原交易请求日期

            // 设置退款金额（单位：元）
            request.setOrdAmt(String.format("%.2f", refundAmount.doubleValue()));
            
            // 设置扩展信息，包含原交易流水号
            Map<String, Object> extendInfoMap = new HashMap<>();    
            // 安全信息
            JSONObject riskDto = new JSONObject();
            // ip地址
            riskDto.put("ip_addr", ip); 
            extendInfoMap.put("risk_check_data",riskDto);  
               
            // 分账对象
            extendInfoMap.put("acct_split_bunch", getAcctSplitBunch()); 


            extendInfoMap.put("remark", "订单退款-" + orderSn);
            extendInfoMap.put("org_hf_seq_id", orgHfSeqId); // 通过扩展信息传递原交易流水号 
            request.setExtendInfo(extendInfoMap);

            // 发送退款请求
            Map<String, Object> response = BasePayClient.request(request);
            logger.info("HuifuPayService doRefund response: {}", JSON.toJSONString(response));

            // 处理响应结果
            String respCode = (String) response.get("resp_code");
            String respDesc = (String) response.get("resp_desc");

            if (!"********".equals(respCode)) {
                String errorMsg = StringUtil.isNotEmpty(respDesc) ? respDesc : "退款失败";
                logger.error("汇付退款失败，respCode: {}, respDesc: {}", respCode, respDesc);
                throw new BusinessCheckException("汇付退款失败：" + errorMsg);
            }

            // 检查交易状态
            String transStat = (String) response.get("trans_stat");
            if (!"S".equals(transStat)) {
                String errorMsg = "退款交易状态异常：" + transStat;
                logger.error("汇付退款交易状态异常，transStat: {}", transStat);
                throw new BusinessCheckException(errorMsg);
            }

            logger.info("汇付退款成功，orderSn: {}, refundAmount: {}", orderSn, refundAmount);
            return true;

        } catch (BusinessCheckException e) {
            throw e;
        } catch (Exception e) {
            logger.error("汇付退款异常，orderSn: {}, error: {}", orderSn, e.getMessage(), e);
            throw new BusinessCheckException("退款处理异常：" + e.getMessage());
        }
    }

    @Override
    public String getFaPiaoQrCode(String orderSn) throws BusinessCheckException {
        UserOrderDto orderInfo = orderService.getOrderByOrderSn(orderSn);
        if (orderInfo == null) {
            return null;
        }
        if(orderInfo.getHfOrder() == null || orderInfo.getHfOrder().equals("")){
            return null;
        }

        // 先检查数据库中是否已有发票URL缓存
        MtOrder mtOrder = mtOrderMapper.findByOrderSn(orderSn);
        if (mtOrder != null && StringUtil.isNotEmpty(mtOrder.getInvoiceUrl())) {
            logger.info("从缓存获取发票二维码URL: {}", mtOrder.getInvoiceUrl());
            return mtOrder.getInvoiceUrl();
        }

        Map<String, Object> params = new HashMap<>();

        params.put("req_date", DateTools.getCurrentDateYYYYMMDD());
        params.put("req_seq_id", SequenceTools.getReqSeqId32());
        params.put("huifu_id", huifuPayBean.getHuifuId());
        params.put("ivc_type", "1");
        params.put("open_type", "0");
        params.put("order_amt", String.format("%.2f", orderInfo.getPayAmount().floatValue() ));

        JSONArray goods_infos = new JSONArray();

       /*  Integer goodsCount = orderInfo.getGoods().stream()
                .mapToInt(goods -> goods.getNum())
                .sum();
        
        // 计算总折扣金额
        BigDecimal totalDiscount = orderInfo.getCouponInfo() !=null ? orderInfo.getCouponInfo().getAmount() : BigDecimal.ZERO;
        // 计算每件商品平均折扣金额
        BigDecimal avgDiscountPerItem = totalDiscount.divide(new BigDecimal(goodsCount), 2, BigDecimal.ROUND_DOWN);
        // 计算可能的余数
        BigDecimal remainingDiscount = totalDiscount.subtract(avgDiscountPerItem.multiply(new BigDecimal(goodsCount)));
        
        // 记录最后一个商品的索引，用于处理余数
        int lastItemIndex = orderInfo.getGoods().size() - 1;
        final int[] currentIndex = {0};
        
        orderInfo.getGoods().forEach(goods -> {
            JSONObject goods_info = new JSONObject();
            goods_info.put("ivc_nature", "0");
            goods_info.put("goods_code", "3070401********0000");
            goods_info.put("goods_name", goods.getName());
            goods_info.put("tax_rate", "0.01");
            goods_info.put("is_price_con_tax", "1");
            
            BigDecimal originalPrice = new BigDecimal(goods.getPrice());
            BigDecimal discountedPrice;
            
            if (currentIndex[0] == lastItemIndex && remainingDiscount.compareTo(BigDecimal.ZERO) > 0) {
                // 最后一个商品，处理余数
                BigDecimal totalItemDiscount = avgDiscountPerItem.add(remainingDiscount.divide(new BigDecimal(goods.getNum()), 2, BigDecimal.ROUND_HALF_UP));
                discountedPrice = originalPrice.subtract(totalItemDiscount);
            } else {
                // 其他商品，应用平均折扣
                discountedPrice = originalPrice.subtract(avgDiscountPerItem);
            }
            
            // 确保价格不为负
            if (discountedPrice.compareTo(BigDecimal.ZERO) < 0) {
                discountedPrice = BigDecimal.ZERO;
            }
            
            goods_info.put("goods_count", goods.getNum());
            goods_info.put("goods_price", String.format("%.2f", discountedPrice.floatValue()));
            goods_info.put("trans_amt", String.format("%.2f", discountedPrice.multiply(new BigDecimal(goods.getNum())).floatValue()));
    
            goods_infos.add(goods_info);
            currentIndex[0]++;
        }); */
        JSONObject goods_info = new JSONObject();
        goods_info.put("ivc_nature", "0");
        goods_info.put("goods_code", "3070401********0000");
        goods_info.put("goods_name", "餐费");
        goods_info.put("tax_rate", "0.01");
        goods_info.put("is_price_con_tax", "1");
        goods_info.put("goods_count", "1");
        goods_info.put("goods_price", String.format("%.2f", orderInfo.getPayAmount().floatValue() ));
        goods_info.put("trans_amt", String.format("%.2f", orderInfo.getPayAmount().floatValue() ));
        goods_infos.add(goods_info); 


        params.put("goods_infos", goods_infos.toString());

        try {
            Map<String, Object> response =  BasePayRequest.requestBasePay("v2/invoice/selfscanopen", params); 
            System.out.println("返回数据:" + JSONObject.toJSONString(response));

            String resp_code = (String) response.get("resp_code");
            if (resp_code.equals("********")) {
                String qr_code_url = (String) response.get("qr_code_url");
                logger.info("开票二维码URL:" + qr_code_url);

                // 保存发票URL到数据库缓存
                if (mtOrder != null && StringUtil.isNotEmpty(qr_code_url)) {
                    mtOrder.setInvoiceUrl(qr_code_url);
                    mtOrderMapper.updateById(mtOrder);
                    logger.info("发票二维码URL已保存到数据库缓存: {}", qr_code_url);
                }

                return qr_code_url;
            } else {
                throw new BusinessCheckException("获取发票失败：" + response.get("resp_desc"));
            }
        } catch (Exception e) {
            logger.error("获取发票失败", e);
            return null;
            // throw new BusinessCheckException("Failed to get FaPiao QR Code: " + e.getMessage());
        } 
    }

   @Override
   public Boolean doRefundAsync(Integer storeId, String orderSn, BigDecimal totalAmount, BigDecimal refundAmount, String platform, String ip) throws BusinessCheckException {
       try {
           logger.info("HuifuPayService.doRefundAsync orderSn = {}, totalAmount = {}, refundAmount = {}", orderSn, totalAmount, refundAmount);

           // 参数校验
           if (StringUtil.isEmpty(orderSn)) {
               throw new BusinessCheckException("退款订单号不能为空");
           }
           if (refundAmount.compareTo(totalAmount) > 0) {
               throw new BusinessCheckException("退款金额不能大于总金额");
           }
           if (refundAmount.compareTo(BigDecimal.ZERO) <= 0) {
               throw new BusinessCheckException("退款金额必须大于0");
           }

           // 查询原订单信息
           MtOrder orderInfo = mtOrderMapper.selectOne(new QueryWrapper<MtOrder>().eq("order_sn", orderSn));
           if (orderInfo == null) {
               throw new BusinessCheckException("未找到原订单信息");
           }

           // 解析原订单的汇付支付信息
           if (StringUtil.isEmpty(orderInfo.getHfOrder())) {
               throw new BusinessCheckException("原订单缺少汇付支付信息，无法退款");
           }

           JSONObject hfOrder = JSON.parseObject(orderInfo.getHfOrder());
           String orgHfSeqId = hfOrder.getString("org_hf_seq_id");
           String orgReqDate = hfOrder.getString("req_date");

           if (StringUtil.isEmpty(orgHfSeqId) || StringUtil.isEmpty(orgReqDate)) {
               throw new BusinessCheckException("原订单汇付支付信息不完整，无法退款");
           }

           // 创建退款请求
           V2TradePaymentScanpayRefundRequest request = new V2TradePaymentScanpayRefundRequest();

           // 设置基本参数
           request.setReqDate(DateTools.getCurrentDateYYYYMMDD()); // 请求日期
           request.setReqSeqId(SequenceTools.getReqSeqId32()); // 请求流水号
           request.setHuifuId(huifuPayBean.getHuifuId()); // 商户号

           // 设置原交易信息
           request.setOrgReqDate(orgReqDate); // 原交易请求日期

           // 设置退款金额（单位：元）
           request.setOrdAmt(String.format("%.2f", refundAmount.doubleValue()));
           
           // 设置扩展信息，包含原交易流水号和异步通知地址
           Map<String, Object> extendInfoMap = new HashMap<>();
           // 安全信息
           JSONObject riskDto = new JSONObject();
           // ip地址
           riskDto.put("ip_addr", ip);
           extendInfoMap.put("risk_check_data",riskDto);
              
           // 分账对象
           extendInfoMap.put("acct_split_bunch", getAcctSplitBunch());

           // 异步通知地址
           extendInfoMap.put("notify_url", "https://hatea.zhijuchina.com/api/clientApi/pay/huifuRefundCallback");
           extendInfoMap.put("remark", "订单退款-" + orderSn);
           extendInfoMap.put("org_hf_seq_id", orgHfSeqId); // 通过扩展信息传递原交易流水号
           request.setExtendInfo(extendInfoMap);

           // 发送退款请求
           Map<String, Object> response = BasePayClient.request(request);
           logger.info("HuifuPayService doRefundAsync response: {}", JSON.toJSONString(response));

           // 处理响应结果
           String respCode = (String) response.get("resp_code");
           String respDesc = (String) response.get("resp_desc");

           if (!"********".equals(respCode) && !"********".equals(respCode)) {
               String errorMsg = StringUtil.isNotEmpty(respDesc) ? respDesc : "退款申请失败";
               logger.error("汇付退款申请失败，respCode: {}, respDesc: {}", respCode, respDesc);
               throw new BusinessCheckException("汇付退款申请失败：" + errorMsg);
           }

           logger.info("汇付退款申请提交成功，等待异步回调，orderSn: {}, refundAmount: {}", orderSn, refundAmount);
           return true;

       } catch (BusinessCheckException e) {
           throw e;
       } catch (Exception e) {
           logger.error("汇付退款申请异常，orderSn: {}, error: {}", orderSn, e.getMessage(), e);
           throw new BusinessCheckException("退款申请异常：" + e.getMessage());
       }
   }
}


