
78f2fee288db93e959a946f4965fb61fa97777d5	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.a48910b608e0a07d09ff.hot-update.js\",\"contentHash\":\"93303a691e3926a2925eeadf83d573d9\"}","integrity":"sha512-y7lF+2sKr2YOT52DK1kDQABTBEfZj+IGvkscSotyoqhH0qGpg0lSmeIJC5EEfyUf7czuI/F9+3lrnShgRIZxrA==","time":1755006150321,"size":34063}