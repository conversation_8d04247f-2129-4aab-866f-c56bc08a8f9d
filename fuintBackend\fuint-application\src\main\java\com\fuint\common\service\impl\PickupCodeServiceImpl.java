package com.fuint.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuint.common.enums.PickupCodeStatusEnum;
import com.fuint.common.service.OrderService;
import com.fuint.common.service.PickupCodeService;
import com.fuint.common.util.DateUtil;
import com.fuint.common.util.RedisUtil;
import com.fuint.repository.mapper.MtPickupCodeMapper;
import com.fuint.repository.model.MtOrder;
import com.fuint.repository.model.MtPickupCode;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 取餐码服务实现类
 */
@Service
public class PickupCodeServiceImpl extends ServiceImpl<MtPickupCodeMapper, MtPickupCode> implements PickupCodeService {
 

    public static final String PICKUP_CODE_KEY = "pickup_code_counter_%d";
    private static final Integer INITIAL_CODE = 1000;

    
    /**
     * 订单服务接口
     * */
    private OrderService orderService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MtPickupCode generateCode(Integer orderId, Integer storeId) {
        // 生成店铺特定的Redis key，加上日期前缀确保每天重置
        String storePickupKey = String.format(PICKUP_CODE_KEY + "_%s", storeId, DateUtil.formatDate(new Date(), "yyyyMMdd"));
         
        Long currentCode = RedisUtil.incr(storePickupKey,1) + INITIAL_CODE; 

        // 创建取餐码记录
        MtPickupCode pickupCode = new MtPickupCode();
        pickupCode.setCode(currentCode.intValue());
        pickupCode.setOrderId(orderId);
        pickupCode.setStoreId(storeId);
        pickupCode.setStatus(PickupCodeStatusEnum.PREPARING.getKey());
        pickupCode.setCreateTime(new Date());
        pickupCode.setUpdateTime(new Date());
        
        this.save(pickupCode);
        return pickupCode;
    }

    @Override
    public boolean updateStatus(Integer orderId, String status) { 
        MtPickupCode pickupCode = findByOrderId(orderId);
        if (pickupCode != null) {
            pickupCode.setStatus(status);
            pickupCode.setUpdateTime(new Date());
            return this.updateById(pickupCode);
        }
        return false;
    }
  
    
    @Override
    public MtPickupCode findByOrderId(Integer orderId){ 
        LambdaQueryWrapper<MtPickupCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MtPickupCode::getOrderId, orderId);
        return this.getOne(queryWrapper);

    }
    @Override
    public MtPickupCode findByOrderSn(String orderSn){
        MtOrder order = orderService.getOrderInfoByOrderSn(orderSn);
        if(order == null){
            return null;
        }
        LambdaQueryWrapper<MtPickupCode> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(MtPickupCode::getOrderId, order.getId());
        return this.getOne(queryWrapper);

    }

    @Override
    public Map<String, List<MtPickupCode>> queryPickupCodeListByStoreId(Integer storeId) {
        Map<String, List<MtPickupCode>> result = new HashMap<>();
        
        // 查询制作中的取餐码
        LambdaQueryWrapper<MtPickupCode> preparingWrapper = new LambdaQueryWrapper<>();
        preparingWrapper.eq(MtPickupCode::getStoreId, storeId);
        
        // 获取当天的开始和结束时间
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date startTime = calendar.getTime();
        
        calendar.set(Calendar.HOUR_OF_DAY, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date endTime = calendar.getTime();
        
        preparingWrapper.ge(MtPickupCode::getCreateTime, startTime);
        preparingWrapper.le(MtPickupCode::getCreateTime, endTime);
        preparingWrapper.eq(MtPickupCode::getStatus, PickupCodeStatusEnum.PREPARING.getKey());
        preparingWrapper.orderByAsc(MtPickupCode::getCreateTime);
        preparingWrapper.last("limit 20");
        List<MtPickupCode> preparingList = this.list(preparingWrapper);
        result.put("preparing", preparingList);

        // 查询已取餐的取餐码
        LambdaQueryWrapper<MtPickupCode> completedWrapper = new LambdaQueryWrapper<>();
        completedWrapper.eq(MtPickupCode::getStoreId, storeId);
        preparingWrapper.ge(MtPickupCode::getCreateTime, startTime);
        preparingWrapper.le(MtPickupCode::getCreateTime, endTime);
        completedWrapper.eq(MtPickupCode::getStatus, PickupCodeStatusEnum.COMPLETED.getKey());
        completedWrapper.orderByDesc(MtPickupCode::getUpdateTime);
        completedWrapper.last("limit 20");
        List<MtPickupCode> completedList = this.list(completedWrapper);
        result.put("completed", completedList);

        return result;
    }
}
