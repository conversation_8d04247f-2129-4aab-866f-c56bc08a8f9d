
48fde0acc41cc5c96496a9d2f3f14680d09058dc	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"ed6297951e80c0b5b814664e70e7dee4\"}","integrity":"sha512-RUkTDYbNZQXw/jvtW5W7PApRyxrmT14E/Kk5YX++aWstCUBpo3VaCVSrUSaWljHg28F+Lw72uSIgVilT56XCMw==","time":1755006104074,"size":2882839}