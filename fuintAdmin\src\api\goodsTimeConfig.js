import request from '@/utils/request'

// 获取商品时间段配置列表
export function getGoodsTimeConfigs(goodsId) {
  return request({
    url: 'backendApi/goods/timeConfig/list/' + goodsId,
    method: 'get'
  })
}

// 保存商品时间段配置
export function saveGoodsTimeConfig(data) {
  return request({
    url: 'backendApi/goods/timeConfig/save',
    method: 'post',
    data: data
  }).catch(error => {
    console.error('保存时间段配置失败:', error)
    throw error
  })
}

// 更新商品时间段配置
export function updateGoodsTimeConfig(data) {
  return request({
    url: 'backendApi/goods/timeConfig/update',
    method: 'post',
    data: data
  }).catch(error => {
    console.error('更新时间段配置失败:', error)
    throw error
  })
}

// 删除商品时间段配置
export function deleteGoodsTimeConfig(id) {
  return request({
    url: 'backendApi/goods/timeConfig/delete/' + id,
    method: 'post'
  }).catch(error => {
    console.error('删除时间段配置失败:', error)
    throw error
  })
}

// 获取商品时间段配置详情
export function getGoodsTimeConfigDetail(id) {
  return request({
    url: 'backendApi/goods/timeConfig/detail/' + id,
    method: 'get'
  }).catch(error => {
    console.error('获取时间段配置详情失败:', error)
    throw error
  })
}
