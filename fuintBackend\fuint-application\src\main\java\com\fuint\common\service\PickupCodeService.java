package com.fuint.common.service;

import com.fuint.repository.model.MtPickupCode;
import java.util.Map;
import java.util.List;

/**
 * 取餐码服务接口
 */
public interface PickupCodeService {
    
    /**
     * 生成取餐码
     *
     * @param orderId 订单ID
     * @return PickupCode
     */
    MtPickupCode generateCode(Integer orderId, Integer storeId);

    /**
     * 更新取餐码状态
     *
     * @param orderId 订单号
     * @param status 状态
     * @return boolean
     */
    boolean updateStatus(Integer orderId, String status);
 
    
    /**
     * 根据订单号查询
     *
     * @param orderId 订单号
     * @return MtPickupCode
     */
    MtPickupCode findByOrderId(Integer orderId);
    
    /**
     * 根据订单号SN查询
     *
     * @param orderSn 订单号Sn
     * @return MtPickupCode
     */
    MtPickupCode findByOrderSn(String orderSn);
    
    /**
     * 根据店铺ID查询取餐码列表
     *
     * @param storeId 店铺ID
     * @return Map<String, List<MtPickupCode>>
     */
    Map<String, List<MtPickupCode>> queryPickupCodeListByStoreId(Integer storeId);
}
