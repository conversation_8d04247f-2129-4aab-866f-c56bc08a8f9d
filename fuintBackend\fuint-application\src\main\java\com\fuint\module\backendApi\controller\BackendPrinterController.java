package com.fuint.module.backendApi.controller;

import com.fuint.common.dto.AccountInfo;
import com.fuint.common.dto.UserOrderDto;
import com.fuint.common.enums.*;
import com.fuint.common.service.OrderService;
import com.fuint.common.service.PrinterService;
import com.fuint.common.service.PrinterCateService;
import com.fuint.common.service.CateService;
import com.fuint.common.service.SettingService;
import com.fuint.common.service.StoreService;
import com.fuint.common.util.TokenUtil;
import com.fuint.framework.web.BaseController;
import com.fuint.framework.web.ResponseObject;
import com.fuint.common.Constants;
import com.fuint.framework.pagination.PaginationRequest;
import com.fuint.framework.pagination.PaginationResponse;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.repository.model.MtPrinter;
import com.fuint.repository.model.MtPrinterCate;
import com.fuint.repository.model.MtGoodsCate;
import com.fuint.repository.model.MtSetting;
import com.fuint.repository.model.MtStore;
import com.fuint.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.text.SimpleDateFormat;

/**
 * 打印机管理类controller
 *

 */
@Api(tags="管理端-打印机相关接口")
@RestController
@AllArgsConstructor
@RequestMapping(value = "/backendApi/printer")
public class BackendPrinterController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BackendPrinterController.class);

    /**
     * 打印机服务接口
     */
    private PrinterService printerService;

    /**
     * 店铺服务接口
     */
    private StoreService storeService;

    /**
     * 配置服务接口
     * */
    private SettingService settingService;

    /**
     * 订单服务接口
     * */
    private OrderService orderService;

    /**
     * 打印机分类关联服务接口
     * */
    private PrinterCateService printerCateService;

    /**
     * 商品分类服务接口
     * */
    private CateService cateService;

    /**
     * 打印机列表查询
     *
     * @param  request HttpServletRequest对象
     * @return 打印机列表
     */
    @ApiOperation(value = "打印机列表查询")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('printer:index')")
    public ResponseObject list(HttpServletRequest request) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        Integer page = request.getParameter("page") == null ? Constants.PAGE_NUMBER : Integer.parseInt(request.getParameter("page"));
        Integer pageSize = request.getParameter("pageSize") == null ? Constants.PAGE_SIZE : Integer.parseInt(request.getParameter("pageSize"));
        String name = request.getParameter("name");
        String sn = request.getParameter("sn");
        String status = request.getParameter("status");
        String searchStoreId = request.getParameter("storeId");

        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        Integer storeId;
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        } else {
            storeId = accountInfo.getStoreId();
        }

        PaginationRequest paginationRequest = new PaginationRequest();
        paginationRequest.setCurrentPage(page);
        paginationRequest.setPageSize(pageSize);

        Map<String, Object> params = new HashMap<>();
        if (accountInfo.getMerchantId() != null && accountInfo.getMerchantId() > 0) {
            params.put("merchantId", accountInfo.getMerchantId());
        }
        if (StringUtil.isNotEmpty(name)) {
            params.put("name", name);
        }
        if (StringUtil.isNotEmpty(sn)) {
            params.put("sn", sn);
        }
        if (StringUtil.isNotEmpty(status)) {
            params.put("status", status);
        }
        if (StringUtil.isNotEmpty(searchStoreId)) {
            params.put("storeId", searchStoreId);
        }
        if (storeId != null && storeId > 0) {
            params.put("storeId", storeId);
        }
        paginationRequest.setSearchParams(params);
        PaginationResponse<MtPrinter> paginationResponse = printerService.queryPrinterListByPagination(paginationRequest);

        Map<String, Object> paramsStore = new HashMap<>();
        paramsStore.put("status", StatusEnum.ENABLED.getKey());
        if (accountInfo.getStoreId() != null && accountInfo.getStoreId() > 0) {
            paramsStore.put("storeId", accountInfo.getStoreId().toString());
        }
        if (accountInfo.getMerchantId() != null && accountInfo.getMerchantId() > 0) {
            paramsStore.put("merchantId", accountInfo.getMerchantId());
        }
        List<MtStore> storeList = storeService.queryStoresByParams(paramsStore);

        Map<String, Object> result = new HashMap<>();
        result.put("paginationResponse", paginationResponse);
        result.put("storeList", storeList);

        return getSuccessResult(result);
    }

    /**
     * 更新打印机状态
     *
     * @return
     */
    @ApiOperation(value = "更新打印机状态")
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('printer:index')")
    public ResponseObject updateStatus(HttpServletRequest request, @RequestBody Map<String, Object> params) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        String status = params.get("status") != null ? params.get("status").toString() : StatusEnum.ENABLED.getKey();
        Integer id = params.get("id") == null ? 0 : Integer.parseInt(params.get("id").toString());

        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        MtPrinter mtPrinter = printerService.queryPrinterById(id);
        if (mtPrinter == null) {
            return getFailureResult(201);
        }

        String operator = accountInfo.getAccountName();
        mtPrinter.setOperator(operator);
        mtPrinter.setStatus(status);
        printerService.updatePrinter(mtPrinter);

        return getSuccessResult(true);
    }

    /**
     * 保存打印机
     *
     * @param request HttpServletRequest对象
     * @return
     */
    @ApiOperation(value = "保存打印机")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('printer:index')")
    public ResponseObject saveHandler(HttpServletRequest request, @RequestBody Map<String, Object> params) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        String id = params.get("id") == null ? "" : params.get("id").toString();
        String status = params.get("status") == null ? "" : params.get("status").toString();
        String storeId = params.get("storeId") == null ? "0" : params.get("storeId").toString();
        String name = params.get("name") == null ? "" : params.get("name").toString();
        String sn = params.get("sn") == null ? "" : params.get("sn").toString();
        String description = params.get("description") == null ? "" : params.get("description").toString();
        String autoPrint = params.get("autoPrint") == null ? YesOrNoEnum.NO.getKey() : params.get("autoPrint").toString();
        String beforePay = params.get("beforePay") == null ? YesOrNoEnum.YES.getKey() : params.get("beforePay").toString();
        String afterPay = params.get("afterPay") == null ? YesOrNoEnum.NO.getKey() : params.get("afterPay").toString();
        String type = params.get("type") == null ? PrinterTypeEnum.RECEIPT.getKey() : params.get("type").toString();

        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        MtPrinter mtPrinter = new MtPrinter();
        mtPrinter.setOperator(accountInfo.getAccountName());
        mtPrinter.setStatus(status);
        mtPrinter.setStoreId(Integer.parseInt(storeId));
        mtPrinter.setName(name);
        mtPrinter.setSn(sn);
        mtPrinter.setAutoPrint(autoPrint);
        mtPrinter.setBeforePay(beforePay);
        mtPrinter.setAfterPay(afterPay);
        mtPrinter.setType(type);
        mtPrinter.setDescription(description);
        mtPrinter.setMerchantId(accountInfo.getMerchantId());
        if (StringUtil.isNotEmpty(id)) {
            mtPrinter.setId(Integer.parseInt(id));
            printerService.updatePrinter(mtPrinter);
        } else {
            printerService.addPrinter(mtPrinter);
        }

        return getSuccessResult(true);
    }

    /**
     * 获取打印机详情
     *
     * @param id
     * @return
     */
    @ApiOperation(value = "获取打印机详情")
    @RequestMapping(value = "/info/{id}", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('printer:index')")
    public ResponseObject info(HttpServletRequest request, @PathVariable("id") Integer id) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        MtPrinter printerInfo = printerService.queryPrinterById(id);

        // 获取打印机关联的分类信息
        Map<String, Object> cateParams = new HashMap<>();
        cateParams.put("printerId", id);
        cateParams.put("merchantId", accountInfo.getMerchantId());
        cateParams.put("storeId", accountInfo.getStoreId());
        List<MtPrinterCate> printerCates = printerCateService.queryPrinterCateListByParams(cateParams);

        Map<String, Object> result = new HashMap<>();
        result.put("printerInfo", printerInfo);
        result.put("printerCates", printerCates);

        return getSuccessResult(result);
    }

    /**
     * 获取打印设置
     *
     * @param request
     * @return
     */
    @ApiOperation(value = "获取打印设置")
    @RequestMapping(value = "/setting", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('printer:setting')")
    public ResponseObject setting(HttpServletRequest request) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        List<MtSetting> settingList = settingService.getSettingList(accountInfo.getMerchantId(), SettingTypeEnum.PRINTER.getKey());

        String userName = "";
        String userKey = "";
        String enable = "";
        for (MtSetting setting : settingList) {
            if (StringUtil.isNotEmpty(setting.getValue())) {
                if (setting.getName().equals(PrinterSettingEnum.USER_NAME.getKey())) {
                    userName = setting.getValue();
                } else if (setting.getName().equals(PrinterSettingEnum.USER_KEY.getKey())) {
                    userKey = setting.getValue();
                } else if (setting.getName().equals(PrinterSettingEnum.ENABLE.getKey())) {
                    enable = setting.getValue();
                }
            }
        }

        Map<String, Object> result = new HashMap<>();
        result.put("userName", userName);
        result.put("userKey", userKey);
        result.put("enable", enable);

        return getSuccessResult(result);
    }

    /**
     * 保存打印设置
     *
     * @param request HttpServletRequest对象
     * @return
     */
    @ApiOperation(value = "保存打印设置")
    @RequestMapping(value = "/saveSetting", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('printer:setting')")
    public ResponseObject saveSetting(HttpServletRequest request, @RequestBody Map<String, Object> param) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        String userName = param.get("userName") != null ? param.get("userName").toString() : null;
        String userKey = param.get("userKey") != null ? param.get("userKey").toString() : null;
        String enable = param.get("enable") != null ? param.get("enable").toString() : null;

        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        PrinterSettingEnum[] settingList = PrinterSettingEnum.values();
        for (PrinterSettingEnum setting : settingList) {
            MtSetting mtSetting = new MtSetting();
            mtSetting.setType(SettingTypeEnum.PRINTER.getKey());
            mtSetting.setName(setting.getKey());
            if (setting.getKey().equals(PrinterSettingEnum.USER_NAME.getKey())) {
                mtSetting.setValue(userName);
            } else if (setting.getKey().equals(PrinterSettingEnum.USER_KEY.getKey())) {
                mtSetting.setValue(userKey);
            } else if (setting.getKey().equals(PrinterSettingEnum.ENABLE.getKey())) {
                mtSetting.setValue(enable);
            }
            mtSetting.setDescription(setting.getValue());
            mtSetting.setOperator(accountInfo.getAccountName());
            mtSetting.setUpdateTime(new Date());
            mtSetting.setMerchantId(accountInfo.getMerchantId());
            mtSetting.setStoreId(0);
            settingService.saveSetting(mtSetting);
        }

        return getSuccessResult(true);
    }

    /**
     * 打印订单
     *
     * @param orderId
     * @return
     */
    @ApiOperation(value = "打印订单")
    @RequestMapping(value = "/doPrint/{orderId}", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('order:index')")
    public ResponseObject doPrint(HttpServletRequest request, @PathVariable("orderId") Integer orderId) throws Exception {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        UserOrderDto orderInfo = orderService.getOrderById(orderId);
        if (orderInfo == null) {
            return getFailureResult(201, "该订单不存在");
        }

        // 打印订单（仅小票，不打印标签）
        Boolean result = printerService.printOrder(orderInfo, true, false, true, null, false);

        return getSuccessResult(result);
    }

    /**
     * 打印订单标签
     *
     * @param orderId
     * @return
     */
    @ApiOperation(value = "打印订单标签")
    @RequestMapping(value = "/printOrderLabel/{orderId}", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('order:index')")
    public ResponseObject printOrderLabel(HttpServletRequest request, @PathVariable("orderId") Integer orderId) throws Exception {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        UserOrderDto orderInfo = orderService.getOrderById(orderId);
        if (orderInfo == null) {
            return getFailureResult(201, "该订单不存在");
        }

        // 打印订单标签
        Boolean result = printerService.printOrderLabel(orderInfo);

        return getSuccessResult(result);
    }

    /**
     * 打印有效期标签
     *
     * @param request HttpServletRequest对象
     * @return
     */
    @ApiOperation(value = "打印有效期标签")
    @RequestMapping(value = "/printExpiryLabel", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('printer:index')")
    public ResponseObject printExpiryLabel(HttpServletRequest request, @RequestBody Map<String, Object> params) throws Exception {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        String name = params.get("name") == null ? "" : params.get("name").toString();
        String productDate = params.get("productDate") == null ? "" : params.get("productDate").toString();
        String expiryDate = params.get("expireDate") == null ? "" : params.get("expireDate").toString();
        String remark = params.get("remark") == null ? "" : params.get("remark").toString();
        Integer id = params.get("id") == null ? 0 : Integer.parseInt(params.get("id").toString());
        
        if (StringUtil.isEmpty(name) || StringUtil.isEmpty(productDate) || StringUtil.isEmpty(expiryDate)) {
            return getFailureResult(201, "参数不能为空");
        }

        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        Date pDate = sdf.parse(productDate);
        Date eDate = sdf.parse(expiryDate);

        Boolean result = printerService.printExpiryLabel(name, pDate, eDate, remark, id, accountInfo.getMerchantId());

        return getSuccessResult(result);
    }

    /**
     * 获取商品分类列表
     *
     * @param request HttpServletRequest对象
     * @param storeId 店铺ID（可选，如果不传则使用当前用户所属店铺）
     * @return 商品分类列表
     */
    @ApiOperation(value = "获取商品分类列表")
    @RequestMapping(value = "/categories", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('printer:index')")
    public ResponseObject getCategories(HttpServletRequest request, @RequestParam(required = false) Integer storeId) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("merchantId", accountInfo.getMerchantId());
        params.put("status", "A");

        // 如果传递了storeId参数，使用传递的storeId；否则使用当前用户的storeId
        Integer targetStoreId = storeId != null ? storeId : accountInfo.getStoreId();
        if (targetStoreId != null && targetStoreId > 0) {
            params.put("storeId", targetStoreId);
        }
        // 如果storeId为null或0，则不添加storeId条件，这样会查询所有该商户的分类 
        List<MtGoodsCate> categories = cateService.queryCateListByParams(params);
 

        return getSuccessResult(categories);
    }

    /**
     * 保存打印机分类关联
     *
     * @param request HttpServletRequest对象
     * @param params 请求参数
     * @return 保存结果
     */
    @ApiOperation(value = "保存打印机分类关联")
    @RequestMapping(value = "/savePrinterCategories", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('printer:index')")
    public ResponseObject savePrinterCategories(HttpServletRequest request, @RequestBody Map<String, Object> params) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Integer printerId = params.get("printerId") != null ? Integer.parseInt(params.get("printerId").toString()) : null;
        List<Integer> cateIds = (List<Integer>) params.get("cateIds");

        if (printerId == null) {
            return getFailureResult(3008, "打印机ID不能为空");
        }

        printerCateService.savePrinterCateRelations(printerId, cateIds, accountInfo.getMerchantId(), accountInfo.getStoreId(), accountInfo.getAccountName());

        return getSuccessResult(true);
    }

    /**
     * 获取打印机分类关联列表
     *
     * @param request HttpServletRequest对象
     * @param printerId 打印机ID
     * @return 分类关联列表
     */
    @ApiOperation(value = "获取打印机分类关联列表")
    @RequestMapping(value = "/printerCategories/{printerId}", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('printer:index')")
    public ResponseObject getPrinterCategories(HttpServletRequest request, @PathVariable Integer printerId) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("printerId", printerId);
        params.put("merchantId", accountInfo.getMerchantId());
        params.put("storeId", accountInfo.getStoreId());

        List<MtPrinterCate> printerCates = printerCateService.queryPrinterCateListByParams(params);

        return getSuccessResult(printerCates);
    }
}
