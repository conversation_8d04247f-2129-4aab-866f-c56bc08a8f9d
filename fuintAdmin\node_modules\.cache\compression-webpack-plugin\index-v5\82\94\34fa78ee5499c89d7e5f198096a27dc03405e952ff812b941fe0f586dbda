
922fb2404ea8bfbf0b8517b54a87140ad573616e	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"1d3bac6135ad1e62eab7a6dfc6a55044\"}","integrity":"sha512-dlSVWURJYY8WeKoQ2OE6uWsSfq89onRt/ATCCq886pv+ljfCidZlqNnM6fYIM/HOe/FEWdjQOJCH1fMJDNX1rQ==","time":1755006086216,"size":6654619}