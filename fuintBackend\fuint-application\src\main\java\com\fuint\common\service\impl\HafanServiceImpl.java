package com.fuint.common.service.impl;

import com.fuint.common.service.HafanService;
import com.fuint.common.util.HttpUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import java.util.*;
import org.apache.commons.lang.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import org.springframework.util.DigestUtils;

/**
 * 哈帆支付接口实现类
 */
@Service
public class HafanServiceImpl implements HafanService {
    private static final Logger logger = LoggerFactory.getLogger(HafanServiceImpl.class);

    @Value("${hafan.base.url}")
    private String baseUrl;

    @Value("${hafan.app.id}")
    private String appId;

    @Value("${hafan.device.id}")
    private String deviceId;

    @Value("${hafan.secret.key}")
    private String secretKey;

    /**
     * 生成签名
     */
    private String generateSign(Map<String, Object> params) {
        // 1. 按照key的字符升序排序
        TreeMap<String, Object> sortedParams = new TreeMap<>(params);
        
        // 2. 组合成key1=value1&key2=value2的形式
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, Object> entry : sortedParams.entrySet()) {
            if (entry.getValue() != null && StringUtils.isNotEmpty(entry.getValue().toString())) {
                if (sb.length() > 0) {
                    sb.append("&");
                }
                sb.append(entry.getKey()).append("=").append(entry.getValue());
            }
        }
        
        // 3. 加上secretKey并进行MD5加密
        String signStr = sb.toString() + secretKey;
        return DigestUtils.md5DigestAsHex(signStr.getBytes()).toLowerCase();
    }

    /**
     * 发送HTTP请求
     */
    private Map<String, Object> sendRequest(String apiPath, Map<String, Object> bizParams) throws Exception {
        // 1. 构建通用参数
        Map<String, Object> params = new HashMap<>();
        params.put("deviceId", deviceId);
        params.put("now", System.currentTimeMillis());
        params.put("appId", appId);
        
        // 2. 添加业务参数
        if (bizParams != null) {
            params.putAll(bizParams);
        }
        
        // 3. 生成签名
        String sign = generateSign(params);
        params.put("sign", sign);

        // 4. 发送请求
        String url = baseUrl + apiPath;
        String result = HttpUtil.doPost(url, JSON.toJSONString(params));
        
        logger.info("Hafan API request: {}, params: {}, response: {}", url, JSON.toJSONString(params), result);
        // 5. 解析响应
        JSONObject response = JSON.parseObject(result);
        if (response.getInteger("code") != 0) {
            throw new Exception(response.getString("message"));
        }
        
        return response;
    }

    @Override
    public Map<String, Object> createOrder(String phone, Integer realAmount) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("phone", phone);
        params.put("realAmount", realAmount);
        
        return sendRequest("/openapi/createOrder", params);
    }

    @Override
    public boolean cancelOrder(String orderNo) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("orderNo", orderNo);
        
        Map<String, Object> response = sendRequest("/openapi/cancelOrder", params);
        return response.get("code") != null && response.get("code").equals(0);
    }

    @Override
    public boolean completeOrder(String orderNo) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("orderNo", orderNo);
        
        Map<String, Object> response = sendRequest("/openapi/completeOrder", params);
        return response.get("code") != null && response.get("code").equals(0);
    }

    @Override
    public Map<String, Object> getUserInfo(String phone) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("phone", phone);
        
        return sendRequest("/openapi/user", params);
    }

    @Override
    public Map<String, Object> getUserList(Map<String, Object> params, Integer start, Integer limit) throws Exception {
        Map<String, Object> queryParams = new HashMap<>(params);
        queryParams.put("_start", start);
        queryParams.put("_limit", limit);
        
        return sendRequest("/openapi/users", queryParams);
    }

    @Override
    public Map<String, Object> getOrderList(Map<String, Object> params, Integer start, Integer limit) throws Exception {
        Map<String, Object> queryParams = new HashMap<>(params);
        queryParams.put("_start", start);
        queryParams.put("_limit", limit);
        
        return sendRequest("/openapi/orders", queryParams);
    }

    @Override
    public Map<String, Object> getOrderInfo(String orderNo) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("orderNo", orderNo);
        
        return sendRequest("/openapi/order", params);
    }

    @Override
    @SuppressWarnings("unchecked")
    public List<Map<String, Object>> getUserCoupons(String phone) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("phone", phone);
        
        Map<String, Object> response = sendRequest("/openapi/userCoupons", params);
        return (List<Map<String, Object>>) response.get("data");
    }

    @Override
    public boolean updateCouponStatus(String phone, Integer couponId, String status) throws Exception {
        Map<String, Object> params = new HashMap<>();
        params.put("phone", phone);
        params.put("couponId", couponId);
        params.put("status", status);
        
        Map<String, Object> response = sendRequest("/openapi/updateCoupon", params);
        return response.get("code") != null && response.get("code").equals(0);
    }
}