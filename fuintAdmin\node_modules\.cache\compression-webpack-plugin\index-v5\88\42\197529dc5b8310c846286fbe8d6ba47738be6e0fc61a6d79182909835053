
a14ff80c98bf946c53b5bc0033273c75c109225d	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"d713c04668a5f02abf479220bf61e9e1\"}","integrity":"sha512-5WSSwEU04SdNF+M/0C+BYhCJ31dVkhc/VMuMKVWIEoA4+gBs7gsARm8ZmwRTA0lLWdXpPG/8QjqEBnq5reCmEg==","time":1755006112015,"size":2882594}