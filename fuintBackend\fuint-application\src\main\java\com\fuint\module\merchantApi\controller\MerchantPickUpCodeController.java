package com.fuint.module.merchantApi.controller;

import com.fuint.common.service.PickupCodeService;
import com.fuint.framework.web.BaseController;
import com.fuint.framework.web.ResponseObject;
import com.fuint.repository.model.MtPickupCode;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 商户端取餐码管理
 */
@Api(tags="商户端-取餐码相关接口")
@RestController
@AllArgsConstructor
@RequestMapping(value = "/merchantApi/pickupCode")
public class MerchantPickUpCodeController extends BaseController {

    /**
     * 取餐码服务接口
     */
    private PickupCodeService pickupCodeService;

    /**
     * 查询店铺取餐码列表
     *
     * @param storeId 店铺ID
     * @return
     */
    @ApiOperation(value = "查询店铺取餐码列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @CrossOrigin
    public ResponseObject list(@RequestParam("storeId") Integer storeId) {
        if (storeId == null || storeId <= 0) {
            return getFailureResult(1002, "未指定店铺");
        }

        Map<String, List<MtPickupCode>> data = pickupCodeService.queryPickupCodeListByStoreId(storeId);
        Map<String, Object> result = new HashMap<>();
        result.put("preparingList", data.get("preparing"));
        result.put("completedList", data.get("completed"));

        return getSuccessResult(result);
    }

}
