
7a9b271be67f630883d729bd54ce3eba2516880b	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"f5553916f5ced1ed2dae5056ab18a8ef\"}","integrity":"sha512-CghOA165xKZe6ZVrggMzG/1A9k5dywyQCK54R+xhX/bdb/c9n2R7b+UMIVm+PUr7zLqtTUspPHze/XmnWvnp4A==","time":1755006150739,"size":6676433}