<!DOCTYPE html>
<html>
<head>
    <meta charset='utf-8' />
    <meta name='viewport' content='initial-scale=1, maximum-scale=1'>
    <meta http-equiv='Cache-Control' content='no-siteapp' />
    <meta name='keywords' content='' />
    <meta name='description' content='' />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />
    <title>BLE WebView</title>
    <script>
        document.addEventListener('bleDataReceived', (event) => {
            const data = event.detail;
            console.log('Received BLE data:', data);
            document.getElementById('output').textContent += `Data: ${data} \n`;
        });
    </script>
</head>
<body>
<h1>BLE WebView Example</h1>
<p id="output">Waiting for data...</p>
</body>
</html>
