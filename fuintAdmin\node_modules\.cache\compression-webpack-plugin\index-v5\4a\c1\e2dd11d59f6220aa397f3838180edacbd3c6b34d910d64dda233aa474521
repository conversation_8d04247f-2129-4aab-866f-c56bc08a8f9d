
201819067cad5bfa2dd472d82fb440f39ec3ccbf	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.1d2f0366475fce218439.hot-update.js\",\"contentHash\":\"4ca24c6b7a4a4b0338d54e859022a5b9\"}","integrity":"sha512-sWk4ZoiEPuvT52pmF3gDftFiPyK5jWKkcLg5YdhB4X/veGbGgKkHx5TqsMpyLSsfwEkPK45uiJDF4UTj4Pn5cA==","time":1755006085815,"size":6100}