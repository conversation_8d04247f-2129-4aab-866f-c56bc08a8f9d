
f050ac0ac39416d09fb6b60aad30cea0f3c5d162	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"df0abf8f50f4a93ac83b17ec1fea6b00\"}","integrity":"sha512-GfLFoc/E1YB/kelba8OhI4YPKXqL90JJAWJ3K+atOgm2bg485ldMWsHlVBR2V/v1ALfv+BsfpKn6WbvmF+ltUQ==","time":1755006276265,"size":6673472}