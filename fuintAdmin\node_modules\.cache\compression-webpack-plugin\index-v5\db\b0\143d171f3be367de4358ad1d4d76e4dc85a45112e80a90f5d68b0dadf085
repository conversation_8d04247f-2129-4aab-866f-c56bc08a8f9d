
bcddfa9f6689d8834dfb9e95b56f4b5f89eb46ea	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.e8cb458ff6d7ca232182.hot-update.js\",\"contentHash\":\"d1b4a80d1df9dd77833a8879c4fde6c3\"}","integrity":"sha512-IMIHOg5iV2mhAaJyoMGJDv5Gda1MbI0yT3sLaTVNn7IbxAs4BISkR41hdJqNx67TM7EDSkDdZxnZIJsW16r6sw==","time":1755006111779,"size":6084}