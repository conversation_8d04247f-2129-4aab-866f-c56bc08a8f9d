<template>
  <div class="package-item">
    <div class="package-item-head">
      <el-button type="primary" size="mini" @click="addPackageGroup">添加分组</el-button>
    </div>

    <!-- 分组列表 -->
    <div class="package-group-list">
      <div class="package-group" v-for="(group, groupIndex) in packageGroups" :key="groupIndex">
        <div class="package-group-header">
          <el-input size="small" v-model="group.groupName" placeholder="分组名称" class="group-name-input"></el-input>
          <div class="group-actions">
            <span class="group-sort-label">排序值：</span>
            <el-input-number size="small" v-model="group.groupSort" :min="0" class="group-sort-input"></el-input-number>
            <el-button size="small" type="danger" @click="removeGroup(groupIndex)">删除分组</el-button>
          </div>
        </div>

        <!-- 添加商品按钮 -->
        <div class="package-item-add">
          <div class="package-item-buttons">
            <el-button size="small" type="primary" @click="openGoodsSelector(groupIndex, 'R')">添加必选商品</el-button>
            <el-button size="small" type="success" @click="openGoodsSelector(groupIndex, 'O')">添加可选商品</el-button>
          </div>
          <div class="package-item-counts">
            <span v-if="maxRequiredItems > 0" class="count-label">
              必选项：{{ getGroupRequiredItemsCount(group) }}/{{ maxRequiredItems }}
            </span>
            <span v-if="maxOptionalItems > 0" class="count-label">
              可选项：{{ getGroupOptionalItemsCount(group) }}/{{ maxOptionalItems }}
            </span>
          </div>
        </div>

        <!-- 商品列表 -->
        <el-table border :data="group.items">
          <el-table-column label="序号" align="center" width="60">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="商品图片" align="center" width="80">
            <template slot-scope="scope">
              <img v-if="scope.row.itemGoods && scope.row.itemGoods.logo" :src="uploadDomain + scope.row.itemGoods.logo" class="item-img">
              <div v-else class="no-img">无图片</div>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" align="center" prop="itemGoods.name">
            <template slot-scope="scope">
              <span>{{ scope.row.itemGoods ? scope.row.itemGoods.name : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="商品类型" align="center" width="100">
            <template slot-scope="scope">
              <el-tag type="success" v-if="scope.row.itemType === 'R'">必选</el-tag>
              <el-tag type="warning" v-else>可选</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="数量" align="center" width="150">
            <template slot-scope="scope">
              <el-input-number size="small" v-model="scope.row.quantity" :min="1" :max="99"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="额外加价" align="center" width="120">
            <template slot-scope="scope">
              <el-input-number
                v-if="scope.row.itemType === 'O'"
                size="small"
                v-model="scope.row.extraPrice"
                :min="0"
                :precision="2"
                :step="0.1"
              ></el-input-number>
              <span v-else>-</span>
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" width="150">
            <template slot-scope="scope">
              <el-input-number size="small" v-model="scope.row.sort" :min="0"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="120">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="removeItem(groupIndex, scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 商品选择对话框 -->
    <el-dialog title="选择商品" :visible.sync="goodsSelectorVisible" width="800px">
      <div class="goods-selector">
        <div class="goods-selector-search">
          <el-input v-model="goodsSearch.name" placeholder="商品名称" class="search-input"></el-input>
          <!-- <el-select v-model="goodsSearch.type" placeholder="商品类型" clearable class="search-select">
            <el-option v-for="item in typeOptions" :key="item.key" :label="item.name" :value="item.key"></el-option>
          </el-select> -->
          <el-button type="primary" size="small" @click="searchGoods">搜索</el-button>
        </div>

        <el-table
          border
          :data="goodsList"
          @selection-change="handleGoodsSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="商品图片" align="center" width="80">
            <template slot-scope="scope">
              <img v-if="scope.row.logo" :src="uploadDomain + scope.row.logo" class="item-img">
              <div v-else class="no-img">无图片</div>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" align="center" prop="name"></el-table-column>
          <el-table-column label="商品类型" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ getGoodsTypeName(scope.row.type) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="价格" align="center" width="100">
            <template slot-scope="scope">
              <span>¥{{ scope.row.price }}</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="goods-selector-footer">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="goodsSearch.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="goodsSearch.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="goodsTotal">
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="goodsSelectorVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelectGoods">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getGoodsList } from "@/api/goods";
import { getToken } from '@/utils/auth'

export default {
  name: "PackageItem",
  props: {
    // 套餐商品ID
    goodsId: {
      type: [String, Number],
      default: 0
    },
    // 上传文件域
    uploadDomain: {
      type: String,
      default: ''
    },
    // 商品类型选项
    typeOptions: {
      type: Array,
      default: () => []
    },
    // 已有的分组数据
    packageData: {
      type: Array,
      default: () => []
    },
    // 每个分组必选项目最大数量，0表示不限制
    maxRequiredItems: {
      type: Number,
      default: 0
    },
    // 每个分组可选项目最大数量，0表示不限制
    maxOptionalItems: {
      type: Number,
      default: 0
    }
  },
  data() {
    return {
      // 分组列表
      packageGroups: [],
      // 上传头部
      uploadHeader: { 'Access-Token': getToken() },
      // 商品选择器可见性
      goodsSelectorVisible: false,
      // 当前编辑的分组索引
      currentGroupIndex: -1,
      // 当前选择的商品类型（必选或可选）
      currentItemType: 'R',
      // 已选择的商品
      selectedGoods: [],
      // 商品列表
      goodsList: [],
      // 商品总数
      goodsTotal: 0,
      // 商品搜索条件
      goodsSearch: {
        name: '',
        type: 'goods',
        page: 1,
        pageSize: 10
      }
    };
  },
  created() {
    // 如果有传入套餐数据，初始化分组
    if (this.packageData && this.packageData.length > 0) {
      this.initPackageGroups();
    }
  },
  watch: {
    // 监听套餐数据变化
    packageData: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.initPackageGroups();
        }
      },
      deep: true
    }
  },
  methods: {
    // 初始化套餐分组
    initPackageGroups() {
      // 如果传入的是分组格式
      if (this.packageData && this.packageData.length > 0 && this.packageData[0].items) {
        this.packageGroups = this.packageData.map(group => ({
          groupId: group.groupId,
          groupName: group.groupName,
          groupSort: group.groupSort,
          items: group.items || []
        }));
        // 按排序值排序
        this.packageGroups.sort((a, b) => a.groupSort - b.groupSort);
      } else {
        // 将传入的套餐数据按分组整理
        const groupMap = new Map();

        // 按分组ID分组
        this.packageData.forEach(item => {
          if (!groupMap.has(item.groupId)) {
            groupMap.set(item.groupId, {
              groupId: item.groupId,
              groupName: item.groupName,
              groupSort: item.groupSort,
              items: []
            });
          }
          groupMap.get(item.groupId).items.push(item);
        });

        // 转换为数组并按排序值排序
        this.packageGroups = Array.from(groupMap.values());
        this.packageGroups.sort((a, b) => a.groupSort - b.groupSort);
      }
    },
    // 添加套餐分组
    addPackageGroup() {
      const newGroupId = this.generateGroupId();
      this.packageGroups.push({
        groupId: newGroupId,
        groupName: '分组' + (this.packageGroups.length + 1),
        groupSort: this.packageGroups.length,
        items: []
      });
    },
    // 生成分组ID
    generateGroupId() {
      return this.packageGroups.length > 0 ?
        Math.max(...this.packageGroups.map(g => g.groupId)) + 1 : 1;
    },
    // 删除分组
    removeGroup(groupIndex) {
      this.$confirm('确定要删除该分组吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.packageGroups.splice(groupIndex, 1);
        this.$emit('change', this.getPackageData());
      }).catch(() => {});
    },
    // 打开商品选择器
    openGoodsSelector(groupIndex, itemType) {
      this.currentGroupIndex = groupIndex;
      this.currentItemType = itemType;
      this.selectedGoods = [];
      this.goodsSelectorVisible = true;
      this.searchGoods();
    },
    // 搜索商品
    searchGoods() {
      const params = {
        ...this.goodsSearch,
        status: 'A', // 只查询上架的商品
        type: 'goods' // 只查询实物商品
      };

      getGoodsList(params).then(response => {
        const data = response.data;
        this.goodsList = data.paginationResponse.content;
        this.goodsTotal = data.paginationResponse.totalElements;
        this.uploadDomain = data.imagePath;
      });
    },
    // 商品选择变更
    handleGoodsSelectionChange(selection) {
      this.selectedGoods = selection;
    },
    // 确认选择商品
    confirmSelectGoods() {
      if (this.selectedGoods.length === 0) {
        this.$message.warning('请选择至少一个商品');
        return;
      }

      // 获取当前分组
      const currentGroup = this.packageGroups[this.currentGroupIndex];

      // 检查限制
      if (this.currentItemType === 'R' && this.maxRequiredItems > 0) {
        // 计算当前必选项数量
        const currentRequiredCount = currentGroup.items.filter(item => item.itemType === 'R').length;
        // 计算添加后的总数
        const newItemsCount = this.selectedGoods.filter(goods =>
          !currentGroup.items.some(item => item.itemGoodsId === goods.id && item.itemType === 'R')
        ).length;

        if (currentRequiredCount + newItemsCount > this.maxRequiredItems) {
          this.$message.warning(`每个分组最多可添加 ${this.maxRequiredItems} 个必选项`);
          return;
        }
      }

      if (this.currentItemType === 'O' && this.maxOptionalItems > 0) {
        // 计算当前可选项数量
        const currentOptionalCount = currentGroup.items.filter(item => item.itemType === 'O').length;
        // 计算添加后的总数
        const newItemsCount = this.selectedGoods.filter(goods =>
          !currentGroup.items.some(item => item.itemGoodsId === goods.id && item.itemType === 'O')
        ).length;

        if (currentOptionalCount + newItemsCount > this.maxOptionalItems) {
          this.$message.warning(`每个分组最多可添加 ${this.maxOptionalItems} 个可选项`);
          return;
        }
      }

      // 添加商品到分组
      this.selectedGoods.forEach(goods => {
        // 检查是否已存在
        const exists = currentGroup.items.some(item =>
          item.itemGoodsId === goods.id && item.itemType === this.currentItemType
        );

        if (!exists) {
          currentGroup.items.push({
            goodsId: this.goodsId,
            itemGoodsId: goods.id,
            itemGoods: goods,
            quantity: 1,
            itemType: this.currentItemType,
            groupId: currentGroup.groupId,
            groupName: currentGroup.groupName,
            groupSort: currentGroup.groupSort,
            extraPrice: 0,
            sort: 0
          });
        }
      });

      // 关闭对话框
      this.goodsSelectorVisible = false;

      // 通知父组件数据变更
      this.$emit('change', this.getPackageData());
    },
    // 删除商品项
    removeItem(groupIndex, itemIndex) {
      this.packageGroups[groupIndex].items.splice(itemIndex, 1);
      this.$emit('change', this.getPackageData());
    },
    // 获取商品类型名称
    getGoodsTypeName(type) {
      const found = this.typeOptions.find(option => option.key === type);
      return found ? found.name : type;
    },
    // 分页大小变更
    handleSizeChange(val) {
      this.goodsSearch.pageSize = val;
      this.searchGoods();
    },
    // 分页变更
    handleCurrentChange(val) {
      this.goodsSearch.page = val;
      this.searchGoods();
    },
    // 获取套餐数据（供父组件使用）
    // 获取分组必选项目数量
    getGroupRequiredItemsCount(group) {
      return group.items.filter(item => item.itemType === 'R').length;
    },

    // 获取分组可选项目数量
    getGroupOptionalItemsCount(group) {
      return group.items.filter(item => item.itemType === 'O').length;
    },

    getPackageData() {
      // 返回分组结构，每个分组包含items数组
      return this.packageGroups.map(group => ({
        groupId: group.groupId,
        groupName: group.groupName,
        groupSort: group.groupSort,
        items: group.items.map(item => ({
          ...item,
          goodsId: this.goodsId,
          groupId: group.groupId,
          groupName: group.groupName,
          groupSort: group.groupSort
        }))
      }));
    }
  }
};
</script>

<style scoped>
.package-item {
  padding: 10px 0;
}

.package-item-head {
  margin-bottom: 15px;
}

.package-group {
  margin-bottom: 30px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
}

.package-group-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  align-items: center;
}

.group-name-input {
  width: 200px;
}

.group-actions {
  display: flex;
  align-items: center;
}

.group-sort-label {
  margin-right: 5px;
}

.group-sort-input {
  width: 120px;
  margin-right: 15px;
}

.package-item-add {
  margin-bottom: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.package-item-counts {
  display: flex;
  gap: 15px;
}

.count-label {
  background-color: #f0f9eb;
  padding: 3px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: #67c23a;
}

.count-label:first-child {
  background-color: #ecf5ff;
  color: #409eff;
}

.item-img {
  height: 40px;
  width: 40px;
  object-fit: cover;
}

.no-img {
  height: 40px;
  width: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.goods-selector-search {
  display: flex;
  margin-bottom: 15px;
}

.search-input {
  width: 200px;
  margin-right: 10px;
}

.search-select {
  width: 150px;
  margin-right: 10px;
}

.goods-selector-footer {
  margin-top: 15px;
  text-align: center;
}
</style>
