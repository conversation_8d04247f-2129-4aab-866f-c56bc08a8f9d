package com.hatea.cashierapp;
import android.app.AlertDialog;
import android.content.Context;
import android.content.DialogInterface;
import android.util.Log;
import android.webkit.JsPromptResult;
import android.webkit.JsResult;
import android.webkit.WebChromeClient;
import android.webkit.WebView;
import android.widget.EditText;
import android.view.ViewGroup;
import android.view.WindowManager.LayoutParams;

public class CustomWebChromeClient extends WebChromeClient {
    private final Context context;

    public CustomWebChromeClient(Context context) {
        this.context = context;
    }
    @Override
    public boolean onJsBeforeUnload(WebView view, String url, String message, JsResult result) {
        new AlertDialog.Builder(context)
                .setTitle("确定要离开吗")
                .setMessage(message)
                .setPositiveButton("离开", (dialog, which) -> result.confirm())
                .setNegativeButton("留下", (dialog, which) -> result.cancel())
                .setOnCancelListener(dialog -> result.cancel())
                .show();
        return true;
    }

    @Override
    public void onProgressChanged(WebView view, int newProgress) {
        super.onProgressChanged(view, newProgress);
        // 这里可以更新进度条等 UI 元素
        // 例如：progressBar.setProgress(newProgress);
    }

    @Override
    public boolean onConsoleMessage(android.webkit.ConsoleMessage consoleMessage) {
        // 这里可以将控制台消息记录到日志中
        Log.d("WebViewConsole", consoleMessage.message() + " -- From line "
                + consoleMessage.lineNumber() + " of "
                + consoleMessage.sourceId());
        return super.onConsoleMessage(consoleMessage);
    }


    @Override
    public boolean onJsAlert(WebView view, String url, String message, JsResult result) {
        new AlertDialog.Builder(context)
                .setTitle("Alert")
                .setMessage(message)
                .setPositiveButton("确定", (dialog, which) -> result.confirm())
                .setOnCancelListener(dialog -> result.cancel())
                .show();
        return true; // 表示已经处理了 Alert
    }

    @Override
    public boolean onJsConfirm(WebView view, String url, String message, JsResult result) {
        new AlertDialog.Builder(context)
                .setTitle("Confirm")
                .setMessage(message)
                .setPositiveButton("确定", (dialog, which) -> result.confirm())
                .setNegativeButton("取消", (dialog, which) -> result.cancel())
                .setOnCancelListener(dialog -> result.cancel())
                .show();
        return true; // 表示已经处理了 Confirm
    }

    @Override
    public boolean onJsPrompt(WebView view, String url, String message, String defaultValue, JsPromptResult result) {
        final EditText input = new EditText(context);
        input.setText(defaultValue);

        new AlertDialog.Builder(context)
                .setTitle("Prompt")
                .setMessage(message)
                .setView(input)
                .setPositiveButton("确定", (dialog, which) -> {
                    String value = input.getText().toString();
                    result.confirm(value);
                })
                .setNegativeButton("取消", (dialog, which) -> result.cancel())
                .setOnCancelListener(dialog -> result.cancel())
                .show();
        return true; // 表示已经处理了 Prompt
    }
}
