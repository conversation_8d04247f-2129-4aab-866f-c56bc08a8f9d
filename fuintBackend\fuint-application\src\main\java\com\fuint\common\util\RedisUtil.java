package com.fuint.common.util;

import com.fuint.utils.ContextUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.script.DefaultRedisScript;
import org.springframework.data.redis.core.script.RedisScript;
import org.springframework.data.redis.serializer.GenericToStringSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.util.CollectionUtils;
import java.util.Collection;
import java.util.Collections;
import java.util.concurrent.TimeUnit;

/**
 * redis 缓存工具
 *

 */
@Slf4j
public class RedisUtil {

    private static RedisTemplate<String, Object> redisTemplate = ContextUtils.getBean("redisTemplate", RedisTemplate.class);
    

    /**
     * 指定缓存失效时间
     *
     * @param key  键
     * @param time 时间(秒)
     * @return
     */
    public static boolean expire(String key, long time) {
        try {
            if (time > 0) {
                redisTemplate.expire(key, time, TimeUnit.SECONDS);
            }
            return true;
        } catch (Exception e) {
            log.error("设置redis指定key失效时间错误:", e);
            return false;
        }
    }

    /**
     * 根据key 获取过期时间
     *
     * @param key 键 不能为null
     * @return 时间(秒) 返回0代表为永久有效 失效时间为负数，说明该主键未设置失效时间（失效时间默认为-1）
     */
    public static long getExpire(String key) {
        return redisTemplate.getExpire(key, TimeUnit.SECONDS);
    }

    /**
     * 判断key是否存在
     *
     * @param key 键
     * @return true 存在 false 不存在
     */
    public static boolean hasKey(String key) {
        try {
            return redisTemplate.hasKey(key);
        } catch (Exception e) {
            log.error("redis判断key是否存在错误：", e);
            return false;
        }
    }

    /**
     * 普通缓存获取
     *
     * @param key 键
     * @return 值
     */
    @SuppressWarnings("unchecked")
    public static <T> T get(String key) {
        return key == null ? null : (T) redisTemplate.opsForValue().get(key);
    }

    /**
     * 普通缓存放入
     *
     * @param key   键
     * @param value 值
     * @return true成功 false失败
     */
    public static boolean set(String key, Object value) {
        try {
            redisTemplate.opsForValue().set(key, value);
            return true;
        } catch (Exception e) {
            log.error("设置redis缓存错误：", e);
            return false;
        }

    }

    /**
     * 普通缓存放入并设置时间
     *
     * @param key   键
     * @param value 值
     * @param time  时间(秒) time要大于0 如果time小于等于0 将设置无限期
     * @return true成功 false 失败
     */
    public static boolean set(String key, Object value, long time) {
        try {
            if (time > 0) {
                redisTemplate.opsForValue().set(key, value, time, TimeUnit.SECONDS);
            } else {
                set(key, value);
            }
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * 删除缓存
     *
     * @param key 可以传一个值 或多个
     */
    @SuppressWarnings("unchecked")
    public static void remove(String... key) {
        if (key != null && key.length > 0) {
            if (key.length == 1) {
                redisTemplate.delete(key[0]);
            } else {
                redisTemplate.delete((Collection<String>) CollectionUtils.arrayToList(key));
            }
        }
    }

    /**
     * 删除缓存
     *
     * @param key 键
     * @return
     */
    public static boolean remove(String key) {
        try {
            if (key != null && key.length() > 0) {
                redisTemplate.delete(key);
            }
            return true;
        } catch (Exception e) {
            log.error("删除redis的key错误:", e);
            return false;
        }
    }

    /**
     * 删除匹配模式的所有key
     *
     * @param pattern 模式字符串，如：prefix*
     * @return boolean
     */
    public static boolean removePattern(String pattern) {
        try {
            if (pattern != null && pattern.length() > 0) {
                Collection<String> keys = redisTemplate.keys(pattern);
                if (!CollectionUtils.isEmpty(keys)) {
                    redisTemplate.delete(keys);
                }
            }
            return true;
        } catch (Exception e) {
            log.error("删除redis模式key错误:", e);
            return false;
        }
    }

    /**
     * 递增 此时value值必须为int类型 否则报错
     *
     * @param key   键
     * @param delta 要增加几(大于0)
     * @return
     */
    public static long incr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递增因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, delta);
    }

    /**
     * 递减
     *
     * @param key   键
     * @param delta 要减少几(小于0)
     * @return
     */
    public static long decr(String key, long delta) {
        if (delta < 0) {
            throw new RuntimeException("递减因子必须大于0");
        }
        return redisTemplate.opsForValue().increment(key, -delta);
    }

    public static Long incrementWithDefault(String key, long delta, long defaultValue) {
        // 定义 Lua 脚本
        String scriptText = "local key = KEYS[1]\n" +
            "local delta = tonumber(ARGV[1])\n" +
            "local defaultValue = tonumber(ARGV[2])\n" +
            "if redis.call('EXISTS', key) == 0 then\n" +
            "    redis.call('SET', key, defaultValue)\n" +
            "end\n" +
            "return redis.call('INCRBY', key, delta)";

        DefaultRedisScript<Long> script = new DefaultRedisScript<>();
        script.setScriptText(scriptText);
        script.setResultType(Long.class);

        // 执行 Lua 脚本
        return redisTemplate.execute(
                script,
                redisTemplate.getStringSerializer(),
                new GenericToStringSerializer<>(Long.class),  // Use specific serializer for Long
                Collections.singletonList(key),
                String.valueOf(delta),
                String.valueOf(defaultValue)
        );
    }

    /**
     * 递增
     * @param key 键
     * @return 递增后的值
     */
    public static Long incr(String key) {
        try {
            return redisTemplate.opsForValue().increment(key);
        } catch (Exception e) {
            log.error("Redis incr error:", e);
            throw new RuntimeException("Redis increment operation failed", e);
        }
    }

    /**
     * 递增指定步长
     * @param key 键
     * @param delta 步长
     * @return 递增后的值
     */
    public static Long incrBy(String key, long delta) {
        try {
            return redisTemplate.opsForValue().increment(key, delta);
        } catch (Exception e) {
            log.error("Redis incrBy error:", e);
            throw new RuntimeException("Redis increment by operation failed", e);
        }
    }

    /**
     * 递减
     * @param key 键
     * @return 递减后的值
     */
    public static Long decr(String key) {
        try {
            return redisTemplate.opsForValue().decrement(key);
        } catch (Exception e) {
            log.error("Redis decr error:", e);
            throw new RuntimeException("Redis decrement operation failed", e);
        }
    }

    /**
     * 递减指定步长
     * @param key 键
     * @param delta 步长
     * @return 递减后的值
     */
    public static Long decrBy(String key, long delta) {
        try {
            return redisTemplate.opsForValue().decrement(key, delta);
        } catch (Exception e) {
            log.error("Redis decrBy error:", e);
            throw new RuntimeException("Redis decrement by operation failed", e);
        }
    }
}
