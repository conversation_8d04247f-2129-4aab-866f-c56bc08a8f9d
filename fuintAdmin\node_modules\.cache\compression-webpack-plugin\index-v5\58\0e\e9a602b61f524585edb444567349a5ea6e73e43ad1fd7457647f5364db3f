
c88e078df202b7c2880346ad44c82951f483e1db	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"2d23a67c268328de597d331839156a9d\"}","integrity":"sha512-rLMgsjhdj1M6Xa74mA0x53M+AAQclWElOfnTF1gvlLlnHfSSDg8XSTuwUdh++PEQxjbKx/NmvASkxHTNqumzwQ==","time":1755006112201,"size":6654127}