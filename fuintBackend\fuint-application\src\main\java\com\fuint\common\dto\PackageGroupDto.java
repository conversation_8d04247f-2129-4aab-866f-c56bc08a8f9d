package com.fuint.common.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 套餐分组对象
 */
@Getter
@Setter
public class PackageGroupDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 套餐商品ID
     */
    private Integer goodsId;

    /**
     * 分组名称
     */
    private String groupName;

    /**
     * 分组类型: R-必选组, O-可选组
     */
    private String groupType;

    /**
     * 可选组选择数量，必选组固定为1
     */
    private Integer selectCount;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 状态
     */
    private String status;
    
    /**
     * 分组下的商品
     */
    private List<PackageGroupItemDto> items;
}