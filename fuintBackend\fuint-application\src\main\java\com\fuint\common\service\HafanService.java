package com.fuint.common.service;

import java.util.Map;
import java.util.List;

/**
 * 哈帆支付相关业务接口
 */
public interface HafanService {
    /**
     * 创建订单
     *
     * @param phone 手机号
     * @param realAmount 实际支付金额(单位:分)
     * @return Map 订单创建结果
     * @throws Exception
     */
    Map<String, Object> createOrder(String phone, Integer realAmount) throws Exception;

    /**
     * 取消订单
     *
     * @param orderNo 订单号
     * @return boolean 是否取消成功
     * @throws Exception
     */
    boolean cancelOrder(String orderNo) throws Exception;

    /**
     * 完成订单
     *
     * @param orderNo 订单号
     * @return boolean 是否完成成功 
     * @throws Exception
     */
    boolean completeOrder(String orderNo) throws Exception;

    /**
     * 获取会员信息
     *
     * @param phone 手机号
     * @return Map 会员信息
     * @throws Exception
     */
    Map<String, Object> getUserInfo(String phone) throws Exception;

    /**
     * 查询会员列表
     *
     * @param params 查询参数(phone,nickName,premium_level等)
     * @param start 分页开始行
     * @param limit 每页数量
     * @return Map 查询结果
     * @throws Exception
     */
    Map<String, Object> getUserList(Map<String, Object> params, Integer start, Integer limit) throws Exception;

    /**
     * 订单查询
     *
     * @param params 查询参数(phone,status等)
     * @param start 分页开始行
     * @param limit 每页数量
     * @return Map 订单列表
     * @throws Exception
     */
    Map<String, Object> getOrderList(Map<String, Object> params, Integer start, Integer limit) throws Exception;

    /**
     * 获取订单详情
     *
     * @param orderNo 订单号
     * @return Map 订单详情
     * @throws Exception
     */
    Map<String, Object> getOrderInfo(String orderNo) throws Exception;

    /**
     * 获取用户优惠券
     *
     * @param phone 手机号
     * @return List 优惠券列表
     * @throws Exception
     */
    List<Map<String, Object>> getUserCoupons(String phone) throws Exception;

    /**
     * 更新优惠券状态
     *
     * @param phone 手机号
     * @param couponId 优惠券ID
     * @param status 状态(valid,invalid,outDate,used)
     * @return boolean 是否更新成功
     * @throws Exception
     */
    boolean updateCouponStatus(String phone, Integer couponId, String status) throws Exception;
}
