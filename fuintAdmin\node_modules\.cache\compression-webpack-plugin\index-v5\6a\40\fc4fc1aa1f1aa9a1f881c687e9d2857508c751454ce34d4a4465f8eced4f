
663c705eb75f3090e56f8052c2075fe7f6200cd1	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"7e787806824e55743bdbc154552c8003\"}","integrity":"sha512-zbib3YDiGizK9kZPss0Tq84Bz9Alp/oGd6HITHJR/c0FK3N49ObEzX8NIB+dmKPjf+QEoGDBJMwKz7NiGjqRsg==","time":1755006369102,"size":2883143}