package com.fuint.module.backendApi.controller;

import com.fuint.common.service.GoodsService;
import com.fuint.common.util.TokenUtil;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.framework.web.BaseController;
import com.fuint.framework.web.ResponseObject;
import com.fuint.repository.model.MtGoodsTimeConfig;
import com.fuint.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.*;

/**
 * 商品时间段配置管理controller
 *
 * 
 */
@Api(tags="管理端-商品时间段配置相关接口")
@RestController
@AllArgsConstructor
@RequestMapping(value = "/backendApi/goods/timeConfig")
public class BackendGoodsTimeConfigController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(BackendGoodsTimeConfigController.class);

    /**
     * 商品服务接口
     */
    private GoodsService goodsService;

    /**
     * 获取商品时间段配置列表
     *
     * @param request HttpServletRequest对象
     * @param goodsId 商品ID
     * @return
     */
    @ApiOperation(value = "获取商品时间段配置列表")
    @RequestMapping(value = "/list/{goodsId}", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:index')")
    public ResponseObject list(HttpServletRequest request, @PathVariable("goodsId") Integer goodsId) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        com.fuint.common.dto.AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        // 记录日志
        logger.info("获取商品时间段配置列表，商品ID: {}", goodsId);

        List<MtGoodsTimeConfig> timeConfigList = goodsService.getGoodsTimeConfig(goodsId);
        
        Map<String, Object> result = new HashMap<>();
        result.put("data", timeConfigList);
        
        return getSuccessResult(result);
    }

    /**
     * 保存商品时间段配置
     *
     * @param request HttpServletRequest对象
     * @param param 请求参数
     * @return
     */
    @ApiOperation(value = "保存商品时间段配置")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:add')")
    public ResponseObject save(HttpServletRequest request, @RequestBody Map<String, Object> param) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        com.fuint.common.dto.AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Integer goodsId = param.get("goodsId") == null ? 0 : Integer.parseInt(param.get("goodsId").toString());
        String configType = param.get("configType") == null ? "" : param.get("configType").toString();
        String configValue = param.get("configValue") == null ? "" : param.get("configValue").toString();
        String timeRanges = param.get("timeRanges") == null ? "" : param.get("timeRanges").toString();
        String status = param.get("status") == null ? "A" : param.get("status").toString();

        if (goodsId <= 0) {
           return getFailureResult(201, "商品ID不能为空");
       }

       // 记录日志
       logger.info("保存商品时间段配置，商品ID: {}, 配置类型: {}", goodsId, configType);

       List<MtGoodsTimeConfig> timeConfigs = new ArrayList<>();
        
        // 解析时间段配置
        if (StringUtil.isNotEmpty(timeRanges)) {
          try {
            List<Map> ranges = com.alibaba.fastjson.JSONArray.parseArray(timeRanges, Map.class);
            // 验证时间段配置
            for (Map range : ranges) {
              String startTime = (String) range.get("startTime");
              String endTime = (String) range.get("endTime");
              
              // 验证时间格式
              if (StringUtil.isEmpty(startTime) || StringUtil.isEmpty(endTime)) {
                return getFailureResult(201, "时间段配置不完整，请填写开始时间和结束时间");
              }
              
              // 验证时间格式是否正确 (HH:mm)
              if (!startTime.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$") ||
                  !endTime.matches("^([01]?[0-9]|2[0-3]):[0-5][0-9]$")) {
                return getFailureResult(201, "时间段格式错误，请使用HH:mm格式");
              }
              
              // 验证时间格式正确性，允许跨天时间段配置（如22:00-02:00）
              // 不再强制要求开始时间必须小于结束时间
            }
            
            for (Map range : ranges) {
              MtGoodsTimeConfig config = new MtGoodsTimeConfig();
              config.setGoodsId(goodsId);
              config.setConfigType(configType);
              
              // 根据配置类型设置对应的值
              if ("WEEKLY".equals(configType)) {
                // 按周设置，解析星期几
                if (StringUtil.isNotEmpty(configValue)) {
                  List<Integer> weekDays = com.alibaba.fastjson.JSONArray.parseArray(configValue, Integer.class);
                  // 验证星期几是否有效 (1-7)
                  for (Integer weekDay : weekDays) {
                    if (weekDay < 1 || weekDay > 7) {
                      return getFailureResult(201, "星期几必须在1-7之间");
                    }
                  }
                  
                  for (Integer weekDay : weekDays) {
                    MtGoodsTimeConfig weeklyConfig = new MtGoodsTimeConfig();
                    weeklyConfig.setGoodsId(goodsId);
                    weeklyConfig.setConfigType("2"); // 数据库中存储为数字
                    weeklyConfig.setWeekDay(weekDay);
                    weeklyConfig.setStartTime((String) range.get("startTime"));
                    weeklyConfig.setEndTime((String) range.get("endTime"));
                    weeklyConfig.setStatus(status);
                    weeklyConfig.setOperator(accountInfo.getAccountName());
                    timeConfigs.add(weeklyConfig);
                  }
                } else {
                  return getFailureResult(201, "按周设置必须选择星期几");
                }
              } else if ("CUSTOM".equals(configType)) {
                // 自定义日期，解析指定日期
                if (StringUtil.isNotEmpty(configValue)) {
                  List<String> dates = com.alibaba.fastjson.JSONArray.parseArray(configValue, String.class);
                  // 验证日期格式
                  java.text.SimpleDateFormat dateFormat = new java.text.SimpleDateFormat("yyyy-MM-dd");
                  dateFormat.setLenient(false);
                  for (String date : dates) {
                    try {
                      dateFormat.parse(date);
                    } catch (Exception e) {
                      return getFailureResult(201, "日期格式错误，请使用yyyy-MM-dd格式");
                    }
                  }
                  
                  for (String date : dates) {
                    MtGoodsTimeConfig dateConfig = new MtGoodsTimeConfig();
                    dateConfig.setGoodsId(goodsId);
                    dateConfig.setConfigType("3"); // 数据库中存储为数字
                    dateConfig.setSpecifyDate(date);
                    dateConfig.setStartTime((String) range.get("startTime"));
                    dateConfig.setEndTime((String) range.get("endTime"));
                    dateConfig.setStatus(status);
                    dateConfig.setOperator(accountInfo.getAccountName());
                    timeConfigs.add(dateConfig);
                  }
                } else {
                  return getFailureResult(201, "自定义日期必须选择日期");
                }
              } else {
                // 每日通用
                config.setConfigType("1"); // 数据库中存储为数字
                config.setStartTime((String) range.get("startTime"));
                config.setEndTime((String) range.get("endTime"));
                config.setStatus(status);
                config.setOperator(accountInfo.getAccountName());
                timeConfigs.add(config);
              }
            }
          } catch (Exception e) {
            return getFailureResult(201, "时间段配置格式错误：" + e.getMessage());
          }
        }

        // 保存配置
        goodsService.saveGoodsTimeConfig(goodsId, timeConfigs, accountInfo.getAccountName());

        Map<String, Object> result = new HashMap<>();
        return getSuccessResult(result);
    }

    /**
     * 更新商品时间段配置
     *
     * @param request HttpServletRequest对象
     * @param param 请求参数
     * @return
     */
    @ApiOperation(value = "更新商品时间段配置")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:edit')")
    public ResponseObject update(HttpServletRequest request, @RequestBody Map<String, Object> param) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        com.fuint.common.dto.AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Integer id = param.get("id") == null ? 0 : Integer.parseInt(param.get("id").toString());
        String status = param.get("status") == null ? "" : param.get("status").toString();

        if (id <= 0) {
           return getFailureResult(201, "配置ID不能为空");
       }

       // 记录日志
       logger.info("更新商品时间段配置，配置ID: {}, 状态: {}", id, status);

       // 验证状态值
        if (StringUtil.isNotEmpty(status) && !("A".equals(status) || "N".equals(status))) {
            return getFailureResult(201, "状态值无效，只能是A(启用)或N(禁用)");
        }

        // 获取原有的配置
        MtGoodsTimeConfig config = goodsService.getGoodsTimeConfigById(id);
        if (config == null) {
            return getFailureResult(201, "配置不存在");
        }

        // 更新状态
        if (StringUtil.isNotEmpty(status)) {
            config.setStatus(status);
            config.setOperator(accountInfo.getAccountName());
            config.setUpdateTime(new Date());
            goodsService.saveGoodsTimeConfig(config.getGoodsId(), Arrays.asList(config), accountInfo.getAccountName());
        }

        Map<String, Object> result = new HashMap<>();
        return getSuccessResult(result);
    }

    /**
     * 删除商品时间段配置
     *
     * @param request HttpServletRequest对象
     * @param id 配置ID
     * @return
     */
    @ApiOperation(value = "删除商品时间段配置")
    @RequestMapping(value = "/delete/{id}", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:edit')")
    public ResponseObject delete(HttpServletRequest request, @PathVariable("id") Integer id) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        com.fuint.common.dto.AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }
if (id <= 0) {
   return getFailureResult(201, "配置ID不能为空");
}

// 记录日志
logger.info("删除商品时间段配置，配置ID: {}", id);


        
                // 获取原有的配置
                MtGoodsTimeConfig config = goodsService.getGoodsTimeConfigById(id);
                if (config == null) {
                    return getFailureResult(201, "配置不存在");
                }
        
                // 删除配置
                goodsService.saveGoodsTimeConfig(config.getGoodsId(), new ArrayList<>(), accountInfo.getAccountName());
        Map<String, Object> result = new HashMap<>();
        return getSuccessResult(result);
    }

    /**
     * 获取商品时间段配置详情
     *
     * @param request HttpServletRequest对象
     * @param id 配置ID
     * @return
     */
    @ApiOperation(value = "获取商品时间段配置详情")
    @RequestMapping(value = "/detail/{id}", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:index')")
    public ResponseObject detail(HttpServletRequest request, @PathVariable("id") Integer id) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        com.fuint.common.dto.AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }
if (id <= 0) {
   return getFailureResult(201, "配置ID不能为空");
}

// 记录日志
logger.info("获取商品时间段配置详情，配置ID: {}", id);


        
                // 获取单个配置
                MtGoodsTimeConfig config = goodsService.getGoodsTimeConfigById(id);
                
                Map<String, Object> result = new HashMap<>();
                if (config != null) {
                    result.put("data", config);
                }
        return getSuccessResult(result);
    }
}