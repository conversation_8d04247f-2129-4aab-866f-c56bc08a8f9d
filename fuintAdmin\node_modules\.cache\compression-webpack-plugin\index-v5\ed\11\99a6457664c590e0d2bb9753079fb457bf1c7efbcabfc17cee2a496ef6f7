
ab0607c9b489bec6dbd91e3926f802a275ae8b3f	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"5d3652c4eb3f2441cbad549ed21c09f1\"}","integrity":"sha512-/XAX4i8n//kfJWK02veBAQC/TRmcV2UYAom22dMUlnrn67m9/DQVdfOePBX5NR1qyj7NK9d8V35r0hJSqF+mkw==","time":1755006150556,"size":2882475}