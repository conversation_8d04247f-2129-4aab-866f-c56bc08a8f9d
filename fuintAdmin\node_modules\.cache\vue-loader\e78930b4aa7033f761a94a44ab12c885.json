{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigForm.vue", "mtime": 1755006149730}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "sources": ["TimeConfigForm.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "TimeConfigForm.vue", "sourceRoot": "src/views/goods/components", "sourcesContent": ["<template>\n  <div class=\"time-config-container\">\n    <el-form ref=\"timeConfigForm\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"配置类型\" prop=\"configType\">\n            <el-radio-group v-model=\"form.configType\" @change=\"handleConfigTypeChange\">\n              <el-radio label=\"DAILY\">每日通用</el-radio>\n              <el-radio label=\"WEEKLY\">按周设置</el-radio>\n              <el-radio label=\"CUSTOM\">自定义日期</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 按周设置时显示星期选择 -->\n      <el-row v-if=\"form.configType === 'WEEKLY'\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"选择星期\" prop=\"weekDays\">\n            <el-checkbox-group v-model=\"form.weekDays\">\n              <el-checkbox v-for=\"day in weekOptions\" :key=\"day.value\" :label=\"day.value\">\n                {{ day.label }}\n              </el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 自定义日期时显示日期选择 -->\n      <el-row v-if=\"form.configType === 'CUSTOM'\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"选择日期\" prop=\"customDates\">\n            <el-date-picker\n              v-model=\"form.customDates\"\n              type=\"dates\"\n              placeholder=\"选择一个或多个日期\"\n              value-format=\"yyyy-MM-dd\"\n              :picker-options=\"pickerOptions\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 时间段设置 -->\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"时间段\" prop=\"timeRanges\">\n            <div class=\"time-ranges-container\">\n              <div v-for=\"(range, index) in form.timeRanges\" :key=\"index\" class=\"time-range-item\">\n                <el-time-picker\n                  v-model=\"range.startTime\"\n                  placeholder=\"开始时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  :picker-options=\"{\n                    selectableRange: '00:00:00 - 23:59:59'\n                  }\"\n                />\n                <span class=\"time-separator\">至</span>\n                <el-time-picker\n                  v-model=\"range.endTime\"\n                  placeholder=\"结束时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  :picker-options=\"{\n                    selectableRange: '00:00:00 - 23:59:59'\n                  }\"\n                />\n                <el-button\n                  type=\"danger\"\n                  icon=\"el-icon-delete\"\n                  size=\"mini\"\n                  circle\n                  @click=\"removeTimeRange(index)\"\n                  :disabled=\"form.timeRanges.length === 1\"\n                />\n              </div>\n              <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"addTimeRange\">\n                添加时间段\n              </el-button>\n              <div class=\"form-tip\">提示：时间段不能重叠，支持跨天时间段配置（如22:00-02:00）</div>\n            </div>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"状态\" prop=\"status\">\n            <el-radio-group v-model=\"form.status\">\n              <el-radio label=\"A\">启用</el-radio>\n              <el-radio label=\"N\">禁用</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TimeConfigForm',\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    goodsId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      form: {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      },\n      weekOptions: [\n        { label: '周一', value: 1 },\n        { label: '周二', value: 2 },\n        { label: '周三', value: 3 },\n        { label: '周四', value: 4 },\n        { label: '周五', value: 5 },\n        { label: '周六', value: 6 },\n        { label: '周日', value: 7 }\n      ],\n      pickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < Date.now() - 8.64e7\n        }\n      },\n      rules: {\n        configType: [\n          { required: true, message: '请选择配置类型', trigger: 'change' }\n        ],\n        weekDays: [\n          {\n            required: true,\n            message: '请至少选择一个星期',\n            trigger: 'change',\n            validator: (rule, value, callback) => {\n              if (this.form.configType === 'WEEKLY' && (!value || value.length === 0)) {\n                callback(new Error('请至少选择一个星期'))\n              } else {\n                callback()\n              }\n            }\n          }\n        ],\n        customDates: [\n          {\n            required: true,\n            message: '请至少选择一个日期',\n            trigger: 'change',\n            validator: (rule, value, callback) => {\n              if (this.form.configType === 'CUSTOM' && (!value || value.length === 0)) {\n                callback(new Error('请至少选择一个日期'))\n              } else {\n                callback()\n              }\n            }\n          }\n        ],\n        timeRanges: [\n          {\n            required: true,\n            message: '请设置至少一个时间段',\n            trigger: 'blur',\n            validator: (rule, value, callback) => {\n              if (!value || value.length === 0) {\n                callback(new Error('请设置至少一个时间段'))\n              } else {\n                // 检查时间段是否完整\n                for (let range of value) {\n                  if (!range.startTime || !range.endTime) {\n                    callback(new Error('请完善时间段设置'))\n                    return\n                  }\n                }\n\n                // 优化：使用排序+线性扫描检查重叠，时间复杂度从O(n²)降为O(n log n)\n                const sortedRanges = [...value].sort((a, b) => {\n                  // 将时间转换为分钟数进行比较\n                  const timeToMinutes = (time) => {\n                    const [hours, minutes] = time.split(':').map(Number);\n                    return hours * 60 + minutes;\n                  };\n\n                  return timeToMinutes(a.startTime) - timeToMinutes(b.startTime);\n                });\n\n                // 检查相邻时间段是否重叠\n                for (let i = 0; i < sortedRanges.length - 1; i++) {\n                  const current = sortedRanges[i];\n                  const next = sortedRanges[i + 1];\n\n                  const currentStart = timeToMinutes(current.startTime);\n                  const currentEnd = timeToMinutes(current.endTime);\n                  const nextStart = timeToMinutes(next.startTime);\n                  const nextEnd = timeToMinutes(next.endTime);\n\n                  // 处理跨天情况\n                  const currentCrossDay = currentEnd <= currentStart;\n                  const nextCrossDay = nextEnd <= nextStart;\n\n                  if (currentCrossDay && nextCrossDay) {\n                    // 两个都是跨天时间段，总是重叠\n                    callback(new Error('时间段不能重叠'))\n                    return\n                  } else if (currentCrossDay) {\n                    // 当前是跨天，下一个是正常\n                    if (nextStart >= currentStart || nextEnd <= currentEnd) {\n                      callback(new Error('时间段不能重叠'))\n                      return\n                    }\n                  } else if (nextCrossDay) {\n                    // 当前是正常，下一个是跨天\n                    if (currentStart >= nextStart || currentEnd <= nextEnd) {\n                      callback(new Error('时间段不能重叠'))\n                      return\n                    }\n                  } else {\n                    // 两个都是正常时间段\n                    if (nextStart < currentEnd) {\n                      callback(new Error('时间段不能重叠'))\n                      return\n                    }\n                  }\n                }\n                callback()\n              }\n            }\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        if (newVal && Object.keys(newVal).length > 0) {\n          this.form = {\n            ...this.form,\n            ...newVal,\n            timeRanges: newVal.timeRanges && newVal.timeRanges.length > 0\n              ? newVal.timeRanges\n              : [{ startTime: '', endTime: '' }]\n          }\n          // 数据更新后排序\n          this.$nextTick(() => {\n            this.sortTimeRanges();\n          });\n        }\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    // 将时间转换为分钟数进行比较\n    timeToMinutes(time) {\n      const [hours, minutes] = time.split(':').map(Number);\n      return hours * 60 + minutes;\n    },\n\n    // 对时间段进行排序\n    sortTimeRanges() {\n      if (this.form.timeRanges && this.form.timeRanges.length > 1) {\n        this.form.timeRanges.sort((a, b) => {\n          if (a.startTime && b.startTime) {\n            return this.timeToMinutes(a.startTime) - this.timeToMinutes(b.startTime);\n          }\n          return 0;\n        });\n      }\n    },\n\n    handleConfigTypeChange() {\n      // 切换配置类型时清空相关数据\n      if (this.form.configType === 'DAILY') {\n        this.form.weekDays = [];\n        this.form.customDates = [];\n      } else if (this.form.configType === 'WEEKLY') {\n        this.form.customDates = [];\n      } else if (this.form.configType === 'CUSTOM') {\n        this.form.weekDays = [];\n      }\n\n      // 提示用户配置类型已切换\n      this.$message.info('已切换配置类型，请重新设置相关选项');\n\n      // 验证表单\n      this.$nextTick(() => {\n        this.$refs.timeConfigForm.validateField('weekDays');\n        this.$refs.timeConfigForm.validateField('customDates');\n      });\n\n      // 手动排序时间段\n      this.sortTimeRanges();\n    },\n\n    addTimeRange() {\n      this.form.timeRanges.push({ startTime: '', endTime: '' });\n      // 添加新时间段后排序\n      this.$nextTick(() => {\n        this.sortTimeRanges();\n      });\n    },\n\n    removeTimeRange(index) {\n      if (this.form.timeRanges.length > 1) {\n        this.form.timeRanges.splice(index, 1);\n        // 删除时间段后排序\n        this.$nextTick(() => {\n          this.sortTimeRanges();\n        });\n      }\n    },\n\n    validate() {\n      return new Promise((resolve, reject) => {\n        this.$refs.timeConfigForm.validate(valid => {\n          if (valid) {\n            // 验证通过时排序\n            this.sortTimeRanges();\n            resolve(this.form);\n          } else {\n            this.$message.error('表单验证失败，请检查输入内容');\n            reject(new Error('表单验证失败'));\n          }\n        });\n      });\n    },\n\n    resetForm() {\n      this.$refs.timeConfigForm.resetFields();\n      this.form = {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      };\n      // 重置后排序\n      this.$nextTick(() => {\n        this.sortTimeRanges();\n      });\n    },\n    addTimeRange() {\n      this.form.timeRanges.push({ startTime: '', endTime: '' })\n    },\n    removeTimeRange(index) {\n      if (this.form.timeRanges.length > 1) {\n        this.form.timeRanges.splice(index, 1)\n      }\n    },\n    validate() {\n      return new Promise((resolve, reject) => {\n        this.$refs.timeConfigForm.validate(valid => {\n          if (valid) {\n            resolve(this.form)\n          } else {\n            this.$message.error('表单验证失败，请检查输入内容')\n            reject(new Error('表单验证失败'))\n          }\n        })\n      })\n    },\n    resetForm() {\n      this.$refs.timeConfigForm.resetFields()\n      this.form = {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.time-config-container {\n  padding: 20px;\n}\n\n.time-ranges-container {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.time-range-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.time-separator {\n  margin: 0 5px;\n  color: #606266;\n}\n</style>\n"]}]}