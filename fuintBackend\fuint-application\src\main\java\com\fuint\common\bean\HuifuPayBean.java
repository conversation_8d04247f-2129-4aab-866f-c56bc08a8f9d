package com.fuint.common.bean;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 汇付支付Bean
 */
@Component
@ConfigurationProperties(prefix = "huifupay")
public class HuifuPayBean {

    private String procutId;     
    private String sysId;     
    private String huifuId;     
    private String rsaPrivateKey;     
    private String rsaPublicKey;    

    public String getProcutId() {
        return procutId;
    }

    public void setProcutId(String procutId) {
        this.procutId = procutId;
    }

    public String getSysId() {
        return sysId;
    }

    public void setSysId(String sysId) {
        this.sysId = sysId;
    }

    public String getHuifuId() {
        return huifuId;
    }

    public void setHuifuId(String huifuId) {
        this.huifuId = huifuId;
    }

    public String getRsaPrivateKey() {
        return rsaPrivateKey;
    }

    public void setRsaPrivateKey(String rsaPrivateKey) {
        this.rsaPrivateKey = rsaPrivateKey;
    }

    public String getRsaPublicKey() {
        return rsaPublicKey;
    }

    public void setRsaPublicKey(String rsaPublicKey) {
        this.rsaPublicKey = rsaPublicKey;
    }

    @Override
    public String toString() {
        return "HuifuPayBean [procutId=" + procutId + ", sysId=" + sysId 
            + ", huifuId=" + huifuId + ", rsaPrivateKey=" + rsaPrivateKey 
            + ", rsaPublicKey=" + rsaPublicKey + "]";
    }
}