package com.fuint.common.dto;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;

/**
 * 套餐分组商品对象
 */
@Getter
@Setter
public class PackageGroupItemDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 分组ID
     */
    private Integer groupId;

    /**
     * 套餐商品ID
     */
    private Integer goodsId;

    /**
     * 包含商品ID
     */
    private Integer itemGoodsId;
    
    /**
     * 商品信息
     */
    private GoodsDto itemGoods;

    /**
     * 数量
     */
    private Integer quantity;

    /**
     * 额外加价
     */
    private BigDecimal extraPrice;

    /**
     * 商品在组内排序
     */
    private Integer sort;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 状态
     */
    private String status;
}