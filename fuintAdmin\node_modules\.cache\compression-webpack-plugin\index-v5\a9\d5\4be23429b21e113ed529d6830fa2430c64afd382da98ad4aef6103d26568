
cb068b928eebc658062e0803f0ace17c5d12cdb7	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"1b2d7995cb0628bd33c076da958f77e0\"}","integrity":"sha512-4czRjTxrgSm+zmWvYCz+SPaAj2lXh1qAB2n+N4o1y8boKzESCLNVMr/i1COP2PRT3NmeKnHsRloavb3n8ce2ng==","time":1755006352354,"size":6675319}