package com.fuint.common.test;

import com.fuint.common.service.HuifuPayService;
import com.fuint.common.service.OrderService;
import com.fuint.common.service.PrinterService;
import com.fuint.framework.exception.BusinessCheckException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;


@Component
public class PrinterServiceTest implements CommandLineRunner {
  
  private static final Logger logger = LoggerFactory.getLogger(HuifuPayServiceTest.class);

  @Autowired
  private PrinterService printerService;

  
  @Autowired
  private OrderService orderService;

  @Override
  public void run(String... args) {
      // testPrintOrder();
  }

  /**
   * 测试获取发票二维码
   */
  public void testPrintOrder() {
      try {
          // 这里需要替换为实际存在的订单号
          String orderSn = "202505121952519628872";
          
          logger.info("开始测试打印订单，订单号: {}", orderSn);
          printerService.printOrder(orderService.getOrderByOrderSn(orderSn),true,false,true,null);
      } catch (Exception e) {
          logger.error("测试过程中发生异常: {}", e.getMessage(), e);
      }
  }
}
