package com.fuint.module.schedule;

import com.fuint.common.service.MessageService;
import com.fuint.common.service.WeixinService;
import com.fuint.common.service.impl.PickupCodeServiceImpl;
import com.fuint.common.util.RedisUtil;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.repository.model.MtMessage;
import com.fuint.utils.StringUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.core.env.Environment;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import java.util.Date;
import java.util.List;

/**
 * 会员消息定时任务
 *

 */
@EnableScheduling
@Component("pickupCodeJob")
public class PickupCodeJob {

    private Logger logger = LoggerFactory.getLogger(PickupCodeJob.class);
  

    @Autowired
    private Environment environment; 

    @Scheduled(cron = "${pickupCode.job.time}")
    @Transactional(rollbackFor = Exception.class)
    public void dealMessage() throws BusinessCheckException {
        
        String theSwitch = environment.getProperty("pickupCode.job.switch");
        if (theSwitch != null && theSwitch.equals("1")) {
            logger.info("PickupCodeJobStart!!!");            
            // 删除所有匹配的取餐码计数器
            RedisUtil.removePattern(PickupCodeServiceImpl.PICKUP_CODE_KEY.replace("%d", "*"));  
            logger.info("PickupCodeJobEnd!!!");
        }
    }
}
