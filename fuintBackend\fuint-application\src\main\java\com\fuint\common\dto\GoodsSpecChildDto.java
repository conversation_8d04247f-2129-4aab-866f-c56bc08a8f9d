package com.fuint.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;
import java.math.BigDecimal;

import java.io.Serializable;

/**
 * 商品规格子类实体
 *

 */
@Getter
@Setter
public class GoodsSpecChildDto implements Serializable {

   @ApiModelProperty("自增ID")
   private Integer id;

   @ApiModelProperty("规格名称")
   private String name; 
   
   @ApiModelProperty("额外价格")
   private BigDecimal price;

   @ApiModelProperty("是否选择")
   private boolean checked;
   
   @ApiModelProperty("制作饮料的编码")
   private String makeCode;

}

