
d4383ea1735e309e1e06157e4e9e9df85270c25d	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"1729808054f97774b863512bb18b10b1\"}","integrity":"sha512-lgjp2b6ApIwmkW7Xc+8Lc8TGIRSpCKfhHMa+maHB16d0HfY7feGyRFVaR4WxLtvf++N0C0h9PhTpO8T0jsLIrg==","time":1755006070970,"size":6670889}