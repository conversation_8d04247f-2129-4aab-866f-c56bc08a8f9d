
58640f7844832afd20cd6aecd9bda19b7ac33247	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"976bcc27eb425d6f5495caeeea1df7ee\"}","integrity":"sha512-D6+Zhi37SmnMUvh0QjGh453wqicU4+8M/V0YH2vHEW5XOqRvKjrytnYqrQde+XOjfJ5xDm2jmLLxD23qX3HNdg==","time":1755006070774,"size":2882596}