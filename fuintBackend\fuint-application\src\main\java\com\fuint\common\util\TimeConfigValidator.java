package com.fuint.common.util;

import com.fuint.common.enums.StatusEnum;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.repository.model.MtGoodsTimeConfig;
import org.apache.commons.lang.StringUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

/**
 * 时间段配置验证工具类
 *
 * 
 */
public class TimeConfigValidator {

    private static final SimpleDateFormat TIME_FORMAT = new SimpleDateFormat("HH:mm");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd");

    /**
     * 验证时间段配置
     *
     * @param timeConfigs 时间段配置列表
     * @throws BusinessCheckException
     */
    public static void validateTimeConfigs(List<MtGoodsTimeConfig> timeConfigs) throws BusinessCheckException {
        if (timeConfigs == null || timeConfigs.isEmpty()) {
            return;
        }

        for (MtGoodsTimeConfig config : timeConfigs) {
            validateSingleConfig(config);
        }
    }

    /**
     * 验证单个时间段配置
     *
     * @param config 时间段配置
     * @throws BusinessCheckException
     */
    public static void validateSingleConfig(MtGoodsTimeConfig config) throws BusinessCheckException {
        if (config == null) {
            throw new BusinessCheckException("时间段配置不能为空");
        }

        // 验证配置类型
        if (StringUtils.isBlank(config.getConfigType())) {
            throw new BusinessCheckException("配置类型不能为空");
        }
        if (!"1".equals(config.getConfigType()) && !"2".equals(config.getConfigType()) && !"3".equals(config.getConfigType())) {
            throw new BusinessCheckException("配置类型只能是1、2或3");
        }

        // 验证开始时间和结束时间
        if (StringUtils.isBlank(config.getStartTime())) {
            throw new BusinessCheckException("开始时间不能为空");
        }
        if (StringUtils.isBlank(config.getEndTime())) {
            throw new BusinessCheckException("结束时间不能为空");
        }

        // 验证时间格式
        if (!isValidTimeFormat(config.getStartTime())) {
            throw new BusinessCheckException("开始时间格式不正确，应为HH:mm格式");
        }
        if (!isValidTimeFormat(config.getEndTime())) {
            throw new BusinessCheckException("结束时间格式不正确，应为HH:mm格式");
        }

        // 验证时间格式正确性，但允许跨天时间段（开始时间可以大于结束时间，如22:00-02:00）
        // 移除原来的时间范围验证逻辑

        // 根据配置类型验证特定字段
        if ("2".equals(config.getConfigType())) {
            // 按周设置
            if (config.getWeekDay() == null || config.getWeekDay() < 1 || config.getWeekDay() > 7) {
                throw new BusinessCheckException("星期几必须在1-7之间");
            }
        } else if ("3".equals(config.getConfigType())) {
            // 自定义日期
            if (StringUtils.isBlank(config.getSpecifyDate())) {
                throw new BusinessCheckException("指定日期不能为空");
            }
            if (!isValidDateFormat(config.getSpecifyDate())) {
                throw new BusinessCheckException("指定日期格式不正确，应为yyyy-MM-dd格式");
            }
        }

        // 验证状态
        if (StringUtils.isNotBlank(config.getStatus()) && !StatusEnum.ENABLED.getKey().equals(config.getStatus()) && !StatusEnum.DISABLE.getKey().equals(config.getStatus())) {
            throw new BusinessCheckException("状态只能是A或D");
        }
    }

    /**
     * 验证时间格式
     *
     * @param time 时间字符串
     * @return 是否有效
     */
    private static boolean isValidTimeFormat(String time) {
        try {
            TIME_FORMAT.setLenient(false);
            TIME_FORMAT.parse(time);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 验证日期格式
     *
     * @param date 日期字符串
     * @return 是否有效
     */
    private static boolean isValidDateFormat(String date) {
        try {
            DATE_FORMAT.setLenient(false);
            DATE_FORMAT.parse(date);
            return true;
        } catch (ParseException e) {
            return false;
        }
    }

    /**
     * 检查当前时间是否在配置的时间段内
     *
     * @param config 时间段配置
     * @param currentTime 当前时间(HH:mm格式)
     * @param weekDay 星期几(1-7)
     * @param specifyDate 指定日期(yyyy-MM-dd格式)
     * @return 是否在时间段内
     */
    public static boolean isInTimeRange(MtGoodsTimeConfig config, String currentTime, Integer weekDay, String specifyDate) {
        try {
            // 验证配置类型
            if ("1".equals(config.getConfigType())) {
                // 每日通用，只需验证时间
                return isTimeInRange(currentTime, config.getStartTime(), config.getEndTime());
            } else if ("2".equals(config.getConfigType())) {
                // 按周设置，验证星期几和时间
                if (config.getWeekDay() != null && config.getWeekDay().equals(weekDay)) {
                    return isTimeInRange(currentTime, config.getStartTime(), config.getEndTime());
                }
            } else if ("3".equals(config.getConfigType())) {
                // 自定义日期，验证日期和时间
                if (config.getSpecifyDate() != null && config.getSpecifyDate().equals(specifyDate)) {
                    return isTimeInRange(currentTime, config.getStartTime(), config.getEndTime());
                }
            }
            return false;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 检查时间是否在指定范围内
     *
     * @param time 要检查的时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 是否在范围内
     */
    private static boolean isTimeInRange(String time, String startTime, String endTime) {
        try {
            Date checkTime = TIME_FORMAT.parse(time);
            Date start = TIME_FORMAT.parse(startTime);
            Date end = TIME_FORMAT.parse(endTime);
            
            // 处理跨天的情况
            if (end.before(start)) {
                // 跨天，比如22:00-02:00
                return !checkTime.before(start) || !checkTime.after(end);
            } else {
                // 不跨天
                return !checkTime.before(start) && !checkTime.after(end);
            }
        } catch (ParseException e) {
            return false;
        }
    }
}