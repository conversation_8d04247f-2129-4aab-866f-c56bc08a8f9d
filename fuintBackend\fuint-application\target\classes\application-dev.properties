# MyBatis SQL 日志配置
mybatis-plus.configuration.log-impl=org.apache.ibatis.logging.stdout.StdOutImpl

logging.level.org.springframework.scheduling=ERROR

# 数据库配置
spring.datasource.url=*************************************************************************************
spring.datasource.username=root
spring.datasource.password=123456

# Redis配置
spring.session.store-type=redis
spring.session.redis.namespace=hatea
# Redis数据库索引（默认为0）
spring.redis.database=0 
# Redis服务器地址(生产)
spring.redis.host=127.0.0.1
# Redis服务器连接端口
spring.redis.port=6379
# Redis服务器连接密码（默认为空）
spring.redis.password=123456
# 连接池最大连接数（使用负值表示没有限制）
spring.redis.pool.max-active=-1
# 连接池最大阻塞等待时间（使用负值表示没有限制）
spring.redis.pool.max-wait=-1 
# 连接池中的最大空闲连接
spring.redis.pool.max-idle=8 
# 连接池中的最小空闲连接
spring.redis.pool.min-idle=1 
# 连接超时时间（毫秒）
spring.redis.timeout=30000

# 系统名称
system.name = HaTea餐饮会员系统

# 前端h5地址
website.url=https://hatea.zhijuchina.com/h5/

# 上传图片本地地址
# images.root=C:/fuintFoodSystem_v1.3/fuintBackend/fuint-application/target/classes
images.root=D:/workspace/fuintFoodSystem/fuintBackend/uploads
images.path=/uploadImages/

# 上传图片服务器域名
images.upload.url=http://localhost:8080

# 上传图片允许的大小（单位：MB）
images.upload.maxSize=5
 

################## 定时脚本配置 #########################

# 重置取餐码
pickupCode.job.switch = 1
pickupCode.job.time = 0 0 0 * * ?
# 定时发送消息
message.job.switch = 1
message.job.time = 0 0/1 * * * ?

# 卡券到期处理
couponExpire.job.switch = 1
couponExpire.job.time = 0 0/1 * * * ?

# 订单超时取消
orderCancel.job.switch = 1
orderCancel.job.time = 0 0/1 * * * ?

# 分佣提成计算
commission.job.switch = 1
commission.job.time = 0 0/1 * * * ?

# 预约取餐打印
reservationPrint.job.switch = 1
reservationPrint.job.time = 0 0/1 * * * ?

################## 阿里云短信配置 #######################
# 短信接口模式[0-关闭 1-打开]
aliyun.sms.mode = 0
aliyun.sms.accessKeyId=LTAI4GJMjV123oXsrQJLnPZt
aliyun.sms.accessKeySecret=eGVBL30u5Ypj234d7XODlwYKWTaGT
# 阿里云短信签名
aliyun.sms.signName=哈帆科技

################## 阿里云OSS存储配置######################
# 模式[0-关闭 1-打开]
aliyun.oss.mode = 0
aliyun.oss.accessKeyId = LTAI4GJMjVhBa212rQJLnPZtt
aliyun.oss.accessKeySecret = eGVBL30u53456gXd7XODlwYKWTaGT
aliyun.oss.endpoint = https://oss-cn-shenzhen.aliyuncs.com
aliyun.oss.bucketName = fuint-application
# 上传文件夹
aliyun.oss.folder = uploads
# 访问域名
aliyun.oss.domain = https://fuint-application.oss-cn-shenzhen.aliyuncs.com


################## 汇付支付相关配置 ######################
huifupay.procutId=PAYUN
huifupay.sysId=6666000158371356
huifupay.huifuId=6666000158371356
huifupay.rsaPrivateKey=MIIEvgIBADANBgkqhkiG9w0BAQEFAASCBKgwggSkAgEAAoIBAQCgnnNlgqWuWjgUbJQCB1DxpzhDwE4bG5bG1ul+E17eSAS4ULVFlwW59liN68BZ+ToqGVyEloFWzBWrSLhkkojI6g19E6w146cBeahrlNMPBzX2gFRCHtaJoPh4rSkvJ4uTHeYEuZct7PbHL0hbIkrcKgM2qIUoZlBHtpbI5idwSP21/QAxGfmaaaz03fxQCTGX4ELOdtUwoguqXWjBOCLAvW+qmdTj6o218pASiS8JNm6adqOMYi9PXNnyu9hD2ZjH8nF+dHagaBhVgkrSDqGppPtTq7pMXuQxTAcC3p6swa6PwyuATIuUbJY5EJVKNlR9a/7so9VLOZoKON8AkYgtAgMBAAECggEBAIwrW1dmGYK7lx8z1kKi8d/B59cwdrkvbXifSutMqkjfnsbfICLX9hZgxs0rxRR/7GOa+GN5GOyPdAWjk8+pFZgIsbbKuUnaHv+plPI10a7WwyAYw+9kDCPVh6GAtGVDZIoB2J+c+zMIDiCrwcBOf/f87gsALByilULQNGykdhyFI9Hj14lDbOXJOPLtTQLG1F1/8EXmrGVCTYxzhKc03JuJb21hiALa8m8k1AEuirCK+Cv/1zN8yixJUT9UqKp6w614nW/VO2KFddI72MhaUMma0dlU1ZSgIdNKvZrnDXa0xJc1xyOIRpzVU/KsMADmi6CXTzp/g2xr2XNO+wgVFV0CgYEA17rBaGcoxhep716shdw9uCxnOXrEts1YXQpKWIY/yXWb5i6mwMUcHi85KeIfqJtQ9qk2bkKZtyhgq0ORBr2FuoE0OfP3XL1puSFcJ6cpcy70+7wuzoaL6bsrLP7PNAymheneOXcBwgw4vG4JYe0GpuES4zKzQ81nvJNyQX0zqwcCgYEAvpoUxkbfv4/GGQg86aupURbEKMyF6omOG9RBl3niK7LL8Peh7hy+PEZbbGJKhj/25SifzqVb7RW5IN9m3pSvVy/7HB9+fyZlIKbnTVzTTIzDfVrzVNZoxcfYbwfG4nSWs5cCfY3YPUqvBRJ87oauc5vzMqmfAXTMnYAMX5PUQisCgYBXVtfY0fMTCB+Xtse4RiuCtg9wmQCtVcORn4nr/yYyBPiCH58JaoPwwIOvLkpM89y6kFVmHHapTaKdk/RSO0a59HdVgy1BkboM5rGtqCLC7F7o0A5PNO/OSCW5yMvQsdJ1YvLCZykCC/qoZxBLYjAQPg1siGlFaRK3qSZX1z2k3wKBgCkMQeEZrmIw8na9oXCpeYNvJburoEOMcEBqC941b5H9pZI1ongHVKjidhn3aRlUeTWM9C2qywtaHE6KDjttRf2+5ZN+7N0xRaIsj6/Hh/H6drdueO0SZ+oASgpPYHOwrCvtH6oufB1nEh5DhwhPlQ5szYlhok7L+q0IwKFs0s+VAoGBAKxyaaREliGsxCCjbYtcljXHfgOyeyjm+us2guiaVpPqG9GfDl1JiUqLnG8VjHNipP/NXDWMoEh4RRPqjcrfSRf1yTmHBE9YZtEHp4lbWzmci6mwjILLV8c3W572zqG7tKwbwctBk+K39+1BTtF0+WaTkLMOiLuVIFEPTqKQG+JK
huifupay.rsaPublicKey=MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAjl+QChXWUk++nJwGHmHgLiYoDzceBAPAF64S55yhO+vVNETNjTGtjmQshaw3kIc2hI4Jf8OUD8NRamDkwqqVMWzA2KbEw7G9CFHpbBAVLrFmdx1pmxG/C5pnMGyQ5MYJXfK9ne0f2JtKR9o5E/jQGDNVKgIwfD1U3LJGqNwyrH5EJVjufYr18CazbIUZa4BSUBj4uGTWcWlLJVvM3dJnAYa26jYBKiDM98zhibLlk7tJFPZx0bQ0wlnSnhoNvJF08HuzkeUr5OT3A2Bbqj+s99Wb5I40uuICz27ejQKFdtslN/g5dTVenmSwBBqFh2Q7PWiw0VJcrE4KT6u4gDYR8wIDAQAB

################## 微信相关配置 ##########################
# 公众号配置
weixin.official.appId=wx3c05e06cb28f044a
weixin.official.appSecret=2cb9c242eba81ee2797f04bbae954f0f

# 小程序配置
wxpay.appId=wxe0cd997778d96c49
wxpay.appSecret=aed46ff39e36caba4360d001c9d643dd
# wxpay.mchId=1636980848
# wxpay.apiV2=fuyuan20201030yanhe9999999999999
# wxpay.certPath=/usr/local/fuint/cert/apiclient_cert.p12
# wxpay.domain=https://www.fuint.cn/fuint-application

################## 支付宝支付相关配置 ######################
# alipay.appId = 应用编号
# alipay.privateKey = 应用私钥
# alipay.publicKey = 支付宝公钥（通过应用公钥上传到支付宝开放平台换取支付宝公钥）
# alipay.serverUrl=https://openapi.alipay.com/gateway.do
# alipay.domain=https://www.fuint.cn/fuint-application/clientApi/pay/aliPayCallback

################ 微信订阅模板消息配置 ######################
weixin.subMessage.orderCreated=[{'key':'time', 'name':'订单时间'},{'key':'orderSn', 'name':'订单号'},{'key':'remark', 'name':'备注信息'}]
weixin.subMessage.deliverGoods=[{'key':'receiver', 'name':'收货人'}, {'key':'orderSn', 'name':'订单号'}, {'key':'expressCompany', 'name':'快递公司'}, {'key':'expressNo', 'name':'快递单号'}]
weixin.subMessage.couponExpire=[{'key':'name', 'name':'卡券名称'}, {'key':'expireTime', 'name':'到期时间'},{'key':'tips', 'name':'温馨提示'}]
weixin.subMessage.couponArrival=[{'key':'name', 'name':'卡券名称'},{'key':'amount', 'name':'金额'},{'key':'tips', 'name':'温馨提示'}]
weixin.subMessage.balanceChange=[{'key':'amount', 'name':'变动金额'},{'key':'time', 'name':'变动时间'},{'key':'tips', 'name':'温馨提示'}]
weixin.subMessage.couponConfirm=[{'key':'name', 'name':'卡券名称'},{'key':'time', 'name':'核销时间'}]
weixin.subMessage.pointChange=[{'key':'amount', 'name':'变动数量'},{'key':'time', 'name':'变动时间'},{'key':'remark', 'name':'备注信息'}]



# 哈帆支付配置
hafan.app.id=hafan
hafan.device.id=63cd3dab-f402-4031-80e9-4430444426b0
hafan.secret.key=4a63d24b-b503-4829-af79-c091c2c3edb8
hafan.base.url=http://localhost:1337