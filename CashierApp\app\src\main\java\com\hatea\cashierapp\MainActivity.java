package com.hatea.cashierapp;

import android.annotation.SuppressLint;
import android.bluetooth.BluetoothGattCharacteristic;
import android.bluetooth.le.ScanFilter;
import android.content.pm.ActivityInfo;
import android.graphics.Rect;
import android.os.Build;
import android.os.Bundle;
import android.os.ParcelUuid;
import android.util.Log;
import android.webkit.WebResourceRequest;
import android.webkit.WebSettings;
import android.webkit.WebView;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.contract.ActivityResultContracts;
import androidx.appcompat.app.AppCompatActivity;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import android.Manifest;
import android.webkit.WebViewClient;

import cn.com.heaton.blelibrary.ble.Ble;
import cn.com.heaton.blelibrary.ble.BleLog;
import cn.com.heaton.blelibrary.ble.callback.BleConnectCallback;
import cn.com.heaton.blelibrary.ble.callback.BleNotifyCallback;
import cn.com.heaton.blelibrary.ble.callback.BleScanCallback;
import cn.com.heaton.blelibrary.ble.model.BleDevice;

public class MainActivity extends AppCompatActivity {
    private static final String TAG = "MainActivity";
    private static final String SERVICE_UUID_STR = "0000feea-0000-1000-8000-00805f9b34fb";
    private static final UUID SERVICE_UUID = UUID.fromString(SERVICE_UUID_STR);

    private WebView webView;
    private Ble ble;


    private StringBuilder receivedChunks = new StringBuilder();


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        setRequestedOrientation(ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE);

        requestPermission();

        // 初始化 WebView
        initializeWebView();

    }
    //请求权限
    public void requestPermission() {
        List<String> permissions = new ArrayList<>();
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
            permissions.add(Manifest.permission.BLUETOOTH_SCAN);
            permissions.add(Manifest.permission.BLUETOOTH_ADVERTISE);
            permissions.add(Manifest.permission.BLUETOOTH_CONNECT);
            permissions.add(Manifest.permission.ACCESS_COARSE_LOCATION);
            permissions.add(Manifest.permission.ACCESS_FINE_LOCATION);
        } else {
            permissions.add(Manifest.permission.ACCESS_COARSE_LOCATION);
            permissions.add(Manifest.permission.ACCESS_FINE_LOCATION);
        }

        registerForActivityResult(new ActivityResultContracts.RequestMultiplePermissions(), new ActivityResultCallback<Map<String, Boolean>>() {
            @Override
            public void onActivityResult(Map<String, Boolean> map) {
                // must all permissions agree
                initBle();
            }
        }).launch(permissions.toArray(new String[0]));
    }

    private void initBle() {
        ScanFilter.Builder scanFilter = new ScanFilter.Builder();
        scanFilter.setServiceUuid(ParcelUuid.fromString(SERVICE_UUID_STR));

        Ble.options()//开启配置
                .setLogBleEnable(true)//设置是否输出打印蓝牙日志（非正式打包请设置为true，以便于调试）
                .setThrowBleException(true)//设置是否抛出蓝牙异常 （默认true）
                .setAutoConnect(false)//设置是否自动连接 （默认false）
                .setIgnoreRepeat(false)//设置是否过滤扫描到的设备(已扫描到的不会再次扫描)
                .setConnectTimeout(10 * 1000)//设置连接超时时长（默认10*1000 ms）
                .setMaxConnectNum(7)//最大连接数量
                .setScanPeriod(12 * 1000)//设置扫描时长（默认10*1000 ms）
                .setScanFilter(scanFilter.build())
                .setUuidService(SERVICE_UUID)//设置主服务的uuid（必填）
                .create(this.getApplication(), new Ble.InitCallback() {
                    @Override
                    public void success() {
                        BleLog.e("MainApplication", "初始化成功");
                    }

                    @Override
                    public void failed(int failedCode) {
                        BleLog.e("MainApplication", "初始化失败：" + failedCode);
                    }
                });
        ble = Ble.getInstance();
        this.startScan();
    }

    private void startScan(){
        ble.startScan(new BleScanCallback<BleDevice>() {
            @Override
            public void onLeScan(final BleDevice device, int rssi, byte[] scanRecord) {
                Log.d(TAG, "device.getBleName(): " + device.getBleName());

                ble.connect(device, new BleConnectCallback<BleDevice>() {
                    @Override
                    public void onConnectionChanged(BleDevice device) {
                        Log.d(TAG, "onConnectionChanged: " + device.getConnectionState() + ", 连接状态 2503 未连接状态 2504 正在连接 2505 连接成功");
                        if(device.isDisconnected()){
                            ble.stopScan();
                            startScan();
                        }
                    }

                    @Override
                    public void onReady(BleDevice device) {
                        super.onReady(device);
                        ble.stopScan();

                        ble.enableNotify(device, true, new BleNotifyCallback<BleDevice>() {
                            @Override
                            public void onChanged(BleDevice device, BluetoothGattCharacteristic characteristic) {
                                String chunk = characteristic.getStringValue(0);
                                receivedChunks.append(chunk);
                                if (chunk.contains("\n")) { // 检测到结束符
                                    String completeData = receivedChunks.toString();
                                    receivedChunks.setLength(0); // 清空 StringBuilder

                                    Log.d(TAG, "Complete data: " + completeData);

                                    runJsEvent(completeData);
                                }

                            }
                        });

                    }

                });
            }

            @Override
            public void onStart() {
                super.onStart();
            }

            @Override
            public void onStop() {
                super.onStop();
            }

            @Override
            public void onScanFailed(int errorCode) {
                super.onScanFailed(errorCode);
                Log.e(TAG, "onScanFailed: "+errorCode);
            }
        });
    }

    @Override
    public void onBackPressed() {
        if (webView.canGoBack()) {
            webView.goBack(); // 如果 WebView 有历史记录，则返回上一页
        } else {
            super.onBackPressed(); // 否则执行默认的返回行为
        }
    }
    private void initializeWebView() {
        webView = findViewById(R.id.webview);
        webView.getViewTreeObserver().addOnGlobalLayoutListener(() -> {
            Rect r = new Rect();
            webView.getWindowVisibleDisplayFrame(r);
            int screenHeight = webView.getRootView().getHeight();
            int keypadHeight = screenHeight - r.bottom;

            if (keypadHeight > screenHeight * 0.15) { // 键盘弹出时
                webView.scrollBy(0, keypadHeight);
            } else { // 键盘收起时
                webView.scrollTo(0, 0);
            }
        });
        webView.setWebChromeClient(new CustomWebChromeClient(this));
        webView.setWebViewClient(new WebViewClient() {
            @Override
            public boolean shouldOverrideUrlLoading(WebView view, WebResourceRequest request) {
                // 如果是API 21及以上版本
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
                    view.loadUrl(request.getUrl().toString());
                } else {
                    // 对于API 21以下的版本
                    view.loadUrl(request.toString());
                }
                return true;
            }


        });
        WebSettings webSettings = webView.getSettings();
        webSettings.setJavaScriptEnabled(true);
        webSettings.setDomStorageEnabled(true);
        webView.setWebContentsDebuggingEnabled(true);
//        webView.loadUrl("file:///android_asset/index.html"); // 加载本地 HTML 文件
        webView.loadUrl("https://hatea.zhijuchina.com/cashier/?_v=" + (new Date()).getTime());
    }

    private void runJsEvent(String code){
        String script = String.format(
                "document.dispatchEvent(new CustomEvent('bleDataReceived', { detail: '%s' }));",
                code.trim()
        );
        runOnUiThread(() -> webView.evaluateJavascript(script, null));
    }
    @Override
    @SuppressLint("MissingPermission")
    protected void onDestroy() {
        super.onDestroy();
        ble.released();
    }
}
