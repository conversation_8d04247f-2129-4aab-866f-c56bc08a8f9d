package com.fuint.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fuint.repository.model.MtPrinterCate;
import com.fuint.framework.exception.BusinessCheckException;
import java.util.List;
import java.util.Map;

/**
 * 打印机分类关联业务接口
 *
 * @Created by FSQ
 */
public interface PrinterCateService extends IService<MtPrinterCate> {

    /**
     * 根据条件搜索打印机分类关联
     *
     * @param params 查询参数
     * @throws BusinessCheckException
     * @return
     */
    List<MtPrinterCate> queryPrinterCateListByParams(Map<String, Object> params) throws BusinessCheckException;

    /**
     * 添加打印机分类关联
     *
     * @param mtPrinterCate 打印机分类关联信息
     * @throws BusinessCheckException
     * @return
     */
    MtPrinterCate addPrinterCate(MtPrinterCate mtPrinterCate) throws BusinessCheckException;

    /**
     * 根据打印机ID删除关联
     *
     * @param printerId 打印机ID
     * @throws BusinessCheckException
     * @return
     */
    void deleteByPrinterId(Integer printerId) throws BusinessCheckException;

    /**
     * 根据分类ID删除关联
     *
     * @param cateId 分类ID
     * @throws BusinessCheckException
     * @return
     */
    void deleteByCateId(Integer cateId) throws BusinessCheckException;

    /**
     * 批量保存打印机分类关联
     *
     * @param printerId 打印机ID
     * @param cateIds 分类ID列表
     * @param merchantId 商户ID
     * @param storeId 店铺ID
     * @param operator 操作人
     * @throws BusinessCheckException
     * @return
     */
    void savePrinterCateRelations(Integer printerId, List<Integer> cateIds, Integer merchantId, Integer storeId, String operator) throws BusinessCheckException;

    /**
     * 根据商品分类ID查询关联的打印机ID列表
     *
     * @param cateId 商品分类ID
     * @param storeId 店铺ID
     * @return 打印机ID列表
     */
    List<Integer> getPrinterIdsByCateId(Integer cateId, Integer storeId);

}
