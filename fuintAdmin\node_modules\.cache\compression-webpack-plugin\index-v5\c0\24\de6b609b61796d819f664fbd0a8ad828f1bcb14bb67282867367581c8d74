
3672d6203e374a3e4e5da08b65313dd57a2d36e9	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"16f305140a19a14a50153004d563586f\"}","integrity":"sha512-Xy3GuPHKpaoXhVie2/iWg4Wf9ymSI+5GwtnlhRpKDoNjLp32FPyHWAcVcO8vyFuYzTMMeBZR5Wb+fEkweTZXiA==","time":1755006276078,"size":2882838}