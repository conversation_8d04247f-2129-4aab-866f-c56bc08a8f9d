
01c0da94657839d31a1c78b6122e3fbef450e2b8	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"5a078ba6d0be03ac8dc164e73a5628ad\"}","integrity":"sha512-HaPjOLZ+nSwzQM9yO2uTuC/7ZKqVU03TTXqknpkZzENU55hwQxt/xR4WqxiyFP/XNSKlCBsnBpG2aElPrrMgWQ==","time":1755004803395,"size":2882929}