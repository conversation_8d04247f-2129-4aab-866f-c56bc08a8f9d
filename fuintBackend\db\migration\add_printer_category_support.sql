-- 添加打印机分类支持的数据库迁移脚本

-- 1. 为打印机表添加类型字段（如果不存在）
ALTER TABLE `mt_printer` ADD COLUMN `TYPE` varchar(20) DEFAULT 'RECEIPT' COMMENT '打印机类型，RECEIPT小票，LABEL贴纸' AFTER `NAME`;

-- 2. 创建打印机分类关联表
CREATE TABLE IF NOT EXISTS `mt_printer_cate` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `PRINTER_ID` int NOT NULL COMMENT '打印机ID',
  `CATE_ID` int NOT NULL COMMENT '商品分类ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺ID',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A正常；D作废',
  PRIMARY KEY (`ID`),
  KEY `idx_printer_id` (`PRINTER_ID`),
  KEY `idx_cate_id` (`CATE_ID`),
  KEY `idx_store_cate` (`STORE_ID`, `CATE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='打印机分类关联表';

-- 3. 为现有的标签打印机设置类型
UPDATE `mt_printer` SET `TYPE` = 'LABEL' WHERE `NAME` LIKE '%标签%' OR `NAME` LIKE '%贴纸%';

-- 4. 插入一些示例数据（可选）
-- INSERT INTO `mt_printer_cate` (`PRINTER_ID`, `CATE_ID`, `MERCHANT_ID`, `STORE_ID`, `CREATE_TIME`, `UPDATE_TIME`, `OPERATOR`, `STATUS`) 
-- VALUES (1, 1, 1, 1, NOW(), NOW(), 'system', 'A');
