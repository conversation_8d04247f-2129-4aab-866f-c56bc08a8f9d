<template>
  <div class="time-config-list">
    <div class="header-actions">
      <el-button type="primary" icon="el-icon-plus" @click="handleAdd">新增时间段配置</el-button>
      <div class="form-tip">提示：可以为商品设置多个时间段配置，如每日通用、按周设置或自定义日期</div>
    </div>

    <el-table :data="configList" v-loading="loading" style="width: 100%">
      <el-table-column prop="configType" label="配置类型" width="120">
        <template slot-scope="scope">
          <el-tag :type="getConfigTypeTag(scope.row.configType)">
            {{ getConfigTypeText(scope.row.configType) }}
          </el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="configValue" label="配置值" min-width="200">
        <template slot-scope="scope">
          <div v-if="scope.row.configType === 'DAILY'">
            <span>每日通用</span>
          </div>
          <div v-else-if="scope.row.configType === 'WEEKLY'">
            <span>{{ formatWeekDays(scope.row.configValue) }}</span>
          </div>
          <div v-else-if="scope.row.configType === 'CUSTOM'">
            <span>{{ formatCustomDates(scope.row.configValue) }}</span>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="timeRanges" label="时间段" min-width="200">
        <template slot-scope="scope">
          <div v-for="(range, index) in parseTimeRanges(scope.row.timeRanges)" :key="index">
            <el-tag size="mini">{{ formatTimeRange(range.startTime, range.endTime) }}</el-tag>
          </div>
        </template>
      </el-table-column>

      <el-table-column prop="status" label="状态" width="80">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.status"
            active-value="A"
            inactive-value="N"
            @change="handleStatusChange(scope.row)"
          />
        </template>
      </el-table-column>

      <el-table-column prop="createTime" label="创建时间" width="150">
        <template slot-scope="scope">
          {{ parseTime(scope.row.createTime) }}
        </template>
      </el-table-column>

      <el-table-column label="操作" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button
            type="text"
            size="mini"
            icon="el-icon-edit"
            @click="handleEdit(scope.row)"
          >编辑</el-button>
          <el-button
            type="text"
            size="mini"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 新增/编辑对话框 -->
    <el-dialog
      :title="dialogTitle"
      :visible.sync="dialogVisible"
      width="600px"
      @close="handleDialogClose"
    >
      <TimeConfigForm
        ref="timeConfigForm"
        v-model="currentConfig"
        :goods-id="goodsId"
      />
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getGoodsTimeConfigs, saveGoodsTimeConfig, updateGoodsTimeConfig, deleteGoodsTimeConfig } from '@/api/goodsTimeConfig'
import TimeConfigForm from './TimeConfigForm'

export default {
  name: 'TimeConfigList',
  components: {
    TimeConfigForm
  },
  props: {
    goodsId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      loading: false,
      configList: [],
      dialogVisible: false,
      dialogTitle: '',
      currentConfig: {},
      isEdit: false
    }
  },
  watch: {
    goodsId: {
      handler(newVal) {
        if (newVal) {
          this.loadConfigs()
        }
      },
      immediate: true
    }
  },
  methods: {
    loadConfigs() {
      if (!this.goodsId) return

      this.loading = true
      getGoodsTimeConfigs(this.goodsId).then(response => {
        // 处理接口响应结构
        const apiData = response.data?.data || []
        this.configList = apiData.map(item => {
          // 将后端字段映射到前端模型
          return {
            id: item.id,
            merchantId: item.merchantId,
            storeId: item.storeId,
            goodsId: item.goodsId,
            configType: item.configType,
            weekDay: item.weekDay,
            specifyDate: item.specifyDate,
            timeRanges: JSON.stringify([{
              startTime: item.startTime,
              endTime: item.endTime
            }]),
            status: item.status,
            createTime: item.createTime,
            updateTime: item.updateTime,
            operator: item.operator
          }
        })
        this.loading = false
      }).catch(error => {
        console.error('加载时间段配置失败:', error)
        this.$message.error('加载时间段配置失败: ' + (error.message || '未知错误'))
        this.loading = false
      })
    },

    handleAdd() {
      this.isEdit = false
      this.dialogTitle = '新增时间段配置'
      this.currentConfig = {
        goodsId: this.goodsId,
        configType: 'DAILY',
        weekDays: [],
        customDates: [],
        timeRanges: [{ startTime: '', endTime: '' }],
        status: 'A'
      }
      this.dialogVisible = true

      // 提示用户可以开始配置
      this.$message.info('请配置时间段信息')
    },

    handleEdit(row) {
      this.isEdit = true
      this.dialogTitle = '编辑时间段配置'
      this.currentConfig = {
        ...row,
        weekDays: row.configType === 'WEEKLY' ? JSON.parse(row.configValue || '[]') : [],
        customDates: row.configType === 'CUSTOM' ? JSON.parse(row.configValue || '[]') : [],
        timeRanges: JSON.parse(row.timeRanges || '[]')
      }
      this.dialogVisible = true

      // 提示用户可以开始编辑
      this.$message.info('请编辑时间段配置信息')
    },

    handleDelete(row) {
      this.$confirm('确认删除该时间段配置吗？', '提示', {
        type: 'warning'
      }).then(() => {
        deleteGoodsTimeConfig(row.id).then(() => {
          this.$message.success('删除成功')
          this.loadConfigs()

          // 提示用户配置已删除
          this.$message.info('时间段配置已删除')
        }).catch(error => {
          console.error('删除失败:', error)
          this.$message.error('删除失败: ' + (error.message || '未知错误'))
        })
      }).catch(() => {
        // 用户取消删除
        this.$message.info('已取消删除操作')
      })
    },

    handleStatusChange(row) {
      const statusText = row.status === 'A' ? '启用' : '禁用'
      updateGoodsTimeConfig({
        id: row.id,
        status: row.status
      }).then(() => {
        this.$message.success(`${statusText}成功`)

        // 提示用户状态已更新
        this.$message.info(`时间段配置已${statusText}`)
      }).catch(error => {
        console.error('状态更新失败:', error)
        this.$message.error(`${statusText}失败: ` + (error.message || '未知错误'))
        row.status = row.status === 'A' ? 'N' : 'A'
      })
    },

    handleSave() {
      this.$refs.timeConfigForm.validate().then(formData => {
        const data = {
          ...formData,
          configValue: formData.configType === 'WEEKLY'
            ? JSON.stringify(formData.weekDays)
            : formData.configType === 'CUSTOM'
              ? JSON.stringify(formData.customDates)
              : '',
          timeRanges: JSON.stringify(formData.timeRanges)
        }

        const savePromise = this.isEdit
          ? updateGoodsTimeConfig(data)
          : saveGoodsTimeConfig(data)

        savePromise.then(() => {
          this.$message.success('保存成功')
          this.dialogVisible = false
          this.loadConfigs()

          // 提示用户配置已更新
          this.$message.info('时间段配置已更新')
        }).catch(error => {
          console.error('保存失败:', error)
          this.$message.error('保存失败: ' + (error.message || '未知错误'))
        })
      }).catch(() => {
        // 表单验证失败
      })
    },

    handleDialogClose() {
      this.$refs.timeConfigForm && this.$refs.timeConfigForm.resetForm()
    },

    getConfigTypeTag(type) {
      const map = {
        DAILY: 'success',
        WEEKLY: 'warning',
        CUSTOM: 'info'
      }
      return map[type] || 'default'
    },

    getConfigTypeText(type) {
      const map = {
        DAILY: '每日通用',
        WEEKLY: '按周设置',
        CUSTOM: '自定义日期'
      }
      return map[type] || type
    },

    formatWeekDays(weekDaysStr) {
      try {
        const weekDays = JSON.parse(weekDaysStr || '[]')
        const weekMap = {
          1: '周一',
          2: '周二',
          3: '周三',
          4: '周四',
          5: '周五',
          6: '周六',
          7: '周日'
        }
        return weekDays.map(day => weekMap[day]).join('、')
      } catch {
        return weekDaysStr
      }
    },

    formatCustomDates(datesStr) {
      try {
        const dates = JSON.parse(datesStr || '[]')
        return dates.join('、')
      } catch {
        return datesStr
      }
    },

    parseTimeRanges(timeRangesStr) {
      // 如果已经是对象，则直接返回
      if (Array.isArray(timeRangesStr)) {
        return timeRangesStr;
      }

      // 如果是字符串，则尝试解析
      try {
        return JSON.parse(timeRangesStr || '[]')
      } catch {
        return []
      }
    },

    parseTime(time) {
      if (!time) return ''
      return this.$moment(time).format('YYYY-MM-DD HH:mm:ss')
    },

    // 格式化时间段显示
    formatTimeRange(startTime, endTime) {
      if (!startTime || !endTime) return ''
      return `${startTime} - ${endTime}`
    }
  }
}
</script>

<style scoped>
.time-config-list {
  padding: 20px;
}

.header-actions {
  margin-bottom: 20px;
}
</style>
