#!/bin/bash

# 配置
sourceDirectory="./dist/"  # 相对路径，指向项目根目录下的 source 文件夹
destinationDirectory="/opt/hatea/public/admin"  # 服务器上的目标目录
serverUser="root"  # 服务器用户名
serverHost="**************"  # 服务器地址
serverPort=22  # 服务器端口（如果默认22可省略）
localTarFile="/tmp/backup.tar.gz"  # 临时存放的打包文件路径

# 打包目录为 .tar.gz 文件
echo "打包目录 $sourceDirectory 为 $localTarFile..."
tar -czvf $localTarFile -C $(dirname $sourceDirectory) $(basename $sourceDirectory)

# 上传到 Linux 服务器
echo "上传打包文件到服务器 $serverHost..."
scp -P $serverPort $localTarFile ${serverUser}@${serverHost}:/tmp/backup.tar.gz

# SSH 命令解压并覆盖到目标目录
echo "在服务器 $serverHost 上解压并覆盖目标目录 $destinationDirectory..."
ssh -p $serverPort ${serverUser}@${serverHost} "
  cd $destinationDirectory && tar -xzvf /tmp/backup.tar.gz --warning=no-timestamp --strip-components=1  --overwrite
"

# 清理临时文件
echo "清理临时文件..."
rm $localTarFile

echo "操作完成！"
