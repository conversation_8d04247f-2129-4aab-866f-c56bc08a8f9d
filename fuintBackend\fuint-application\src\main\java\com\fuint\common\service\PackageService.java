package com.fuint.common.service;

import com.fuint.common.dto.PackageGroupDto;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.repository.model.MtPackageGroup;
import com.fuint.repository.model.MtPackageGroupItem;

import java.lang.reflect.InvocationTargetException;
import java.util.List;
import java.util.Map;

/**
 * 套餐服务接口
 */
public interface PackageService {

    /**
     * 根据套餐商品ID获取套餐分组列表
     *
     * @param goodsId 套餐商品ID
     * @return
     */
    List<PackageGroupDto> getPackageGroupsByGoodsId(Integer goodsId) throws BusinessCheckException, InvocationTargetException, IllegalAccessException;

    /**
     * 修改或添加套餐分组
     *
     * @param packageGroup
     * @return
     */
    MtPackageGroup savePackageGroup(MtPackageGroup packageGroup) throws BusinessCheckException;

    /**
     * 根据ID获取套餐分组
     *
     * @param id 套餐分组ID
     * @return
     */
    MtPackageGroup getPackageGroupById(Integer id);

    /**
     * 删除套餐分组
     *
     * @param id 套餐分组ID
     * @param operator 操作人
     * @return
     */
    void deletePackageGroup(Integer id, String operator) throws BusinessCheckException;
    
    /**
     * 修改或添加套餐分组商品
     *
     * @param packageGroupItem
     * @return
     */
    MtPackageGroupItem savePackageGroupItem(MtPackageGroupItem packageGroupItem) throws BusinessCheckException;

    /**
     * 根据ID获取套餐分组商品
     *
     * @param id 套餐分组商品ID
     * @return
     */
    MtPackageGroupItem getPackageGroupItemById(Integer id);

    /**
     * 删除套餐分组商品
     *
     * @param id 套餐分组商品ID
     * @param operator 操作人
     * @return
     */
    void deletePackageGroupItem(Integer id, String operator) throws BusinessCheckException;
    
    /**
     * 批量保存套餐数据
     * 
     * @param goodsId 套餐商品ID
     * @param groups 分组数据
     * @param operator 操作人
     * @return
     */
    boolean batchSavePackageData(Integer goodsId, List<Map<String, Object>> groups, String operator) throws BusinessCheckException;

    /**
     * 根据查询参数获取套餐分组列表
     *
     * @param params
     * @return
     */
    List<MtPackageGroup> queryPackageGroupList(Map<String, Object> params);
    
    /**
     * 根据查询参数获取套餐分组商品列表
     *
     * @param params
     * @return
     */
    List<MtPackageGroupItem> queryPackageGroupItemList(Map<String, Object> params);
}