<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtGoodsTimeConfigMapper">

    <!-- 根据商品ID获取时间段配置 -->
    <select id="getTimeConfigByGoodsId" resultType="com.fuint.repository.model.MtGoodsTimeConfig">
        SELECT
            id,
            merchant_id as merchantId,
            store_id as storeId,
            goods_id as goodsId,
            config_type as configType,
            week_day as weekDay,
            specify_date as specifyDate,
            start_time as startTime,
            end_time as endTime,
            status,
            create_time as createTime,
            update_time as updateTime,
            operator
        FROM mt_goods_time_config
        WHERE goods_id = #{goodsId}
        AND status = 'A'
        ORDER BY id ASC
    </select>

    <!-- 根据商品ID删除时间段配置 -->
    <delete id="deleteByGoodsId">
        DELETE FROM mt_goods_time_config
        WHERE goods_id = #{goodsId}
    </delete>

    <!-- 根据商品ID和当前时间获取有效的时间段配置 -->
    <select id="getValidTimeConfig" resultType="com.fuint.repository.model.MtGoodsTimeConfig">
        SELECT
            id,
            merchant_id as merchantId,
            store_id as storeId,
            goods_id as goodsId,
            config_type as configType,
            week_day as weekDay,
            specify_date as specifyDate,
            start_time as startTime,
            end_time as endTime,
            status,
            create_time as createTime,
            update_time as updateTime,
            operator
        FROM mt_goods_time_config
        WHERE goods_id = #{goodsId}
        AND status = 'A'
        AND (
            (config_type = '1') OR
            (config_type = '2' AND week_day = #{weekDay}) OR
            (config_type = '3' AND specify_date = #{specifyDate})
        )
        AND (
            (start_time &lt;= end_time AND #{currentTime} &gt;= start_time AND #{currentTime} &lt;= end_time) OR
            (start_time &gt; end_time AND (#{currentTime} &gt;= start_time OR #{currentTime} &lt;= end_time))
        )
        ORDER BY id ASC
    </select>
    
    <!-- 根据配置ID获取时间段配置 -->
    <select id="getTimeConfigById" resultType="com.fuint.repository.model.MtGoodsTimeConfig">
        SELECT
            id,
            merchant_id as merchantId,
            store_id as storeId,
            goods_id as goodsId,
            config_type as configType,
            week_day as weekDay,
            specify_date as specifyDate,
            start_time as startTime,
            end_time as endTime,
            status,
            create_time as createTime,
            update_time as updateTime,
            operator
        FROM mt_goods_time_config
        WHERE id = #{id}
        AND status = 'A'
    </select>

</mapper>