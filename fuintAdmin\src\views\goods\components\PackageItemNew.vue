<template>
  <div class="package-item">
    <div class="package-item-head">
      <el-button type="primary" size="mini" @click="addPackageGroup">添加分组</el-button>
    </div>

    <!-- 分组列表 -->
    <div class="package-group-list">
      <div class="package-group" v-for="(group, groupIndex) in packageGroups" :key="groupIndex">
        <div class="package-group-header">
          <div class="group-info">
            <el-input size="small" v-model="group.groupName" placeholder="分组名称" class="group-name-input"></el-input>
            <el-select size="small" v-model="group.groupType" class="group-type-select">
              <el-option label="必选组" value="R"></el-option>
              <el-option label="可选组" value="O"></el-option>
            </el-select>
            <template v-if="group.groupType === 'O'">
              <span class="select-count-label">选择数量：</span>
              <el-input-number size="small" v-model="group.selectCount" :min="1" :max="10" class="select-count-input"></el-input-number>
            </template>
          </div>
          <div class="group-actions">
            <span class="group-sort-label">排序值：</span>
            <el-input-number size="small" v-model="group.sort" :min="0" class="group-sort-input"></el-input-number>
            <el-button size="small" type="danger" @click="removeGroup(groupIndex)">删除分组</el-button>
          </div>
        </div>

        <!-- 添加商品按钮 -->
        <div class="package-item-add">
          <el-button size="small" type="primary" @click="openGoodsSelector(groupIndex)">添加商品</el-button>
        </div>

        <!-- 商品列表 -->
        <el-table border :data="group.items">
          <el-table-column label="序号" align="center" width="60">
            <template slot-scope="scope">
              <span>{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>
          <el-table-column label="商品图片" align="center" width="80">
            <template slot-scope="scope">
              <img v-if="scope.row.itemGoods && scope.row.itemGoods.logo" :src="  scope.row.itemGoods.logo" class="item-img">
              <div v-else class="no-img">无图片</div>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" align="center" prop="itemGoods.name">
            <template slot-scope="scope">
              <span>{{ scope.row.itemGoods ? scope.row.itemGoods.name : '' }}</span>
            </template>
          </el-table-column>
          <el-table-column v-if="group.groupType === 'O'" label="额外加价" align="center" width="150">
            <template slot-scope="scope">
              <el-input-number
                size="small"
                v-model="scope.row.extraPrice"
                :min="0"
                :precision="2"
                :step="0.1"
              ></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="排序" align="center" width="150">
            <template slot-scope="scope">
              <el-input-number size="small" v-model="scope.row.sort" :min="0"></el-input-number>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" width="80">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="removeItem(groupIndex, scope.$index)">删除</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>

    <!-- 商品选择对话框 -->
    <el-dialog title="选择商品" :visible.sync="goodsSelectorVisible" width="800px">
      <div class="goods-selector">
        <div class="goods-selector-search">
          <el-input v-model="goodsSearch.name" placeholder="商品名称" class="search-input"></el-input>
          <!-- <el-select v-model="goodsSearch.type" placeholder="商品类型" clearable class="search-select">
            <el-option v-for="item in typeOptions" :key="item.key" :label="item.name" :value="item.key"></el-option>
          </el-select> -->
          <el-button type="primary" size="small" @click="searchGoods">搜索</el-button>
        </div>

        <el-table
          border
          :data="goodsList"
          @selection-change="handleGoodsSelectionChange"
        >
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column label="商品图片" align="center" width="80">
            <template slot-scope="scope">
              <img v-if="scope.row.logo" :src="uploadDomain + scope.row.logo" class="item-img">
              <div v-else class="no-img">无图片</div>
            </template>
          </el-table-column>
          <el-table-column label="商品名称" align="center" prop="name"></el-table-column>
          <el-table-column label="商品类型" align="center" width="100">
            <template slot-scope="scope">
              <span>{{ getGoodsTypeName(scope.row.type) }}</span>
            </template>
          </el-table-column>
          <el-table-column label="价格" align="center" width="100">
            <template slot-scope="scope">
              <span>¥{{ scope.row.price }}</span>
            </template>
          </el-table-column>
        </el-table>

        <div class="goods-selector-footer">
          <el-pagination
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="goodsSearch.page"
            :page-sizes="[10, 20, 50, 100]"
            :page-size="goodsSearch.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="goodsTotal">
          </el-pagination>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="goodsSelectorVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelectGoods">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getGoodsList } from "@/api/goods";
import { getToken } from '@/utils/auth'

export default {
  name: "PackageItemNew",
  props: {
    // 套餐商品ID
    goodsId: {
      type: [String, Number],
      default: 0
    },
    // 上传文件域
    uploadDomain: {
      type: String,
      default: ''
    },
    // 商品类型选项
    typeOptions: {
      type: Array,
      default: () => []
    },
    // 已有的分组数据
    packageData: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      // 分组列表
      packageGroups: [],
      // 上传头部
      uploadHeader: { 'Access-Token': getToken() },
      // 商品选择器可见性
      goodsSelectorVisible: false,
      // 当前编辑的分组索引
      currentGroupIndex: -1,
      // 已选择的商品
      selectedGoods: [],
      // 商品列表
      goodsList: [],
      // 商品总数
      goodsTotal: 0,
      // 商品搜索条件
      goodsSearch: {
        name: '',
        type: 'goods',
        page: 1,
        pageSize: 10
      }
    };
  },
  watch: {
    // 监听套餐数据变化
    packageData: {
      handler(newVal) {
        if (newVal && newVal.length > 0) {
          this.initPackageGroups();
        }
      },
      deep: false
    }
  },
  methods: {
    // 初始化套餐分组
    initPackageGroups() {
      this.packageGroups = this.packageData.map(group => ({
        id: group.id,
        goodsId: group.goodsId,
        groupName: group.groupName,
        groupType: group.groupType,
        selectCount: group.selectCount,
        sort: group.sort,
        items: group.items || []
      }));
    },
    // 添加套餐分组
    addPackageGroup() {
      this.packageGroups.push({
        id: 0,
        goodsId: this.goodsId,
        groupName: '分组' + (this.packageGroups.length + 1),
        groupType: 'R', // 默认为必选组
        selectCount: 1,
        sort: this.packageGroups.length,
        items: []
      });
    },
    // 删除分组
    removeGroup(groupIndex) {
      this.$confirm('确定要删除该分组吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.packageGroups.splice(groupIndex, 1);
        this.$emit('change', this.packageGroups);
      }).catch(() => {});
    },
    // 打开商品选择器
    openGoodsSelector(groupIndex) {
      this.currentGroupIndex = groupIndex;
      this.selectedGoods = [];
      this.goodsSelectorVisible = true;
      this.searchGoods();
    },
    // 搜索商品
    searchGoods() {
      const params = {
        ...this.goodsSearch,
        page: 1,
        status: 'A', // 只查询上架的商品
        type: 'goods' // 只查询实物商品
      };

      getGoodsList(params).then(response => {
        const data = response.data;
        this.goodsList = data.paginationResponse.content;
        this.goodsTotal = data.paginationResponse.totalElements;

        this.goodsSearch.page = 1;
      });
    },
    // 商品选择变更
    handleGoodsSelectionChange(selection) {
      this.selectedGoods = selection;
    },
    // 确认选择商品
    confirmSelectGoods() {
      if (this.selectedGoods.length === 0) {
        this.$message.warning('请选择至少一个商品');
        return;
      }

      // 获取当前分组
      const currentGroup = this.packageGroups[this.currentGroupIndex];

      // 添加商品到分组
      this.selectedGoods.forEach(goods => {
        // 检查是否已存在
        const exists = currentGroup.items.some(item =>
          item.itemGoodsId === goods.id
        );

        if (!exists) {
          currentGroup.items.push({
            id: 0,
            goodsId: this.goodsId,
            groupId: currentGroup.id || 0,
            itemGoodsId: goods.id,
            itemGoods: goods,
            quantity: 1,
            extraPrice: 0,
            sort: currentGroup.items.length
          });
        }
      });

      // 关闭对话框
      this.goodsSelectorVisible = false;

      // 通知父组件数据变更
      this.$emit('change', this.packageGroups);
    },
    // 删除商品项
    removeItem(groupIndex, itemIndex) {
      this.packageGroups[groupIndex].items.splice(itemIndex, 1);
      this.$emit('change', this.packageGroups);
    },
    // 获取商品类型名称
    getGoodsTypeName(type) {
      const found = this.typeOptions.find(option => option.key === type);
      return found ? found.name : type;
    },
    // 分页大小变更
    handleSizeChange(val) {
      this.goodsSearch.pageSize = val;
      this.searchGoods();
    },
    // 分页变更
    handleCurrentChange(val) {
      this.goodsSearch.page = val;
      this.searchGoods();
    },
    // 获取套餐数据（供父组件使用）
    getPackageData() {
      return this.packageGroups;
    }
  }
};
</script>

<style scoped>
.package-item {
  padding: 10px 0;
}

.package-item-head {
  margin-bottom: 15px;
}

.package-group {
  margin-bottom: 30px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 15px;
}

.package-group-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  align-items: center;
}

.group-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.group-name-input {
  width: 180px;
}

.group-type-select {
  width: 100px;
}

.select-count-label {
  margin-left: 10px;
  font-size: 14px;
}

.select-count-input {
  width: 120px;
}

.group-actions {
  display: flex;
  align-items: center;
}

.group-sort-label {
  margin-right: 5px;
}

.group-sort-input {
  width: 120px;
  margin-right: 15px;
}

.package-item-add {
  margin-bottom: 15px;
}

.item-img {
  height: 40px;
  width: 40px;
  object-fit: cover;
}

.no-img {
  height: 40px;
  width: 40px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #f5f7fa;
  color: #909399;
  font-size: 12px;
}

.goods-selector-search {
  display: flex;
  margin-bottom: 15px;
}

.search-input {
  width: 200px;
  margin-right: 10px;
}

.search-select {
  width: 150px;
  margin-right: 10px;
}

.goods-selector-footer {
  margin-top: 15px;
  text-align: center;
}
</style>
