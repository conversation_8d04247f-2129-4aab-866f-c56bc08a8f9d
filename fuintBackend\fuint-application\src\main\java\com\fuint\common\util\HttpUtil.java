package com.fuint.common.util;

import org.apache.http.HttpEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.nio.charset.StandardCharsets;

/**
 * HTTP请求工具类
 */
public class HttpUtil {
    private static final Logger logger = LoggerFactory.getLogger(HttpUtil.class);

    /**
     * 发送POST请求
     *
     * @param url 请求URL
     * @param jsonParams JSON格式的请求参数
     * @return 响应结果
     * @throws Exception
     */
    public static String doPost(String url, String jsonParams) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        CloseableHttpResponse response = null;
        String result = null;

        try {
            HttpPost httpPost = new HttpPost(url);
            httpPost.addHeader("Content-Type", "application/json;charset=UTF-8");
            
            // 设置请求参数
            if (jsonParams != null && !jsonParams.isEmpty()) {
                StringEntity entity = new StringEntity(jsonParams, StandardCharsets.UTF_8);
                entity.setContentType("application/json;charset=UTF-8");
                httpPost.setEntity(entity);
            }

            // 执行请求
            response = httpClient.execute(httpPost);
            int statusCode = response.getStatusLine().getStatusCode();
            
            if (statusCode == 200) {
                HttpEntity entity = response.getEntity();
                result = EntityUtils.toString(entity, StandardCharsets.UTF_8);
            } else {
                throw new Exception("HTTP请求失败，状态码：" + statusCode);
            }
        } catch (Exception e) {
            logger.error("HTTP请求异常", e);
            throw e;
        } finally {
            // 关闭资源
            if (response != null) {
                try {
                    response.close();
                } catch (Exception e) {
                    logger.error("关闭HTTP响应失败", e);
                }
            }
            if (httpClient != null) {
                try {
                    httpClient.close();
                } catch (Exception e) {
                    logger.error("关闭HTTP客户端失败", e);
                }
            }
        }

        return result;
    }
}