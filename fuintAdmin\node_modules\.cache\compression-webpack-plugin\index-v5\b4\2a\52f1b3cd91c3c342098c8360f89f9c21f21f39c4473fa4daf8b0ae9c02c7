
7100789a2afe1e6abf508562837c8688d67f1d40	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"c9bf5a6c9b606d2c7d116152a53e50a1\"}","integrity":"sha512-xW89moQ5PGzTcI5NaTlmBnfluld9zcdBoTo0fpTLOk1G/aug8QSTGyeKHlx9LvQ+nph3BUw8A3RE/6IJowcO6A==","time":1755006129043,"size":2882484}