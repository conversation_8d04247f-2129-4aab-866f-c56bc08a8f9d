# 编译输出
/build/
*/build/

# 局部配置文件
/local.properties

# 日志文件
*.log

# 操作系统生成的文件
.DS_Store
Thumbs.db

# Android Studio
.idea/
*.iml
.gradle/
*.hprof

# 用户相关文件
/.idea/workspace.xml
/.idea/libraries/
.idea/modules.xml
.idea/.name
.idea/misc.xml
.idea/vcs.xml
.idea/dictionaries/
.idea/caches/
.idea/httpRequests/

# Keystore files
#*.jks
#*.keystore

# Gradle Wrapper 缓存
!gradle/wrapper/gradle-wrapper.jar

# 文件结尾符配置
*.orig

# Crashlytics 自动生成的文件
com_crashlytics_export_strings.xml

# NDK 编译
/cmake-build-debug/

# Kotlin 编译输出
*.kotlin_module
.kotlin/

# 无用的 jar 包
*.jar

# Android Lint 输出
lint/intermediates/
lint/generated/

# 编译时生成的 JSON 文件
/.cxx/

# Release 输出的 APK/AAB 文件
app/release
app/debug
app/build
*.apk
*.aab

# IntelliJ IDEA 相关文件
out/

# 检查器结果
.idea/inspectionProfiles

# Test result
/test-results/

# Android studio 自动生成的签名配置文件
signing/

# Git 合并冲突生成的文件
*.orig

# Local build cache
.cxx/
.cmake/
