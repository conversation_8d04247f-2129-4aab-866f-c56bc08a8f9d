import request from '@/utils/request'

// 分页查询商品列表
export function getGoodsList(query) {
  return request({
      url: 'backendApi/goods/goods/list',
      method: 'get',
      params: query
  })
}

export function copyGoods(ids, targetStoreId) {
  return request({
      url: 'backendApi/goods/goods/copy',
      method: 'post',
      data: { ids, targetStoreId }
  })
}

// 获取套餐商品项目列表
export function getPackageItems(goodsId) {
  return request({
    url: 'backendApi/goods/package/list/' + goodsId,
    method: 'get'
  })
}

// 保存套餐商品项目
export function savePackageItems(data) {
  return request({
    url: 'backendApi/goods/package/batchSave',
    method: 'post',
    data: data
  })
}

// 保存套餐分组
export function savePackageGroup(data) {
  return request({
    url: 'backendApi/goods/package/saveGroup',
    method: 'post',
    data: data
  })
}

// 保存套餐分组商品
export function savePackageGroupItem(data) {
  return request({
    url: 'backendApi/goods/package/saveGroupItem',
    method: 'post',
    data: data
  })
}

// 删除套餐分组
export function deletePackageGroup(id) {
  return request({
    url: 'backendApi/goods/package/deleteGroup/' + id,
    method: 'get'
  })
}

// 删除套餐分组商品
export function deletePackageGroupItem(id) {
  return request({
    url: 'backendApi/goods/package/deleteGroupItem/' + id,
    method: 'get'
  })
}

// 查询商品详情
export function getGoodsInfo(goodsId) {
  return request({
    url: 'backendApi/goods/goods/info/' + goodsId,
    method: 'get'
  })
}

// 更新状态
export function updateGoodsStatus(id, status) {
  const data = {
    id,
    status
  }
  return request({
      url: 'backendApi/goods/goods/updateStatus',
      method: 'post',
      data: data
  })
}

// 保存分类数据
export function saveGoods(data) {
  return request({
      url: 'backendApi/goods/goods/save',
      method: 'post',
      data: data
  })
}

// 保存商品规格名称
export function saveSpecName(data) {
  return request({
      url: 'backendApi/goods/goods/saveSpecName',
      method: 'post',
      data: data
  })
}

// 保存商品规格值
export function saveSpecValue(data) {
  return request({
      url: 'backendApi/goods/goods/saveSpecValue',
      method: 'post',
      data: data
  })
}
// 修改商品规格排序
export function saveSpecSort(data) {
  return request({
      url: 'backendApi/goods/goods/saveSpecSort',
      method: 'post',
      data: data
  })
}

// 删除商品规格
export function deleteSpec(query) {
  return request({
      url: 'backendApi/goods/goods/deleteSpec',
      method: 'get',
      params: query
  })
}

// 删除商品规格值
export function deleteSpecValue(query) {
  return request({
      url: 'backendApi/goods/goods/deleteSpecValue',
      method: 'get',
      params: query
  })
}

// 获取选择商品列表
export function selectGoodsList(data) {
  return request({
     url: 'backendApi/goods/goods/selectGoods',
     method: 'post',
     data: data
  })
}
