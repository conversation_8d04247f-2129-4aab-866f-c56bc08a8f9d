-- 商品时间段配置功能数据库迁移脚本
-- 创建时间：2025-08-10
-- 作者：系统开发团队

-- 1. 扩展商品表，添加时间段配置相关字段
ALTER TABLE `mt_goods` 
ADD COLUMN `enable_time_config` char(1) DEFAULT 'N' COMMENT '是否启用时间段配置 Y:启用 N:不启用' AFTER `source_id`,
ADD COLUMN `time_config_type` char(1) DEFAULT '1' COMMENT '时间段配置类型 1:每日通用 2:按周设置 3:自定义日期' AFTER `enable_time_config`;

-- 2. 创建商品时间段配置表
CREATE TABLE IF NOT EXISTS `mt_goods_time_config` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `merchant_id` int(11) DEFAULT '0' COMMENT '商户ID',
  `store_id` int(11) DEFAULT '0' COMMENT '店铺ID',
  `goods_id` int(11) NOT NULL COMMENT '商品ID',
  `config_type` char(1) NOT NULL COMMENT '配置类型 1:每日通用 2:按周设置 3:自定义日期',
  `week_day` tinyint(4) DEFAULT NULL COMMENT '星期几(1-7对应周一到周日，当config_type=2时使用)',
  `specify_date` varchar(10) DEFAULT NULL COMMENT '指定日期(yyyy-MM-dd格式，当config_type=3时使用)',
  `start_time` varchar(5) NOT NULL COMMENT '开始时间(HH:mm格式)',
  `end_time` varchar(5) NOT NULL COMMENT '结束时间(HH:mm格式)',
  `status` char(1) DEFAULT 'A' COMMENT '状态 A:有效 D:无效',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `operator` varchar(30) DEFAULT NULL COMMENT '最后操作人',
  PRIMARY KEY (`id`),
  KEY `idx_goods_id` (`goods_id`),
  KEY `idx_merchant_store` (`merchant_id`, `store_id`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商品时间段配置表';

-- 3. 添加索引优化查询性能
CREATE INDEX idx_goods_time_config_goods_id ON mt_goods_time_config(goods_id);
CREATE INDEX idx_goods_time_config_merchant_store ON mt_goods_time_config(merchant_id, store_id);
CREATE INDEX idx_goods_time_config_type ON mt_goods_time_config(config_type);
CREATE INDEX idx_goods_time_config_status ON mt_goods_time_config(status);

-- 4. 添加外键约束（可选，根据实际业务需求）
-- ALTER TABLE `mt_goods_time_config` 
-- ADD CONSTRAINT `fk_goods_time_config_goods` FOREIGN KEY (`goods_id`) REFERENCES `mt_goods` (`id`) ON DELETE CASCADE;

-- 5. 插入示例数据（可选，用于测试）
-- INSERT INTO `mt_goods_time_config` (`merchant_id`, `store_id`, `goods_id`, `config_type`, `week_day`, `start_time`, `end_time`, `status`, `operator`) VALUES
-- (1, 1, 1, '1', NULL, '09:00', '21:00', 'A', 'admin'),
-- (1, 1, 2, '2', 1, '08:00', '12:00', 'A', 'admin'),
-- (1, 1, 2, '2', 1, '14:00', '18:00', 'A', 'admin'),
-- (1, 1, 3, '3', NULL, '2025-08-10', '10:00', '20:00', 'A', 'admin');

-- 6. 更新现有商品数据（可选，将所有商品默认设置为不启用时间段配置）
UPDATE `mt_goods` SET `enable_time_config` = 'N', `time_config_type` = '1' WHERE `enable_time_config` IS NULL;

-- 7. 添加字段注释
ALTER TABLE `mt_goods` 
MODIFY COLUMN `enable_time_config` char(1) DEFAULT 'N' COMMENT '是否启用时间段配置 Y:启用 N:不启用',
MODIFY COLUMN `time_config_type` char(1) DEFAULT '1' COMMENT '时间段配置类型 1:每日通用 2:按周设置 3:自定义日期';