package com.fuint.common.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.List;

/**
 * 商品规格项实体
 *

 */
@Getter
@Setter
public class GoodsSpecItemDto implements Serializable {

    @ApiModelProperty("自增ID")
    private Integer id;

    @ApiModelProperty("规格名称")
    private String name;

    
    @ApiModelProperty("排序")
    private Integer sort;

    @ApiModelProperty("规格子类")
    private List<GoodsSpecChildDto> child;

}

