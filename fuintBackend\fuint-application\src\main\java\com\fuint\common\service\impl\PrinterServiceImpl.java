package com.fuint.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuint.common.dto.GoodsSpecValueDto;
import com.fuint.common.dto.OrderDto;
import com.fuint.common.dto.OrderGoodsDto;
import com.fuint.common.dto.UserOrderDto;
import com.fuint.common.enums.*;
import com.fuint.common.service.SettingService;
import com.fuint.common.util.DateUtil;
import com.fuint.common.util.HashSignUtil;
import com.fuint.common.util.NoteFormatter;
import com.fuint.common.util.PrinterUtil;
import com.fuint.common.vo.printer.*;
import com.fuint.framework.annoation.OperationServiceLog;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.framework.pagination.PaginationRequest;
import com.fuint.framework.pagination.PaginationResponse;
import com.fuint.repository.model.MtGoods;
import com.fuint.repository.model.MtGoodsSku;
import com.fuint.repository.model.MtOrder;
import com.fuint.repository.model.MtPrinter;
import com.fuint.repository.model.MtPrinterCate;
import com.fuint.common.service.GoodsService;
import com.fuint.common.service.HuifuPayService;
import com.fuint.common.service.OrderService;
import com.fuint.common.service.PrinterService;
import com.fuint.common.service.PrinterCateService;
import com.fuint.repository.mapper.MtGoodsMapper;
import com.fuint.repository.mapper.MtGoodsSkuMapper;
import com.fuint.repository.mapper.MtGoodsSpecMapper;
import com.fuint.repository.mapper.MtPrinterMapper;
import com.fuint.repository.model.MtSetting;
import com.fuint.repository.model.MtStore;
import com.fuint.utils.StringUtil;
import com.github.pagehelper.PageHelper;
import lombok.AllArgsConstructor;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.github.pagehelper.Page;
import org.springframework.beans.BeanUtils;
import org.springframework.core.env.Environment;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import java.util.*;
import java.util.stream.Collectors;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;

/**
 * 打印机服务接口
 *

 */
@Service
@AllArgsConstructor
public class PrinterServiceImpl extends ServiceImpl<MtPrinterMapper, MtPrinter> implements PrinterService {

    private static final Logger logger = LoggerFactory.getLogger(PrinterServiceImpl.class);

    private MtPrinterMapper mtPrinterMapper;

    /**
     * 系统配置服务接口
     * */
    private SettingService settingService;

    /**
     * 环境变量
     * */
    private Environment env;
 
    private MtGoodsMapper mtGoodsMapper;

    private MtGoodsSpecMapper mtGoodsSpecMapper;

    private MtGoodsSkuMapper mtGoodsSkuMapper;

    private OrderService orderService;

    private HuifuPayService huifuPayService;

    private PrinterCateService printerCateService;

    /**
     * 分页查询数据列表
     *
     * @param paginationRequest
     * @return
     */
    @Override
    public PaginationResponse<MtPrinter> queryPrinterListByPagination(PaginationRequest paginationRequest) {
        Page<MtPrinter> pageHelper = PageHelper.startPage(paginationRequest.getCurrentPage(), paginationRequest.getPageSize());
        LambdaQueryWrapper<MtPrinter> lambdaQueryWrapper = Wrappers.lambdaQuery();
        lambdaQueryWrapper.ne(MtPrinter::getStatus, StatusEnum.DISABLE.getKey());

        String status =  paginationRequest.getSearchParams().get("status") == null ? "" : paginationRequest.getSearchParams().get("status").toString();
        if (StringUtils.isNotBlank(status)) {
            lambdaQueryWrapper.eq(MtPrinter::getStatus, status);
        }
        String merchantId =  paginationRequest.getSearchParams().get("merchantId") == null ? "" : paginationRequest.getSearchParams().get("merchantId").toString();
        if (StringUtils.isNotBlank(merchantId)) {
            lambdaQueryWrapper.eq(MtPrinter::getMerchantId, merchantId);
        }
        String storeId =  paginationRequest.getSearchParams().get("storeId") == null ? "" : paginationRequest.getSearchParams().get("storeId").toString();
        if (StringUtils.isNotBlank(storeId)) {
            lambdaQueryWrapper.and(wq -> wq
                    // .eq(MtPrinter::getStoreId, 0)
                    // .or()
                    .eq(MtPrinter::getStoreId, storeId));
        }
        String sn =  paginationRequest.getSearchParams().get("sn") == null ? "" : paginationRequest.getSearchParams().get("sn").toString();
        if (StringUtils.isNotBlank(sn)) {
            lambdaQueryWrapper.eq(MtPrinter::getSn, sn);
        }
        String name = paginationRequest.getSearchParams().get("name") == null ? "" : paginationRequest.getSearchParams().get("name").toString();
        if (StringUtils.isNotBlank(name)) {
            lambdaQueryWrapper.eq(MtPrinter::getName, name);
        }
        String autoPrint = paginationRequest.getSearchParams().get("autoPrint") == null ? "" : paginationRequest.getSearchParams().get("autoPrint").toString();
        if (StringUtils.isNotBlank(autoPrint)) {
            lambdaQueryWrapper.eq(MtPrinter::getAutoPrint, autoPrint);
        }

        lambdaQueryWrapper.orderByAsc(MtPrinter::getId);
        List<MtPrinter> dataList = mtPrinterMapper.selectList(lambdaQueryWrapper);

        Sort sort = Sort.by(Sort.Direction.ASC, "id");
        
        PageRequest pageRequest = PageRequest.of(paginationRequest.getCurrentPage(), paginationRequest.getPageSize(),sort);
        PageImpl pageImpl = new PageImpl(dataList, pageRequest, pageHelper.getTotal());
        PaginationResponse<MtPrinter> paginationResponse = new PaginationResponse(pageImpl, MtPrinter.class);
        paginationResponse.setTotalPages(pageHelper.getPages());
        paginationResponse.setTotalElements(pageHelper.getTotal());
        paginationResponse.setContent(dataList);

        return paginationResponse;
    }

    /**
     * 添加打印机
     *
     * @param mtPrinter 打印机信息
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationServiceLog(description = "新增打印机")
    public MtPrinter addPrinter(MtPrinter mtPrinter) throws BusinessCheckException {
        mtPrinter.setStatus(StatusEnum.ENABLED.getKey());
        mtPrinter.setUpdateTime(new Date());
        mtPrinter.setCreateTime(new Date());
        if (mtPrinter.getMerchantId() == null || mtPrinter.getMerchantId() < 1) {
            throw new BusinessCheckException("平台方帐号无法执行该操作，请使用商户帐号操作");
        }

        Integer printerId = mtPrinterMapper.insert(mtPrinter);
        if (printerId > 0) {
            // 添加云打印机
            if (mtPrinter.getSn() != null && mtPrinter.getName() != null) {
                AddPrinterRequest restRequest = new AddPrinterRequest();
                createRequestHeader(mtPrinter.getMerchantId(), restRequest);
                AddPrinterRequestItem item = new AddPrinterRequestItem();
                item.setName(mtPrinter.getName());
                item.setSn(mtPrinter.getSn());
                AddPrinterRequestItem[] items = { item };
                restRequest.setItems(items);
                PrinterUtil.addPrinters(restRequest);
            }
            return mtPrinter;
        } else {
            logger.error("新增打印机数据失败.");
            throw new BusinessCheckException("新增打印机数据失败");
        }
    }

    /**
     * 打印订单
     *
     * @param orderInfo 订单信息
     * @param autoPrint 自动打印
     * @param beforePay 支付前打印
     * @param afterPay 支付后打印
     * @param goodsIds 打印的商品Id
     * @return
     * */
    @Override
    public Boolean printOrder(UserOrderDto orderInfo, boolean autoPrint, boolean beforePay, boolean afterPay, List<Integer> goodsIds) throws Exception {
        return printOrder(orderInfo, autoPrint, beforePay, afterPay, goodsIds, true);
    }

    /**
     * 打印订单（可控制是否打印标签）
     *
     * @param orderInfo 订单信息
     * @param autoPrint 自动打印
     * @param beforePay 支付前打印
     * @param afterPay 支付后打印
     * @param goodsIds 打印的商品Id
     * @param printLabel 是否同时打印标签
     * @return
     * */
    @Override
    public Boolean printOrder(UserOrderDto orderInfo, boolean autoPrint, boolean beforePay, boolean afterPay, List<Integer> goodsIds, boolean printLabel) throws Exception {
        PrintRequest printRequest = new PrintRequest();
        createRequestHeader(orderInfo.getMerchantId(), printRequest);
        if (orderInfo.getStoreInfo() == null) {
            return false;
        }

        // 获取打印机列表
        Map<String, Object> params = new HashMap<>();
        params.put("storeId", orderInfo.getStoreInfo().getId());
        params.put("status", StatusEnum.ENABLED.getKey());
        params.put("type", PrinterTypeEnum.RECEIPT.getKey());

        if (autoPrint) {
            params.put("autoPrint", YesOrNoEnum.YES.getKey());
        }

        if (beforePay) {
            params.put("beforePay", YesOrNoEnum.YES.getKey());
        } else if (afterPay) {
            params.put("afterPay", YesOrNoEnum.YES.getKey());
        } 

        List<MtPrinter> printers = queryPrinterListByParams(params);
        if (printers == null || printers.size() < 1) {
            return false;
        }
        
        MtOrder orderDto = orderService.getOrderInfo(orderInfo.getId());
        
        String fapiaoQrCodeUrl = huifuPayService.getFaPiaoQrCode(orderInfo.getOrderSn());

        MtStore storeInfo = orderInfo.getStoreInfo();
        for (MtPrinter mtPrinter : printers) {
            printRequest.setSn(mtPrinter.getSn());

            StringBuilder printContent = new StringBuilder();

            if(orderInfo.getPickupCode() != null && orderInfo.getPickupCode().length() > 0) {
                printContent.append("<C>").append("<B>取餐号:" + orderInfo.getPickupCode().toString() + "</B>").append("<BR></C>");
            }
            // printContent.append("<C>").append("<B>" + storeInfo.getName() + "</B>").append("<BR></C>");
            // printContent.append("<BR>");

            // 分割线
            printContent.append(org.apache.commons.lang3.StringUtils.repeat("-", 32)).append("<BR>");
            
            // 条形码
            printContent.append("<C><BARCODE t=CODE128 w=2 h=100 p=2>").append(orderInfo.getOrderSn()).append("</BARCODE></C>");

            // 单号桌码
            // printContent.append("<L>订单号：").append(orderInfo.getOrderSn()).append("</L>");
            
            if (orderInfo.getTableInfo() != null && StringUtil.isNotEmpty(orderInfo.getTableInfo().getCode())) {
                printContent.append("<R><BOLD>桌码:").append(orderInfo.getTableInfo().getCode()).append("</BOLD></R>");
            }

            // 订单状态
            String payStatus = PayStatusEnum.WAIT.getValue();
            if (orderInfo.getPayStatus().equals(PayStatusEnum.SUCCESS.getKey())) {
                payStatus = PayStatusEnum.SUCCESS.getValue();
            }
            printContent.append("<BR>");
            printContent.append("<L>").append("支付状态：").append(payStatus).append("<BR></L>");

            // 分割线
            printContent.append(org.apache.commons.lang3.StringUtils.repeat("-", 32)).append("<BR>");

            
            printContent.append("<LINE p=\"20,26\" />");
            printContent.append("品名<HT>数量<HT>单价<BR>"); 
            // 分割线
            printContent.append(org.apache.commons.lang3.StringUtils.repeat("-", 32)).append("<BR>");

            // 商品列表
            if (orderInfo.getGoods() != null && orderInfo.getGoods().size() > 0) {
                for (OrderGoodsDto goodsDto : orderInfo.getGoods()) {   
                    if (goodsIds != null && goodsIds.size() > 0) {
                        if (goodsIds.contains(goodsDto.getGoodsId())) {
                           printContent.append(goodsDto.getName()).append("<HT>").append(goodsDto.getNum()).append("<HT>").append(Double.parseDouble(goodsDto.getPrice())).append("<BR>"); 
                            // printContent.append(NoteFormatter.formatPrintOrderItemForNewLine80(goodsDto.getName() , goodsDto.getNum(), Double.parseDouble(goodsDto.getPrice())));
                        }
                    } else {
                        printContent.append(goodsDto.getName()).append("<HT>").append(goodsDto.getNum()).append("<HT>").append(Double.parseDouble(goodsDto.getPrice())).append("<BR>"); 
                    }
                    if(goodsDto.getSpecList() != null && goodsDto.getSpecList().size() > 0){
                        String specValues = goodsDto.getSpecList().stream().map(GoodsSpecValueDto::getSpecValue).collect(Collectors.joining(","));
                        printContent.append(specValues).append("<HT>").append(" ").append("<HT>").append(" ").append("<BR>"); 
                    }
                }
            }

            if(orderInfo.getDeliveryFee() != null && orderInfo.getDeliveryFee().compareTo(new BigDecimal("0")) > 0 ) {
                printContent.append("配送费").append("<HT>").append("1").append("<HT>").append(orderInfo.getDeliveryFee().toString()).append("<BR>");
            }

            if(orderInfo.getCouponInfo() != null && orderInfo.getCouponInfo().getAmount().compareTo(new BigDecimal("0")) > 0 ) {
                printContent.append("优惠券").append("<HT>").append("1").append("<HT>-").append(orderInfo.getCouponInfo().getAmount().toString()).append("<BR>");
            }

            Integer totalCups = orderInfo.getGoods().stream().mapToInt(OrderGoodsDto::getNum).sum();
            printContent.append("合计").append("<HT>").append(totalCups).append("<HT>").append(orderInfo.getPayAmount()).append("<BR>");

            // 分割线
            printContent.append(org.apache.commons.lang3.StringUtils.repeat("-", 32)).append("<BR>");

            // printContent.append("<R>").append("合计：").append(orderInfo.getPayAmount()).append("元").append("</R><BR>");


            // printContent.append("<BR>");
            printContent.append("<L>")
                    .append("店铺地址：").append((orderInfo.getStoreInfo().getAddress() == null) ? "无" : orderInfo.getStoreInfo().getAddress()).append("<BR>")
                    .append("联系电话：").append((orderInfo.getStoreInfo().getPhone() == null) ? "无" : orderInfo.getStoreInfo().getPhone()).append("<BR>")
                    .append("下单时间：").append(orderInfo.getCreateTime()).append("<BR>")
                    .append("订单备注：").append(StringUtil.isEmpty(orderInfo.getRemark()) ? "无" : orderInfo.getRemark()).append("<BR>");

            // 预约取餐信息
            if (orderDto.getIsReservation() != null && orderDto.getIsReservation().equals("Y")) {
                printContent.append("<B>【预约取餐】</B><BR>");
                if (orderDto.getReservationTime() != null) {
                    String reservationTimeStr = DateUtil.formatDate(orderDto.getReservationTime(), "yyyy-MM-dd HH:mm");
                    printContent.append("预约时间：").append(reservationTimeStr).append("<BR>");
                }
            }

            printContent.append("</L>");
                    // 下单来源

            String platform = orderDto.getPlatform();
            String orderSource = "店内下单";
            if (platform != null ) {
                if (platform.equals(PlatformTypeEnum.MP_WEIXIN.getCode())) {
                    orderSource = "微信小程序下单";
                }
            }

            
            // 分割线
            printContent.append("<L>").append(org.apache.commons.lang3.StringUtils.repeat("-", 32)).append("<BR></L>");
            
            // 配送订单，打印配送信息
            if (orderInfo.getOrderMode().equals(OrderModeEnum.EXPRESS.getKey())) {                
                printContent.append("<BR>");
                printContent.append(org.apache.commons.lang3.StringUtils.repeat("-", 32)).append("<BR>");
                printContent.append("<L>")
                        .append("<B>【配送订单】</B><BR>")
                        .append("配送姓名：").append(orderInfo.getAddress().getName()).append("<BR>")
                        .append("联系电话：").append(orderInfo.getAddress().getMobile()).append("<BR>")
                        .append("详细地址：").append(orderInfo.getAddress().getProvinceName() + orderInfo.getAddress().getCityName() + orderInfo.getAddress().getRegionName() + orderInfo.getAddress().getDetail()).append("<BR></L>");
                        
                // 分割线
                printContent.append("<L>").append(org.apache.commons.lang3.StringUtils.repeat("-", 32)).append("<BR></L>");

            }else if (orderInfo.getOrderMode().equals(OrderModeEnum.ONESELF.getKey()) && !orderInfo.getPayType().equals(PayTypeEnum.EXTERNAL.getKey())) {                   
                String takeMode = orderInfo.getTakeMode();
                printContent.append("<L>").append("到店取餐 "+ takeMode +" <BR></L>");
            }

            if(!orderInfo.getPayType().equals(PayTypeEnum.EXTERNAL.getKey())){
                printContent.append("<L>").append(orderSource).append("<BR></L>");
            }
            
 
            printContent.append("<C><IMG></IMG></C>");

            if (StringUtil.isNotEmpty(fapiaoQrCodeUrl)) {
                printContent.append("<QRCODE s=6 e=L l=center>").append(fapiaoQrCodeUrl).append("</QRCODE>");
                printContent.append("<C>扫码开具电子发票</C>");
            }

            if(orderInfo.getPayType().equals(PayTypeEnum.EXTERNAL.getKey())){
                printContent.append("<CB>外卖 / 团购 ").append(orderInfo.getExternalOrder()).append("</CB>");
            }

            // 网站二维码
            // String webSite = env.getProperty("website.url");
            // if (StringUtil.isNotEmpty(webSite)) {
            //     printContent.append("<C>").append("<QR>" + webSite + "</QR>").append("</C>");
            // }
            // System.out.println(printContent.toString());
            printRequest.setContent(printContent.toString());
            printRequest.setCopies(1);
            printRequest.setVoice(2);
            printRequest.setMode(1);
            PrinterUtil.print(printRequest);
        }

        if (printLabel) {
            printLabel(orderInfo, storeInfo);
        }
        return true;
    }

    /**
     * 打印标签
     *
     * @param orderInfo 订单信息
     * @param storeInfo 店铺信息
     * @return 打印结果
     * */
    Boolean printLabel(UserOrderDto orderInfo, MtStore storeInfo ) throws Exception  {
        MtOrder orderDto = orderService.getOrderInfo(orderInfo.getId());
        PrintRequest printRequest = new PrintRequest();
        createRequestHeader(orderInfo.getMerchantId(), printRequest);
        if (orderInfo.getStoreInfo() == null) {
            return false;
        }
        List<OrderGoodsDto> goods = orderInfo.getGoods();
        if (goods == null || goods.size() == 0) {
            return false;
        }

        // 将goods展开为新的list，每个商品根据num重复
        List<OrderGoodsDto> expandedGoods = new ArrayList<>();
        for (OrderGoodsDto good : goods) {
            for (int i = 0; i < good.getNum(); i++) {
                expandedGoods.add(good);
            }
        }

        Integer totalCups = expandedGoods.size();

        // 按商品分类分组打印
        Map<Integer, List<OrderGoodsDto>> goodsByCate = new HashMap<>();
        for (OrderGoodsDto good : expandedGoods) {
            MtGoods mtGoods = mtGoodsMapper.selectById(good.getGoodsId());
            if (mtGoods != null && mtGoods.getCateId() != null) {
                goodsByCate.computeIfAbsent(mtGoods.getCateId(), k -> new ArrayList<>()).add(good);
            }
        }

        // 为每个分类查找对应的打印机并打印
        for (Map.Entry<Integer, List<OrderGoodsDto>> entry : goodsByCate.entrySet()) {
            Integer cateId = entry.getKey();
            List<OrderGoodsDto> cateGoods = entry.getValue();

            // 先查找配置了该分类的打印机
            List<MtPrinter> categoryPrinters = queryLabelPrintersByCateId(cateId, orderInfo.getStoreInfo().getId());

            // 如果没有找到配置过分类的打印机，使用未配置分类的打印机
            if (categoryPrinters.isEmpty()) {
                Map<String, Object> params = new HashMap<>();
                params.put("storeId", orderInfo.getStoreInfo().getId());
                params.put("status", StatusEnum.ENABLED.getKey());
                params.put("autoPrint", YesOrNoEnum.YES.getKey());
                params.put("afterPay", YesOrNoEnum.YES.getKey());
                params.put("type", PrinterTypeEnum.LABEL.getKey());

                List<MtPrinter> allPrinters = queryPrinterListByParams(params);

                // 过滤出未配置任何分类的打印机
                List<Integer> configuredPrinterIds = new ArrayList<>();
                Map<String, Object> cateParams = new HashMap<>();
                cateParams.put("storeId", orderInfo.getStoreInfo().getId());
                List<MtPrinterCate> allPrinterCates = printerCateService.queryPrinterCateListByParams(cateParams);
                for (MtPrinterCate printerCate : allPrinterCates) {
                    configuredPrinterIds.add(printerCate.getPrinterId());
                }

                categoryPrinters = allPrinters.stream()
                        .filter(printer -> !configuredPrinterIds.contains(printer.getId()))
                        .collect(Collectors.toList());
            }

            // 如果还是没有找到打印机，跳过该分类
            if (categoryPrinters.isEmpty()) {
                continue;
            }

            // 为该分类的商品打印标签
            for (MtPrinter mtPrinter : categoryPrinters) {
                for (int i = 0; i < cateGoods.size(); i++) {
                    OrderGoodsDto goodsDto = cateGoods.get(i);
                    int currentCup = i + 1;
                List<GoodsSpecValueDto> specList = goodsDto.getSpecList();

                printRequest.setSn(mtPrinter.getSn());

                MtGoods mtGoods = mtGoodsMapper.selectById(goodsDto.getGoodsId());
                if(mtGoods.getType().equals("package")){
                    continue;
                }

                StringBuilder printContent = new StringBuilder();
                /**
                 * 
//   <IMG x="20" y="32" w="40">
<PAGE>
  <SIZE>40,60</SIZE> 
  <TEXT x="200" y="10" font="9" w="1" h="1">1234</TEXT>
  <TEXT x="200" y="40" font="9" w="1" h="1">1/1杯</TEXT>
  
  <TEXT x="10" y="150" font="9" w="1" h="1">珍珠奶茶</TEXT> 

  <TEXT x="10" y="180" font="9" w="1" h="1">杯型：大杯</TEXT> 
  <TEXT x="10" y="210" font="9" w="1" h="1">温度：标准冰</TEXT> 
  <TEXT x="10" y="240" font="9" w="1" h="1">糖度：标准甜</TEXT> 

  <TEXT x="10" y="270" font="9" w="1" h="1">门店:青岛xx 1号店</TEXT>
  <TEXT x="10" y="300" font="9" w="1" h="1">时间:2024年12月26日 14点41分</TEXT>


  <TEXT x="10" y="380" font="9" w="1" h="1">堂食</TEXT> 
  <TEXT x="10" y="410" font="9" w="1" h="1">小程序下单</TEXT> 
  <QRC x="200" y="350" s="4" e="L">123456789012345645</QRC>
</PAGE>
                 */
                
                printContent.append("<PAGE>\n");
                printContent.append("<SIZE>40,60</SIZE>\n");
                printContent.append("<IMG x=\"10\" y=\"10\" w=\"20\" >");
                
                // 取餐码和杯数
                printContent.append("<TEXT x=\"200\" y=\"10\" font=\"9\" w=\"1\" h=\"1\">").append(orderInfo.getPickupCode()).append("</TEXT>\n");
                printContent.append("<TEXT x=\"200\" y=\"40\" font=\"9\" w=\"1\" h=\"1\">").append(currentCup).append(" / ").append(totalCups).append(" </TEXT>\n\n");
                
                // 设备
                if(StringUtil.isNotEmpty(mtGoods.getDrinkMaker())){
                    printContent.append("<TEXT x=\"10\" y=\"90\" font=\"9\" w=\"1\" h=\"1\">").append(mtGoods.getDrinkMaker()).append("</TEXT>\n");
                }
            
                // 商品信息
                printContent.append("<TEXT x=\"10\" y=\"120\" font=\"9\" w=\"1\" h=\"1\">").append(goodsDto.getName()).append("</TEXT>\n");
                
                
                int y = 150;
                if (specList != null && specList.size() > 0) {
                    for (int j = 0; j < specList.size(); j++) {
                        GoodsSpecValueDto spec = specList.get(j);
                        printContent.append("<TEXT x=\"10\" y=\""+(150 + j * 30)+ "\" font=\"9\" w=\"1\" h=\"1\">").append(spec.getSpecName()).append(": ").append(spec.getSpecValue()).append("</TEXT>\n");
                    }
                    y = 150 + specList.size() * 30;
                }
                
                // 门店信息
                // printContent.append("<TEXT x=\"10\" y=\""+y+"\" font=\"9\" w=\"1\" h=\"1\">门店: ").append(storeInfo.getName()).append("</TEXT>\n");
                
                // 时间信息
                printContent.append("<TEXT x=\"10\" y=\""+(y+6)+"\" font=\"9\" w=\"1\" h=\"1\">时间: ").append(orderInfo.getCreateTime()).append("</TEXT>\n");

                
                // 下单来源
                String platform = orderDto.getPlatform();
                String orderSource = "店内下单";
                if (platform != null) {
                    if (platform.equals(PlatformTypeEnum.MP_WEIXIN.getCode())) {
                        orderSource = "微信小程序下单";
                    }
                }
                if(!orderInfo.getPayType().equals(PayTypeEnum.EXTERNAL.getKey())){
                    String takeMode = orderInfo.getTakeMode();
                    printContent.append("<TEXT x=\"10\" y=\"380\" font=\"9\" w=\"1\" h=\"1\">到店取餐 "+takeMode+"</TEXT> \n");
                    printContent.append("<TEXT x=\"10\" y=\"410\" font=\"9\" w=\"1\" h=\"1\">").append(orderSource).append("</TEXT> \n");
                     
                }else{
                    printContent.append("<TEXT x=\"10\" y=\"380\" font=\"9\" w=\"1\" h=\"1\">外卖 / 团购</TEXT> \n");
                    printContent.append("<TEXT x=\"10\" y=\"410\" font=\"9\" w=\"1\" h=\"1\">").append(orderInfo.getExternalOrder()).append("</TEXT> \n");
                }
                
                // 二维码
                String produceCode = "";
                
                if(goodsDto.getSkuId() != null &&  goodsDto.getSkuId() > 0 && goodsDto.getSpecList() != null && goodsDto.getSpecList().size() > 0){
                    MtGoodsSku mtGoodsSku = mtGoodsSkuMapper.selectById(goodsDto.getSkuId());
                    produceCode = mtGoodsSku.getProduceCode();
                } else{
                    produceCode = mtGoods.getProduceCode();
                }
                 
                if(!StringUtils.isEmpty(produceCode)){
                    printContent.append("<QRC x=\"180\" y=\"340\" s=\"6\" e=\"L\">").append(produceCode).append("</QRC>\n");
                }
                
                printContent.append("</PAGE>");

                printRequest.setContent(printContent.toString());
                printRequest.setCopies(1);
                printRequest.setVoice(2);
                printRequest.setMode(1);
                PrinterUtil.printLabel(printRequest);

                }
            }
        }

        return true;
    }

    /**
     * 打印订单标签
     *
     * @param orderInfo 订单信息
     * @return 打印结果
     * */
    @Override
    public Boolean printOrderLabel(UserOrderDto orderInfo) throws Exception {
        if (orderInfo.getStoreInfo() == null) {
            return false;
        }
        return printLabel(orderInfo, orderInfo.getStoreInfo());
    }

    /**
     * 打印有效期标签
     *
     * @param name 商品名称
     * @param productDate 生产日期
     * @param expiryDate 过期日期
     * @param merchantId 商户ID
     * @param storeId 店铺ID
     * @return
     * */
    @Override
    public Boolean printExpiryLabel(String name, Date productDate, Date expiryDate, String remark, Integer printerId, Integer merchantId) throws Exception {
        PrintRequest printRequest = new PrintRequest();
        createRequestHeader(merchantId, printRequest);

        // 获取打印机列表
       
        MtPrinter mtPrinter = queryPrinterById(printerId);
        if (mtPrinter == null) {            
            throw new BusinessCheckException("未找到打印机");
        }
 
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        printRequest.setSn(mtPrinter.getSn());
            
        StringBuilder printContent = new StringBuilder();
        printContent.append("<PAGE>\n");
        printContent.append("<SIZE>40,30</SIZE>\n");
        
        // 商品名称
        printContent.append("<TEXT x=\"10\" y=\"40\" font=\"9\" w=\"2\" h=\"2\">").append(name).append("</TEXT>\n");
        
        // 生产日期
        printContent.append("<TEXT x=\"10\" y=\"100\" font=\"9\" w=\"1\" h=\"1\">生产日期:").append(sdf.format(productDate)).append("</TEXT>\n");
        
        // 过期日期
        printContent.append("<TEXT x=\"10\" y=\"150\" font=\"9\" w=\"1\" h=\"1\">过期日期: ").append(sdf.format(expiryDate)).append("</TEXT>\n");
        
        printContent.append("<TEXT x=\"10\" y=\"200\" font=\"9\" w=\"1\" h=\"1\">").append(remark).append("</TEXT>\n");
        
        printContent.append("</PAGE>");

        printRequest.setContent(printContent.toString());
        printRequest.setCopies(1);
        printRequest.setVoice(2);
        printRequest.setMode(1);
        PrinterUtil.printLabel(printRequest);

        return true;
    }
    /**
     * 根据ID获打印机取息
     *
     * @param id 打印机ID
     * @return
     */
    @Override
    public MtPrinter queryPrinterById(Integer id) {
        return mtPrinterMapper.selectById(id);
    }

    /**
     * 根据ID删除打印机
     *
     * @param id 打印机ID
     * @param operator 操作人
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationServiceLog(description = "删除打印机")
    public void deletePrinter(Integer id, String operator) throws BusinessCheckException {
        MtPrinter mtPrinter = queryPrinterById(id);
        if (null == mtPrinter) {
            return;
        }
        // 删除云打印机
        if (StringUtil.isNotEmpty(mtPrinter.getSn())) {
            DelPrinterRequest restRequest = new DelPrinterRequest();
            createRequestHeader(mtPrinter.getMerchantId(), restRequest);
            String[] snList = { mtPrinter.getSn() };
            restRequest.setSnlist(snList);
            PrinterUtil.delPrinters(restRequest);
        }
        mtPrinter.setStatus(StatusEnum.DISABLE.getKey());
        mtPrinter.setUpdateTime(new Date());
        mtPrinterMapper.updateById(mtPrinter);
    }

    /**
     * 修改打印机数据
     *
     * @param  mtPrinter 打印机参数
     * @throws BusinessCheckException
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    @OperationServiceLog(description = "更新打印机")
    public MtPrinter updatePrinter(MtPrinter mtPrinter) throws BusinessCheckException {
        MtPrinter printer = queryPrinterById(mtPrinter.getId());
        BeanUtils.copyProperties(mtPrinter, printer);
        if (mtPrinter == null) {
            throw new BusinessCheckException("该打印机状态异常");
        }
        if (printer.getMerchantId() == null || printer.getMerchantId() < 1) {
            throw new BusinessCheckException("平台方帐号无法执行该操作，请使用商户帐号操作");
        }

        if (mtPrinter.getSn() != null && mtPrinter.getName() != null && !mtPrinter.getStatus().equals(StatusEnum.DISABLE.getKey())) {
            UpdPrinterRequest restRequest = new UpdPrinterRequest();
            createRequestHeader(mtPrinter.getMerchantId(), restRequest);
            restRequest.setName(mtPrinter.getName());
            restRequest.setSn(mtPrinter.getSn());
            PrinterUtil.updPrinter(restRequest);
        }
        if (mtPrinter.getStatus().equals(StatusEnum.DISABLE.getKey())) {
            deletePrinter(mtPrinter.getId(), mtPrinter.getOperator());
        }

        mtPrinter.setUpdateTime(new Date());
        mtPrinterMapper.updateById(printer);
        return printer;
    }

   /**
    * 根据条件搜索打印机
    *
    * @param params 查询参数
    * @throws BusinessCheckException
    * @return
    * */
    @Override
    public List<MtPrinter> queryPrinterListByParams(Map<String, Object> params) {
        String status = params.get("status") == null ? StatusEnum.ENABLED.getKey(): params.get("status").toString();
        String storeId = params.get("storeId") == null ? "" : params.get("storeId").toString();
        String merchantId = params.get("merchantId") == null ? "" : params.get("merchantId").toString();
        String sn = params.get("sn") == null ? "" : params.get("sn").toString();
        String name = params.get("name") == null ? "" : params.get("name").toString();
        String autoPrint = params.get("autoPrint") == null ? "" : params.get("autoPrint").toString();
        String beforePay = params.get("beforePay") == null ? YesOrNoEnum.YES.getKey() : params.get("beforePay").toString();
        String afterPay = params.get("afterPay") == null ? YesOrNoEnum.NO.getKey() : params.get("afterPay").toString();
        String type = params.get("type") == null ? "" : params.get("type").toString();

        LambdaQueryWrapper<MtPrinter> lambdaQueryWrapper = Wrappers.lambdaQuery();
        if (StringUtils.isNotBlank(type)) {
            lambdaQueryWrapper.eq(MtPrinter::getType, type);
        }
        if (StringUtils.isNotBlank(status)) {
            lambdaQueryWrapper.eq(MtPrinter::getStatus, status);
        }
        if (StringUtils.isNotBlank(merchantId)) {
            lambdaQueryWrapper.eq(MtPrinter::getMerchantId, merchantId);
        }
        if (StringUtils.isNotBlank(sn)) {
            lambdaQueryWrapper.eq(MtPrinter::getSn, sn);
        }
        if (StringUtils.isNotBlank(name)) {
            lambdaQueryWrapper.eq(MtPrinter::getName, name);
        }
        if (StringUtils.isNotBlank(storeId)) {
            lambdaQueryWrapper.and(wq -> wq
                    // .eq(MtPrinter::getStoreId, 0)
                    // .or()
                    .eq(MtPrinter::getStoreId, storeId));
        }
        if (StringUtils.isNotBlank(autoPrint)) {
            lambdaQueryWrapper.eq(MtPrinter::getAutoPrint, autoPrint);
        }
        if (StringUtils.isNotBlank(beforePay)) {
            lambdaQueryWrapper.eq(MtPrinter::getBeforePay, beforePay);
        }
        if (StringUtils.isNotBlank(afterPay)) {
            lambdaQueryWrapper.eq(MtPrinter::getAfterPay, afterPay);
        }
        lambdaQueryWrapper.orderByAsc(MtPrinter::getId);

        return mtPrinterMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 创建接口请求header
     *
     * @param merchantId 商户ID
     * @param request RestRequest
     * @return
     * */
    public void createRequestHeader(Integer merchantId, RestRequest request) throws BusinessCheckException {
        List<MtSetting> settings = settingService.getSettingList(merchantId, SettingTypeEnum.PRINTER.getKey());
        if (settings != null && settings.size() > 0) {
            String userName = "";
            String userKey = "";
            for (MtSetting mtSetting : settings) {
                if (mtSetting.getName().equals(PrinterSettingEnum.USER_NAME.getKey())) {
                    userName = mtSetting.getValue();
                }
                if (mtSetting.getName().equals(PrinterSettingEnum.USER_KEY.getKey())) {
                    userKey = mtSetting.getValue();
                }
            }
            if (StringUtil.isNotEmpty(userName) && StringUtil.isNotEmpty(userKey)) {
                request.setUser(userName);
                request.setTimestamp(System.currentTimeMillis() + "");
                request.setSign(HashSignUtil.sign(request.getUser() + userKey + request.getTimestamp()));
                request.setDebug("0");
            } else {
                throw new BusinessCheckException("请先设置芯烨云打印账号！");
            }
        } else {
            throw new BusinessCheckException("请先设置芯烨云打印账号！");
        }
    }

    /**
     * 根据商品分类查找标签打印机
     *
     * @param cateId 商品分类ID
     * @param storeId 店铺ID
     * @return 打印机列表
     * */
    @Override
    public List<MtPrinter> queryLabelPrintersByCateId(Integer cateId, Integer storeId) throws BusinessCheckException {
        // 先查找配置了该分类的打印机ID列表
        List<Integer> printerIds = printerCateService.getPrinterIdsByCateId(cateId, storeId);

        if (printerIds != null && !printerIds.isEmpty()) {
            // 查询这些打印机的详细信息
            Map<String, Object> params = new HashMap<>();
            params.put("storeId", storeId);
            params.put("status", StatusEnum.ENABLED.getKey());
            params.put("type", PrinterTypeEnum.LABEL.getKey());

            List<MtPrinter> allPrinters = queryPrinterListByParams(params);

            // 过滤出配置了该分类的打印机
            return allPrinters.stream()
                    .filter(printer -> printerIds.contains(printer.getId()))
                    .collect(Collectors.toList());
        }

        // 如果没有找到配置过分类的打印机，返回空列表
        return new ArrayList<>();
    }
}
