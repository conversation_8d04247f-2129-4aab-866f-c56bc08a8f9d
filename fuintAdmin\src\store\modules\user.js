import { login, logout, getInfo } from '@/api/login'
import { getToken, setToken, removeToken } from '@/utils/auth'

const user = {
  state: {
    token: getToken(),
    name: '',
    avatar: '',
    roles: [],
    permissions: [],
    merchantId: '',
    merchantName: '',
    storeId: '',
    storeName: ''
  },

  mutations: {
    SET_TOKEN: (state, token) => {
      state.token = token
    },
    SET_NAME: (state, name) => {
      state.name = name
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles
    },
    SET_PERMISSIONS: (state, permissions) => {
      state.permissions = permissions
    },
    SET_MERCHANT_ID: (state, merchantId) => {
      state.merchantId = merchantId
    },
    SET_MERCHANT_NAME: (state, merchantName) => {
      state.merchantName = merchantName
    },
    SET_STORE_ID: (state, storeId) => {
      state.storeId = storeId
    },
    SET_STORE_NAME: (state, storeName) => {
      state.storeName = storeName
    },
  },

  actions: {
    // 登录
    Login({ commit }, userInfo) {
      const username = userInfo.username.trim()
      const password = userInfo.password
      const captchaCode = userInfo.captchaCode
      const uuid = userInfo.uuid
      return new Promise((resolve, reject) => {
        login(username, password, captchaCode, uuid).then(res => {
          setToken(res.data.token)
          commit('SET_TOKEN', res.data.token)
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 获取用户信息
    GetInfo({ commit, state }) {
      return new Promise((resolve, reject) => {
        getInfo().then(res => {
          const user = res.data.accountInfo
          const avatar = (user.avatar == "" || user.avatar == null) ? require("@/assets/images/avatar.png") : process.env.VUE_APP_BASE_API + user.avatar;
          if (res.data.roles && res.data.roles.length > 0) { // 验证返回的roles是否是一个非空数组
              commit('SET_ROLES', res.data.roles);
              commit('SET_PERMISSIONS', res.data.permissions);
          } else {
              commit('SET_ROLES', ['ROLE_DEFAULT']);
          }
          commit('SET_NAME', user.accountName);
          commit('SET_AVATAR', avatar);
          commit('SET_MERCHANT_ID',  user.merchantId);
          commit('SET_MERCHANT_NAME',  user.merchantName);
          commit('SET_STORE_ID',  user.storeId);
          commit('SET_STORE_NAME',  user.storeName);
          resolve(res);
        }).catch(error => {
          reject(error)
        })
      })
    },

    // 退出系统
    LogOut({ commit, state }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '')
          commit('SET_ROLES', [])
          commit('SET_PERMISSIONS', [])
          commit('SET_MERCHANT_ID',  '')
          commit('SET_MERCHANT_NAME',  '')
          commit('SET_STORE_ID', '')
          commit('SET_STORE_NAME', '')
          removeToken()
          resolve()
        }).catch(error => {
          reject(error)
        })
      })
    }
  }
}

export default user
