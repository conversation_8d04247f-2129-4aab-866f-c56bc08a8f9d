package com.fuint.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fuint.framework.pagination.PaginationRequest;
import com.fuint.framework.pagination.PaginationResponse;
import com.fuint.repository.model.${className};
import com.fuint.framework.exception.BusinessCheckException;
import java.util.List;
import java.util.Map;

/**
 * ${moduleName}业务接口
 *
 * Created by ${author}
 
 */
public interface ${tableClass}Service extends IService<${className}> {

    /**
     * 分页查询列表
     *
     * @param paginationRequest
     * @return
     */
    PaginationResponse<${className}> query${tableClass}ListByPagination(PaginationRequest paginationRequest) throws BusinessCheckException;

    /**
     * 添加${moduleName}
     *
     * @param  ${tablePrefix}${tableClass}
     * @throws BusinessCheckException
     * @return
     */
    ${className} add${tableClass}(${className} ${tablePrefix}${tableClass}) throws BusinessCheckException;

    /**
     * 根据ID获取${moduleName}信息
     *
     * @param id ID
     * @throws BusinessCheckException
     * @return
     */
    ${className} query${tableClass}ById(Integer id) throws BusinessCheckException;

    /**
     * 根据ID删除${moduleName}
     *
     * @param id ID
     * @param operator 操作人
     * @throws BusinessCheckException
     * @return
     */
    void delete${tableClass}(Integer id, String operator) throws BusinessCheckException;

    /**
     * 更新${moduleName}
     * @param  ${tablePrefix}${tableClass}
     * @throws BusinessCheckException
     * @return
     * */
    ${className} update${tableClass}(${className} ${tablePrefix}${tableClass}) throws BusinessCheckException;

    /**
     * 根据条件搜索${moduleName}
     *
     * @param params 查询参数
     * @throws BusinessCheckException
     * @return
     * */
    List<${className}> query${tableClass}ListByParams(Map<String, Object> params) throws BusinessCheckException;
}
