package com.fuint.repository.mapper;

import com.fuint.repository.model.MtPrinterCate;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 打印机分类关联 Mapper 接口
 *
 * @Created by FSQ
 */
public interface MtPrinterCateMapper extends BaseMapper<MtPrinterCate> {

    /**
     * 根据参数查询打印机分类关联列表
     *
     * @param params 查询参数
     * @return 打印机分类关联列表
     */
    List<MtPrinterCate> selectByParams(@Param("params") Map<String, Object> params);

    /**
     * 根据打印机ID删除关联
     *
     * @param printerId 打印机ID
     * @return 影响行数
     */
    int deleteByPrinterId(@Param("printerId") Integer printerId);

    /**
     * 根据分类ID删除关联
     *
     * @param cateId 分类ID
     * @return 影响行数
     */
    int deleteByCateId(@Param("cateId") Integer cateId);

    /**
     * 根据商品分类ID查询关联的打印机ID列表
     *
     * @param cateId 商品分类ID
     * @param storeId 店铺ID
     * @return 打印机ID列表
     */
    List<Integer> selectPrinterIdsByCateId(@Param("cateId") Integer cateId, @Param("storeId") Integer storeId);

}
