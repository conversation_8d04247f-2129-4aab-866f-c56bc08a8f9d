
3227fa44c1cd8988a9d41aa08357ebdfca2d48b9	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.cb808d13e4fc9d82f890.hot-update.js\",\"contentHash\":\"94723181d93459afbdcc89182250425d\"}","integrity":"sha512-DFwlEn6lYuuj6vlzGg5y+AFqRr09rm9xDeh7fwelfaj4kGMX5g3myrj09bw4786r/ivH19RCMSO28j7i+kAjcw==","time":1755006275848,"size":32499}