package com.fuint.common.test;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.fuint.common.service.HuifuPayService;
import com.fuint.common.service.OrderService;
import com.fuint.common.service.WeixinService;
import com.fuint.framework.exception.BusinessCheckException;

import java.math.BigDecimal;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

/**
 * 汇付支付服务测试类
 */
@Component
public class HuifuPayServiceTest implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(HuifuPayServiceTest.class);

    @Autowired
    private HuifuPayService huifuPayService;

    @Autowired
    private OrderService orderService;
    @Autowired
    private WeixinService weixinService;


    @Override
    public void run(String... args) {
        // testGetFaPiaoQrCode();
        // testWXMsg1();
        // testWXMsg2();
        // testPayed();
    }
    public void testPayed(){
        try {
             orderService.setOrderPayed(192, null);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }
    public void testWXMsg1(){
        try {
            
            
            JSONObject jsonData = new JSONObject();
            jsonData.put("touser", "oyNWi7WAoTnICfRGaMl9Bja3V1f8"); // 接收者的openid
            jsonData.put("template_id", "eTqcnyNUQAm2fkMddmEU3RwuJozteKz7A2Acdzh_6y8");
            jsonData.put("page", "pages/order/index");
            jsonData.put("miniprogram_state", "formal");
            jsonData.put("lang", "zh_CN");
            JSONObject data = new JSONObject(); 
            // 门店名称
            JSONObject data3 = new JSONObject();
            data3.put("value", "山东威海石岛凤凰湖店");
            data.put("thing11", data3);
            
            JSONObject data2 = new JSONObject();
            data2.put("value", "制作中");
            data.put("phrase1", data2); 

            // 取餐码
            JSONObject data4 = new JSONObject();
            data4.put("value", "1234");
            data.put("number7", data4); 

            // 温馨提示
            JSONObject data5 = new JSONObject();
            data5.put("value", "预计1分钟后可取餐");
            data.put("thing10", data5); 
            // 下单时间
            JSONObject data6 = new JSONObject();
            data6.put("value", "2025-04-27 20:13:00");
            data.put("time8", data6);

            jsonData.put("data", data);
            String reqDataJsonStr = JSON.toJSONString(jsonData);  
            weixinService.doSendSubscribeMessage(0,reqDataJsonStr);
        } catch (Exception e) {
            logger.error("测试过程中发生未知异常: {}", e.getMessage(), e);
        }
        
    } 
    public void testWXMsg2(){
        try { 
            JSONObject jsonData = new JSONObject();
            jsonData.put("touser", "oyNWi7WAoTnICfRGaMl9Bja3V1f8"); // 接收者的openid
            jsonData.put("template_id", "iN-nhCWdmmaP2gAS69lFbjHCHbSHU3F4en1vVWGFLTs");
            jsonData.put("page", "pages/order/index");
            jsonData.put("miniprogram_state", "formal");
            jsonData.put("lang", "zh_CN");
            JSONObject data = new JSONObject();
            // 取餐码
            JSONObject data1 = new JSONObject();
            data1.put("value", "1234");
            data.put("character_string12", data1);
            // 订单编号
            JSONObject data2 = new JSONObject();
            data2.put("value", "202504271813516088562");
            data.put("character_string22", data2);
            // 门店名称
            JSONObject data3 = new JSONObject();
            data3.put("value", "山东威海石岛凤凰湖店");
            data.put("thing23", data3);
    
            // 温馨提醒
            JSONObject data4 = new JSONObject();
            data4.put("value", "您的餐品已制作完成，请到前台领取");
            data.put("thing7", data4); 
    
            jsonData.put("data", data);
            String reqDataJsonStr = JSON.toJSONString(jsonData);  
            weixinService.doSendSubscribeMessage(0,reqDataJsonStr);
        } catch (Exception e) {
            logger.error("测试过程中发生未知异常: {}", e.getMessage(), e);
        }
        
    }
    /**
     * 测试获取发票二维码
     */
    public void testGetFaPiaoQrCode() {
        try {
            // 这里需要替换为实际存在的订单号
            String orderSn = "202504301204081499302";
            
            logger.info("开始测试获取发票二维码，订单号: {}", orderSn);
            
            String qrCodeUrl = huifuPayService.getFaPiaoQrCode(orderSn);
            
            if (qrCodeUrl != null) {
                logger.info("获取发票二维码成功: {}", qrCodeUrl);
            } else {
                logger.error("获取发票二维码失败: 返回为空");
            }
        } catch (BusinessCheckException e) {
            logger.error("获取发票二维码异常: {}", e.getMessage(), e);
        } catch (Exception e) {
            logger.error("测试过程中发生未知异常: {}", e.getMessage(), e);
        }
    }
}