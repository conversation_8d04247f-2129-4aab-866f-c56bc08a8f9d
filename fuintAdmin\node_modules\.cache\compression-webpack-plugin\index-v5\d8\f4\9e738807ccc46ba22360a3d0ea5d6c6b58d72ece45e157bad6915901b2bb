
f46dc5c95331d412598bb47e8829bbcb9e3db6ef	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"d406f2c61f1ac99e7836acc81a7ac417\"}","integrity":"sha512-jUsgOyq68dvNhLw9EYYRD3fiflBMTOuHdh79IsfWVHvBYsJRISXX+4LNfIrOJSXm9kV6qVjvPrxs+SzlboK4Pg==","time":1755006086041,"size":2883363}