package com.fuint.common.enums;

/**
 * 支付类型
 *

 */
public enum PayTypeEnum {
    CASH("CASH", "现金支付"),
    EXTERNAL("EXTERNAL", "外部订单"),
    JSAPI("JSAPI", "微信支付"),
    MICROPAY("MICROPAY", "扫码支付"),
    BALANCE("BALANCE", "余额支付"),
    ALISCAN("ALISCAN", "支付宝扫码"),
    HAFAN("HAFAN", "火炬币支付");

    private String key;

    private String value;

    PayTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
