
b53eb306a3ed9c38f6a98cac557d4152a3df2fcf	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002Fapp.js\",\"contentHash\":\"19811fdf1278cfdbe32c566330127063\"}","integrity":"sha512-1sBE9EpxfpLUhAkHd4W9jUyQSGjjwX3Qe9/pCkHc2WMJZ1HSdmFD3IQN+IYxwMfdOf/N6W1eKdVBkQumQEYSdQ==","time":1755006352160,"size":2883027}