
ad9221c92859689f0191bddcc7bd0ca3c8e749db	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.5f0df4098bc5cf63c3f8.hot-update.js\",\"contentHash\":\"2d2e6961da84dbd02750d28830f50c84\"}","integrity":"sha512-neblUg8ySyjnMKf3B1Gld2MzeU5IwS1txUK1z/Bh/bXiw0hw9kbwynHnDw8ESHXilwGpqPypYdCQgmLrKSd0eQ==","time":1755006070529,"size":31390}