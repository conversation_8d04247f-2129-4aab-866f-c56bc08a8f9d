package com.fuint.common.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.fuint.common.dto.GoodsDto;
import com.fuint.common.dto.PackageGroupDto;
import com.fuint.common.dto.PackageGroupItemDto;
import com.fuint.common.enums.StatusEnum;
import com.fuint.common.service.GoodsService;
import com.fuint.common.service.PackageService;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.repository.mapper.MtPackageGroupItemMapper;
import com.fuint.repository.mapper.MtPackageGroupMapper;
import com.fuint.repository.model.MtPackageGroup;
import com.fuint.repository.model.MtPackageGroupItem;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

/**
 * 套餐服务实现类
 */
@Service
@AllArgsConstructor
public class PackageServiceImpl implements PackageService {

    private static final Logger logger = LoggerFactory.getLogger(PackageServiceImpl.class);

    private MtPackageGroupMapper packageGroupMapper;
    
    private MtPackageGroupItemMapper packageGroupItemMapper;

    /**
     * 商品服务
     */
    private GoodsService goodsService;

    /**
     * 根据套餐商品ID获取套餐分组列表
     *
     * @param goodsId 套餐商品ID
     * @return
     * @throws IllegalAccessException 
     * @throws InvocationTargetException 
     */
    @Override
    public List<PackageGroupDto> getPackageGroupsByGoodsId(Integer goodsId) throws BusinessCheckException, InvocationTargetException, IllegalAccessException {
        List<PackageGroupDto> result = new ArrayList<>();
        
        // 获取分组列表
        List<MtPackageGroup> groupList = this.packageGroupMapper.getPackageGroups(goodsId);
        if (groupList == null || groupList.isEmpty()) {
            return result;
        }
        
        for (MtPackageGroup group : groupList) {
            PackageGroupDto groupDto = new PackageGroupDto();
            BeanUtils.copyProperties(group, groupDto);
            
            // 获取组下商品
            List<MtPackageGroupItem> itemList = this.packageGroupItemMapper.getGroupItems(group.getId());
            List<PackageGroupItemDto> itemDtoList = new ArrayList<>();
            
            for (MtPackageGroupItem item : itemList) {
                PackageGroupItemDto itemDto = new PackageGroupItemDto();
                BeanUtils.copyProperties(item, itemDto);
                
                // 获取商品信息
                GoodsDto goodsDto = goodsService.getGoodsDetail(item.getItemGoodsId(), false);
                itemDto.setItemGoods(goodsDto);
                
                itemDtoList.add(itemDto);
            }
            
            groupDto.setItems(itemDtoList);
            result.add(groupDto);
        }
        
        return result;
    }

    /**
     * 修改或添加套餐分组
     *
     * @param packageGroup
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MtPackageGroup savePackageGroup(MtPackageGroup packageGroup) throws BusinessCheckException {
        if (packageGroup.getId() != null && packageGroup.getId() > 0) {
            packageGroup.setUpdateTime(new Date());
            this.packageGroupMapper.updateById(packageGroup);
        } else {
            packageGroup.setStatus(StatusEnum.ENABLED.getKey());
            packageGroup.setCreateTime(new Date());
            packageGroup.setUpdateTime(new Date());
            this.packageGroupMapper.insert(packageGroup);
        }
        
        return packageGroup;
    }

    /**
     * 根据ID获取套餐分组
     *
     * @param id 套餐分组ID
     * @return
     */
    @Override
    public MtPackageGroup getPackageGroupById(Integer id) {
        return this.packageGroupMapper.selectById(id);
    }

    /**
     * 删除套餐分组
     *
     * @param id 套餐分组ID
     * @param operator 操作人
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePackageGroup(Integer id, String operator) throws BusinessCheckException {
        MtPackageGroup packageGroup = this.packageGroupMapper.selectById(id);
        if (packageGroup == null) {
            throw new BusinessCheckException("套餐分组不存在");
        }
        
        // 先删除分组下的商品
        Map<String, Object> params = new HashMap<>();
        params.put("groupId", id);
        List<MtPackageGroupItem> itemList = queryPackageGroupItemList(params);
        for (MtPackageGroupItem item : itemList) {
            deletePackageGroupItem(item.getId(), operator);
        }
        
        // 再删除分组
        packageGroup.setStatus(StatusEnum.DISABLE.getKey());
        packageGroup.setUpdateTime(new Date());
        this.packageGroupMapper.updateById(packageGroup);
    }

    /**
     * 修改或添加套餐分组商品
     *
     * @param packageGroupItem
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MtPackageGroupItem savePackageGroupItem(MtPackageGroupItem packageGroupItem) throws BusinessCheckException {
        if (packageGroupItem.getId() != null && packageGroupItem.getId() > 0) {
            packageGroupItem.setUpdateTime(new Date());
            this.packageGroupItemMapper.updateById(packageGroupItem);
        } else {
            packageGroupItem.setStatus(StatusEnum.ENABLED.getKey());
            packageGroupItem.setCreateTime(new Date());
            packageGroupItem.setUpdateTime(new Date());
            this.packageGroupItemMapper.insert(packageGroupItem);
        }
        
        return packageGroupItem;
    }

    /**
     * 根据ID获取套餐分组商品
     *
     * @param id 套餐分组商品ID
     * @return
     */
    @Override
    public MtPackageGroupItem getPackageGroupItemById(Integer id) {
        return this.packageGroupItemMapper.selectById(id);
    }

    /**
     * 删除套餐分组商品
     *
     * @param id 套餐分组商品ID
     * @param operator 操作人
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deletePackageGroupItem(Integer id, String operator) throws BusinessCheckException {
        MtPackageGroupItem packageGroupItem = this.packageGroupItemMapper.selectById(id);
        if (packageGroupItem == null) {
            throw new BusinessCheckException("套餐分组商品不存在");
        }
        
        packageGroupItem.setStatus(StatusEnum.DISABLE.getKey());
        packageGroupItem.setUpdateTime(new Date());
        this.packageGroupItemMapper.updateById(packageGroupItem);
    }

    /**
     * 批量保存套餐数据
     * 
     * @param goodsId 套餐商品ID
     * @param groups 分组数据
     * @param operator 操作人
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchSavePackageData(Integer goodsId, List<Map<String, Object>> groups, String operator) throws BusinessCheckException {
        if (goodsId == null || goodsId <= 0) {
            throw new BusinessCheckException("商品ID不合法");
        }
        
        // 先删除原有数据
        Map<String, Object> params = new HashMap<>();
        params.put("goodsId", goodsId);
        
        // 删除分组商品
        List<MtPackageGroupItem> itemList = queryPackageGroupItemList(params);
        for (MtPackageGroupItem item : itemList) {
            deletePackageGroupItem(item.getId(), operator);
        }
        
        // 删除分组
        List<MtPackageGroup> groupList = queryPackageGroupList(params);
        for (MtPackageGroup group : groupList) {
            deletePackageGroup(group.getId(), operator);
        }
        
        // 新增分组及商品
        for (Map<String, Object> groupData : groups) {
            String groupName = groupData.get("groupName") == null ? "" : groupData.get("groupName").toString();
            String groupType = groupData.get("groupType") == null ? "R" : groupData.get("groupType").toString();
            Integer selectCount = groupData.get("selectCount") == null ? 1 : Integer.parseInt(groupData.get("selectCount").toString());
            Integer sort = groupData.get("sort") == null ? 0 : Integer.parseInt(groupData.get("sort").toString());
            
            // 创建分组
            MtPackageGroup group = new MtPackageGroup();
            group.setGoodsId(goodsId);
            group.setGroupName(groupName);
            group.setGroupType(groupType);
            group.setSelectCount(selectCount);
            group.setSort(sort);
            group = savePackageGroup(group);
            
            // 添加商品
            List<Map<String, Object>> items = groupData.get("items") == null ? new ArrayList<>() : (List<Map<String, Object>>) groupData.get("items");
            for (Map<String, Object> itemData : items) {
                Integer itemGoodsId = itemData.get("itemGoodsId") == null ? 0 : Integer.parseInt(itemData.get("itemGoodsId").toString());
                Integer quantity = itemData.get("quantity") == null ? 1 : Integer.parseInt(itemData.get("quantity").toString());
                String extraPriceStr = itemData.get("extraPrice") == null ? "0" : itemData.get("extraPrice").toString();
                Integer itemSort = itemData.get("sort") == null ? 0 : Integer.parseInt(itemData.get("sort").toString());
                
                MtPackageGroupItem item = new MtPackageGroupItem();
                item.setGroupId(group.getId());
                item.setGoodsId(goodsId);
                item.setItemGoodsId(itemGoodsId);
                item.setQuantity(quantity);
                item.setExtraPrice(new java.math.BigDecimal(extraPriceStr));
                item.setSort(itemSort);
                savePackageGroupItem(item);
            }
        }
        
        return true;
    }

    /**
     * 根据查询参数获取套餐分组列表
     *
     * @param params
     * @return
     */
    @Override
    public List<MtPackageGroup> queryPackageGroupList(Map<String, Object> params) {
        LambdaQueryWrapper<MtPackageGroup> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        
        if (params.get("goodsId") != null) {
            lambdaQueryWrapper.eq(MtPackageGroup::getGoodsId, params.get("goodsId"));
        }
        
        if (params.get("groupType") != null) {
            lambdaQueryWrapper.eq(MtPackageGroup::getGroupType, params.get("groupType"));
        }
        
        if (params.get("status") != null) {
            lambdaQueryWrapper.eq(MtPackageGroup::getStatus, params.get("status"));
        } else {
            lambdaQueryWrapper.eq(MtPackageGroup::getStatus, StatusEnum.ENABLED.getKey());
        }
        
        lambdaQueryWrapper.orderByAsc(MtPackageGroup::getSort);
        
        return this.packageGroupMapper.selectList(lambdaQueryWrapper);
    }

    /**
     * 根据查询参数获取套餐分组商品列表
     *
     * @param params
     * @return
     */
    @Override
    public List<MtPackageGroupItem> queryPackageGroupItemList(Map<String, Object> params) {
        LambdaQueryWrapper<MtPackageGroupItem> lambdaQueryWrapper = new LambdaQueryWrapper<>();
        
        if (params.get("goodsId") != null) {
            lambdaQueryWrapper.eq(MtPackageGroupItem::getGoodsId, params.get("goodsId"));
        }
        
        if (params.get("groupId") != null) {
            lambdaQueryWrapper.eq(MtPackageGroupItem::getGroupId, params.get("groupId"));
        }
        
        if (params.get("status") != null) {
            lambdaQueryWrapper.eq(MtPackageGroupItem::getStatus, params.get("status"));
        } else {
            lambdaQueryWrapper.eq(MtPackageGroupItem::getStatus, StatusEnum.ENABLED.getKey());
        }
        
        lambdaQueryWrapper.orderByAsc(MtPackageGroupItem::getSort);
        
        return this.packageGroupItemMapper.selectList(lambdaQueryWrapper);
    }
}