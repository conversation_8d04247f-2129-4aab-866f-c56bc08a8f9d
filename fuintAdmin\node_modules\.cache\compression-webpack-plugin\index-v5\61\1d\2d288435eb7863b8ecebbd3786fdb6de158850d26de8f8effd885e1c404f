
8c49ee56d86fde0185d5e063383236abb2f90eed	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.980f85cc3c33bbbea299.hot-update.js\",\"contentHash\":\"aa4e4dc6c30810011c430fcbd178d7e0\"}","integrity":"sha512-hx0MUI0WJfEbq/ULwgDH87KFbSg0JLWPSgKClUejyDz1B2C3pW8f8F5BIj4aYpcCpze9fY5cD0aNzFJMPwSl1Q==","time":1755006368870,"size":30929}