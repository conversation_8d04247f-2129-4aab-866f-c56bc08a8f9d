package com.fuint.common.enums;

/**
 * 打印机类型枚举
 */
public enum PrinterTypeEnum {
    RECEIPT("RECEIPT", "小票打印机"),
    LABEL("LABEL", "标签打印机");

    private String key;
    private String value;

    PrinterTypeEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }
}
