/*
SQLyog Ultimate v13.1.1 (64 bit)
MySQL - 8.0.21 : Database - fuint-food
*********************************************************************
*/

/*!40101 SET NAMES utf8 */;

/*!40101 SET SQL_MODE=''*/;

/*!40014 SET @OLD_UNIQUE_CHECKS=@@UNIQUE_CHECKS, UNIQUE_CHECKS=0 */;
/*!40014 SET @OLD_FOREIGN_KEY_CHECKS=@@FOREIGN_KEY_CHECKS, FOREIGN_KEY_CHECKS=0 */;
/*!40101 SET @OLD_SQL_MODE=@@SQL_MODE, SQL_MODE='NO_AUTO_VALUE_ON_ZERO' */;
/*!40111 SET @OLD_SQL_NOTES=@@SQL_NOTES, SQL_NOTES=0 */;
CREATE DATABASE /*!32312 IF NOT EXISTS*/`fuint-food` /*!40100 DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_bin */ /*!80016 DEFAULT ENCRYPTION='N' */;

USE `fuint-food`;

/*Table structure for table `mt_address` */

DROP TABLE IF EXISTS `mt_address`;

CREATE TABLE `mt_address` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `USER_ID` int NOT NULL DEFAULT '0' COMMENT '用户ID',
  `NAME` varchar(30) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `MOBILE` varchar(20) DEFAULT '' COMMENT '收货手机号',
  `PROVINCE_ID` int unsigned DEFAULT '0' COMMENT '省份ID',
  `CITY_ID` int unsigned DEFAULT '0' COMMENT '城市ID',
  `REGION_ID` int DEFAULT '0' COMMENT '区/县ID',
  `DETAIL` varchar(255) DEFAULT '' COMMENT '详细地址',
  `IS_DEFAULT` char(1) DEFAULT 'N' COMMENT '是否默认',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='会员地址表';

/*Data for the table `mt_address` */

insert  into `mt_address`(`ID`,`USER_ID`,`NAME`,`MOBILE`,`PROVINCE_ID`,`CITY_ID`,`REGION_ID`,`DETAIL`,`IS_DEFAULT`,`CREATE_TIME`,`UPDATE_TIME`,`STATUS`) values 
(1,3,'毕先生','15653290692',1,2,3,'团结花园','Y','2024-05-13 16:57:46','2024-05-13 16:57:46','A');

/*Table structure for table `mt_article` */

DROP TABLE IF EXISTS `mt_article`;

CREATE TABLE `mt_article` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `STORE_ID` int NOT NULL DEFAULT '0' COMMENT '目录ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `TITLE` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '标题',
  `BRIEF` varchar(500) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '简介',
  `URL` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '链接地址',
  `IMAGE` varchar(200) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '图片地址',
  `DESCRIPTION` text CHARACTER SET utf8 COLLATE utf8_general_ci COMMENT '描述',
  `CLICK` int DEFAULT '0' COMMENT '点击次数',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '最后操作人',
  `SORT` int DEFAULT '0' COMMENT '排序',
  `STATUS` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'A' COMMENT 'A：正常；N：禁用；D：删除',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=DYNAMIC COMMENT='文章表';

/*Data for the table `mt_article` */

/*Table structure for table `mt_balance` */

DROP TABLE IF EXISTS `mt_balance`;

CREATE TABLE `mt_balance` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺ID',
  `MOBILE` varchar(11) DEFAULT '' COMMENT '手机号',
  `USER_ID` int NOT NULL DEFAULT '0' COMMENT '用户ID',
  `ORDER_SN` varchar(32) DEFAULT '' COMMENT '订单号',
  `AMOUNT` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '余额变化数量',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `DESCRIPTION` varchar(200) DEFAULT '' COMMENT '备注说明',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A正常；D作废',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='余额变化表';

/*Data for the table `mt_balance` */

insert  into `mt_balance`(`ID`,`MERCHANT_ID`,`STORE_ID`,`MOBILE`,`USER_ID`,`ORDER_SN`,`AMOUNT`,`CREATE_TIME`,`UPDATE_TIME`,`DESCRIPTION`,`OPERATOR`,`STATUS`) values 
(1,1,4,'',1,'',1000.00,'2024-08-23 16:45:08','2024-08-23 16:45:08','后台充值','fuint','A'),
(2,1,4,'',1,'202408231645162097816',-120.00,'2024-08-23 16:45:16','2024-08-23 16:45:16','','','A');

/*Table structure for table `mt_banner` */

DROP TABLE IF EXISTS `mt_banner`;

CREATE TABLE `mt_banner` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `TITLE` varchar(100) DEFAULT '' COMMENT '标题',
  `POSITION` varchar(30) DEFAULT '' COMMENT '展示位置',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺ID',
  `URL` varchar(100) DEFAULT '' COMMENT '链接地址',
  `IMAGE` varchar(200) DEFAULT '' COMMENT '图片地址',
  `DESCRIPTION` text COMMENT '描述',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT NULL COMMENT '最后操作人',
  `SORT` int DEFAULT '0' COMMENT '排序',
  `STATUS` char(1) DEFAULT 'A' COMMENT 'A：正常；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=323 DEFAULT CHARSET=utf8 COMMENT='会员端焦点图表';

/*Data for the table `mt_banner` */

insert  into `mt_banner`(`ID`,`TITLE`,`POSITION`,`MERCHANT_ID`,`STORE_ID`,`URL`,`IMAGE`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`OPERATOR`,`SORT`,`STATUS`) values 
(1,'活动','m_home_banner',1,0,'pages/user/index','/static/uploadImages/20240508/6e4233e7f53a485bbfb0a86716587159.jpg','','2021-04-14 09:38:20','2024-05-08 16:30:23','fuint',1,'A'),
(2,'活动','m_home_ads',1,0,'pages/category/index','/static/uploadImages/20240508/35b5dfc25f114bd9aeb3e573f6de1100.png','','2021-04-14 09:38:36','2024-05-08 16:22:33','fuint',1,'A'),
(321,'蛋挞','m_home_ads',1,0,'pages/index/index','/static/uploadImages/20240508/b232bf749dbb4c139fb8dc77a02dc66e.png','','2024-05-08 16:24:16','2024-05-08 16:28:21','fuint',2,'A'),
(322,'推荐','m_home_banner',1,0,'pages/category/index','/static/uploadImages/20240508/887894c6a6eb430593165d4eb4bd4b50.jpg','','2024-05-08 16:32:02','2024-05-08 16:32:02','fuint',0,'A');

/*Table structure for table `mt_book` */

DROP TABLE IF EXISTS `mt_book`;

CREATE TABLE `mt_book` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `NAME` varchar(255) DEFAULT NULL COMMENT '预约名称',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `TYPE` varchar(30) DEFAULT 'time' COMMENT '预约方式，time：按时间预约；staff:按员工预约',
  `LOGO` varchar(255) DEFAULT '' COMMENT 'LOGO图片',
  `GOODS_ID` int DEFAULT '0' COMMENT '预约服务ID',
  `CATE_ID` int DEFAULT '0' COMMENT '预约分类',
  `SERVICE_DATES` varchar(1000) DEFAULT '' COMMENT '可预约日期',
  `SERVICE_TIMES` varchar(1000) DEFAULT '' COMMENT '可预约时间段',
  `SERVICE_STAFF_IDS` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '可预约员工ID',
  `DESCRIPTION` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '预约说明',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `SORT` int DEFAULT '0' COMMENT '排序',
  `STATUS` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'A' COMMENT '订单状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=7 DEFAULT CHARSET=utf8 COMMENT='预约表';

/*Data for the table `mt_book` */

insert  into `mt_book`(`ID`,`NAME`,`MERCHANT_ID`,`STORE_ID`,`TYPE`,`LOGO`,`GOODS_ID`,`CATE_ID`,`SERVICE_DATES`,`SERVICE_TIMES`,`SERVICE_STAFF_IDS`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`OPERATOR`,`SORT`,`STATUS`) values 
(1,'门店大保养预约',1,2,'time','/static/defaultImage/banner-2.png?v=1',1,1,'2024-08-21,2024-08-22,2024-08-23,2024-08-24,2024-08-31,2024-08-30,2024-08-29','08:30-12:00-2,14:00-18:00-3','1,2,3','小保养预约，请提前预约。线上预约提交后，店员审核，审核结果将以短信通知您，给您带来不便敬请谅解，感谢您的支持！','2023-02-14 11:45:54','2024-08-16 18:01:50','fuint',3,'A'),
(2,'门店小保养预约',1,0,'time','/static/defaultImage/banner-1.png?v=1',0,2,'2024-08-14,2024-08-15,2024-08-16','08:30-12:00-3,14:00-18:00-4','','小保养预约，请提前预约。线上预约提交后，店员审核，审核结果将以短信通知您，给您带来不便敬请谅解，感谢您的支持！','2024-08-05 18:58:45','2024-08-16 18:01:42','fuint',2,'A');

/*Table structure for table `mt_book_cate` */

DROP TABLE IF EXISTS `mt_book_cate`;

CREATE TABLE `mt_book_cate` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int NOT NULL COMMENT '所属商户',
  `STORE_ID` int DEFAULT NULL COMMENT '所属店铺',
  `NAME` varchar(50) NOT NULL COMMENT '分类名称',
  `LOGO` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `DESCRIPTION` varchar(500) DEFAULT NULL COMMENT '说明',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `SORT` int DEFAULT NULL COMMENT '排序',
  `OPERATOR` varchar(30) DEFAULT NULL COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT NULL COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='预约分类';

/*Data for the table `mt_book_cate` */

insert  into `mt_book_cate`(`ID`,`MERCHANT_ID`,`STORE_ID`,`NAME`,`LOGO`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`SORT`,`OPERATOR`,`STATUS`) values 
(1,1,0,'测试001','/static/defaultImage/banner-2.png?v=1','11211111','2024-08-05 14:36:16','2024-08-14 09:52:29',2,'fuint','A'),
(2,1,0,'测试002','/static/defaultImage/banner-1.png?v=1','223112','2024-08-05 15:57:39','2024-08-13 18:55:32',1,'fuint','A');

/*Table structure for table `mt_book_item` */

DROP TABLE IF EXISTS `mt_book_item`;

CREATE TABLE `mt_book_item` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `CATE_ID` int DEFAULT '0' COMMENT '预约分类ID',
  `BOOK_ID` int DEFAULT '0' COMMENT '预约ID',
  `USER_ID` int DEFAULT '0' COMMENT '预约用户ID',
  `GOODS_ID` int DEFAULT '0' COMMENT '预约服务ID',
  `VERIFY_CODE` varchar(10) DEFAULT '' COMMENT '核销码',
  `CONTACT` varchar(30) DEFAULT NULL COMMENT '预约联系人',
  `MOBILE` varchar(30) DEFAULT NULL COMMENT '预约手机号',
  `SERVICE_DATE` datetime DEFAULT NULL COMMENT '预约日期',
  `SERVICE_TIME` varchar(100) DEFAULT NULL COMMENT '预约时间段',
  `SERVICE_STAFF_ID` int DEFAULT NULL COMMENT '预约员工ID',
  `REMARK` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '预约说明',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='预约详情表';

/*Data for the table `mt_book_item` */

/*Table structure for table `mt_cart` */

DROP TABLE IF EXISTS `mt_cart`;

CREATE TABLE `mt_cart` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `USER_ID` int NOT NULL DEFAULT '0' COMMENT '会员ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `TABLE_ID` int DEFAULT '0' COMMENT '桌码ID',
  `IS_VISITOR` char(1) DEFAULT 'N' COMMENT '是否游客',
  `HANG_NO` varchar(10) DEFAULT '' COMMENT '挂单号',
  `SKU_ID` int DEFAULT '0' COMMENT 'skuID',
  `GOODS_ID` int DEFAULT '0' COMMENT '商品ID',
  `NUM` int DEFAULT '1' COMMENT '数量',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=15 DEFAULT CHARSET=utf8 COMMENT='购物车';

/*Data for the table `mt_cart` */

insert  into `mt_cart`(`ID`,`USER_ID`,`MERCHANT_ID`,`STORE_ID`,`TABLE_ID`,`IS_VISITOR`,`HANG_NO`,`SKU_ID`,`GOODS_ID`,`NUM`,`CREATE_TIME`,`UPDATE_TIME`,`STATUS`) values 
(14,1,1,2,0,'N','',0,1,1,'2024-08-23 16:45:53','2024-08-23 16:45:53','A');

/*Table structure for table `mt_commission_cash` */

DROP TABLE IF EXISTS `mt_commission_cash`;

CREATE TABLE `mt_commission_cash` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `SETTLE_NO` varchar(32) DEFAULT NULL COMMENT '结算单号',
  `UUID` varchar(32) DEFAULT NULL COMMENT '结算UUID',
  `MERCHANT_ID` int NOT NULL COMMENT '商户ID',
  `STORE_ID` int DEFAULT NULL COMMENT '店铺ID',
  `USER_ID` int DEFAULT NULL COMMENT '会员ID',
  `STAFF_ID` int DEFAULT NULL COMMENT '员工ID',
  `AMOUNT` decimal(10,2) DEFAULT NULL COMMENT '金额',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `DESCRIPTION` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A:待确认,B:已确认,C:已支付,D:已作废',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分佣提现记录表';

/*Data for the table `mt_commission_cash` */

/*Table structure for table `mt_commission_log` */

DROP TABLE IF EXISTS `mt_commission_log`;

CREATE TABLE `mt_commission_log` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int NOT NULL COMMENT '商户ID',
  `TARGET` varchar(30) DEFAULT '' COMMENT '对象,member:会员分销；staff：员工提成',
  `TYPE` varchar(30) NOT NULL COMMENT '分佣类型',
  `LEVEL` int DEFAULT '1' COMMENT '分销等级',
  `USER_ID` int DEFAULT NULL COMMENT '会员ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `STAFF_ID` int DEFAULT '0' COMMENT '员工ID',
  `ORDER_ID` int DEFAULT '0' COMMENT '订单ID',
  `AMOUNT` decimal(10,2) DEFAULT NULL COMMENT '分佣金额',
  `RULE_ID` int DEFAULT NULL COMMENT '分佣规则ID',
  `RULE_ITEM_ID` int DEFAULT NULL COMMENT '分佣规则项ID',
  `DESCRIPTION` varchar(500) DEFAULT NULL COMMENT '备注信息',
  `SETTLE_UUID` varchar(32) DEFAULT NULL COMMENT '结算uuid',
  `CASH_ID` int DEFAULT NULL COMMENT '提现记录ID',
  `IS_CASH` char(1) DEFAULT 'N' COMMENT '是否提现，Y：是；N：否',
  `CASH_TIME` datetime DEFAULT NULL COMMENT '提现时间',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A：待结算；B：已结算；C：已作废',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='佣金记录表';

/*Data for the table `mt_commission_log` */

/*Table structure for table `mt_commission_relation` */

DROP TABLE IF EXISTS `mt_commission_relation`;

CREATE TABLE `mt_commission_relation` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int NOT NULL COMMENT '商户ID',
  `USER_ID` int DEFAULT NULL COMMENT '邀请会员ID',
  `LEVEL` int DEFAULT '1' COMMENT '等级',
  `INVITE_CODE` varchar(32) DEFAULT '' COMMENT '邀请码',
  `SUB_USER_ID` int DEFAULT NULL COMMENT '被邀请会员ID',
  `DESCRIPTION` varchar(500) DEFAULT NULL COMMENT '说明',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A：激活；D：删除',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会员分销关系表';

/*Data for the table `mt_commission_relation` */

/*Table structure for table `mt_commission_rule` */

DROP TABLE IF EXISTS `mt_commission_rule`;

CREATE TABLE `mt_commission_rule` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `NAME` varchar(100) DEFAULT '' COMMENT '规则名称',
  `TYPE` varchar(30) DEFAULT NULL COMMENT '方案类型',
  `TARGET` varchar(30) DEFAULT '' COMMENT '方案对象,member:会员分销；staff：员工提成',
  `MERCHANT_ID` int NOT NULL COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `STORE_IDS` varchar(500) DEFAULT '' COMMENT '适用店铺',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `DESCRIPTION` varchar(1000) DEFAULT NULL COMMENT '备注信息',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A：激活；N：禁用；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='方案规则表';

/*Data for the table `mt_commission_rule` */

/*Table structure for table `mt_commission_rule_item` */

DROP TABLE IF EXISTS `mt_commission_rule_item`;

CREATE TABLE `mt_commission_rule_item` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `TYPE` varchar(30) DEFAULT NULL COMMENT '分佣类型',
  `RULE_ID` int NOT NULL DEFAULT '0' COMMENT '规则ID',
  `MERCHANT_ID` int NOT NULL COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '适用店铺',
  `TARGET` varchar(30) DEFAULT NULL COMMENT '分佣对象',
  `TARGET_ID` int NOT NULL DEFAULT '0' COMMENT '对象ID',
  `METHOD` varchar(30) DEFAULT NULL COMMENT '提成方式（按比例/固定金额）',
  `STORE_IDS` varchar(500) DEFAULT '' COMMENT '适用店铺',
  `GUEST` decimal(10,2) DEFAULT NULL COMMENT '散客佣金',
  `SUB_GUEST` decimal(10,2) DEFAULT NULL COMMENT '二级散客佣金',
  `MEMBER` decimal(10,2) DEFAULT NULL COMMENT '会员佣金',
  `SUB_MEMBER` decimal(10,2) DEFAULT NULL COMMENT '二级会员佣金',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A：激活；N：禁用；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='分佣提成规则项目表';

/*Data for the table `mt_commission_rule_item` */

/*Table structure for table `mt_confirm_log` */

DROP TABLE IF EXISTS `mt_confirm_log`;

CREATE TABLE `mt_confirm_log` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `CODE` varchar(32) NOT NULL DEFAULT '' COMMENT '编码',
  `AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '核销金额',
  `COUPON_ID` int DEFAULT '0' COMMENT '卡券ID',
  `USER_COUPON_ID` int NOT NULL DEFAULT '0' COMMENT '用户券ID',
  `ORDER_ID` int DEFAULT '0' COMMENT '订单ID',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `USER_ID` int NOT NULL DEFAULT '0' COMMENT '卡券所属用户ID',
  `OPERATOR_USER_ID` int DEFAULT NULL COMMENT '核销者用户ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int NOT NULL DEFAULT '0' COMMENT '核销店铺ID',
  `STATUS` varchar(1) NOT NULL COMMENT '状态，A正常核销；D：撤销使用',
  `CANCEL_TIME` datetime DEFAULT NULL COMMENT '撤销时间',
  `OPERATOR` varchar(30) DEFAULT NULL COMMENT '最后操作人',
  `OPERATOR_FROM` varchar(30) DEFAULT 'mt_user' COMMENT '操作来源user_id对应表t_account 还是 mt_user',
  `REMARK` varchar(500) DEFAULT '' COMMENT '备注信息',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='核销记录表';

/*Data for the table `mt_confirm_log` */

/*Table structure for table `mt_coupon` */

DROP TABLE IF EXISTS `mt_coupon`;

CREATE TABLE `mt_coupon` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `GROUP_ID` int NOT NULL DEFAULT '0' COMMENT '券组ID',
  `TYPE` char(1) DEFAULT 'C' COMMENT '券类型，C优惠券；P预存卡；T集次卡',
  `NAME` varchar(100) NOT NULL DEFAULT '' COMMENT '券名称',
  `IS_GIVE` tinyint(1) DEFAULT '0' COMMENT '是否允许转赠',
  `GRADE_IDS` varchar(100) DEFAULT '' COMMENT '适用会员等级',
  `POINT` int DEFAULT '0' COMMENT '获得卡券所消耗积分',
  `APPLY_GOODS` varchar(20) DEFAULT '' COMMENT '适用商品：allGoods、parkGoods',
  `RECEIVE_CODE` varchar(32) DEFAULT '' COMMENT '领取码',
  `USE_FOR` varchar(30) DEFAULT '' COMMENT '使用专项',
  `EXPIRE_TYPE` varchar(30) DEFAULT '' COMMENT '过期类型',
  `EXPIRE_TIME` int DEFAULT '0' COMMENT '有效期，单位：天',
  `BEGIN_TIME` datetime DEFAULT NULL COMMENT '开始有效期',
  `END_TIME` datetime DEFAULT NULL COMMENT '结束有效期',
  `AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '面额',
  `SEND_WAY` varchar(20) DEFAULT 'backend' COMMENT '发放方式',
  `SEND_NUM` int unsigned DEFAULT '1' COMMENT '每次发放数量',
  `TOTAL` int DEFAULT '0' COMMENT '发行数量',
  `LIMIT_NUM` int DEFAULT '1' COMMENT '每人拥有数量限制',
  `EXCEPT_TIME` varchar(500) DEFAULT '' COMMENT '不可用日期，逗号隔开。周末：weekend；其他：2019-01-02_2019-02-09',
  `STORE_IDS` varchar(100) DEFAULT '' COMMENT '所属店铺ID,逗号隔开',
  `DESCRIPTION` varchar(2000) DEFAULT '' COMMENT '描述信息',
  `IMAGE` varchar(100) DEFAULT '' COMMENT '效果图片',
  `REMARKS` varchar(1000) DEFAULT '' COMMENT '后台备注',
  `IN_RULE` varchar(1000) DEFAULT '' COMMENT '获取券的规则',
  `OUT_RULE` varchar(1000) DEFAULT '' COMMENT '核销券的规则',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT 'A：正常；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='卡券信息表';

/*Data for the table `mt_coupon` */

/*Table structure for table `mt_coupon_goods` */

DROP TABLE IF EXISTS `mt_coupon_goods`;

CREATE TABLE `mt_coupon_goods` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `COUPON_ID` int NOT NULL COMMENT '卡券ID',
  `GOODS_ID` int NOT NULL COMMENT '商品ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  `STATUS` char(1) NOT NULL DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='卡券商品表';

/*Data for the table `mt_coupon_goods` */

/*Table structure for table `mt_coupon_group` */

DROP TABLE IF EXISTS `mt_coupon_group`;

CREATE TABLE `mt_coupon_group` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `NAME` varchar(100) NOT NULL DEFAULT '' COMMENT '券组名称',
  `MONEY` decimal(18,2) DEFAULT '0.00' COMMENT '价值金额',
  `NUM` int DEFAULT '0' COMMENT '券种类数量',
  `TOTAL` int DEFAULT '0' COMMENT '发行数量',
  `DESCRIPTION` varchar(2000) DEFAULT '' COMMENT '备注',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建日期',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新日期',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) NOT NULL DEFAULT 'A' COMMENT 'A：正常；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='优惠券组';

/*Data for the table `mt_coupon_group` */

/*Table structure for table `mt_freight` */

DROP TABLE IF EXISTS `mt_freight`;

CREATE TABLE `mt_freight` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `NAME` varchar(100) NOT NULL COMMENT '名称',
  `TYPE` int NOT NULL COMMENT '计费类型，1：按件数；2：按重量',
  `AMOUNT` decimal(10,2) NOT NULL COMMENT '费用',
  `INCRE_AMOUNT` decimal(10,2) NOT NULL COMMENT '续费',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  `STATUS` char(1) NOT NULL COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运费模板';

/*Data for the table `mt_freight` */

/*Table structure for table `mt_freight_region` */

DROP TABLE IF EXISTS `mt_freight_region`;

CREATE TABLE `mt_freight_region` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `FREIGHT_ID` int NOT NULL COMMENT '运费模板ID',
  `PROVINCE_ID` int NOT NULL COMMENT '省份ID',
  `CITY_ID` int NOT NULL COMMENT '城市ID',
  `AREA_ID` int NOT NULL COMMENT '区域ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  `STATUS` char(1) NOT NULL COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='运费模板地区';

/*Data for the table `mt_freight_region` */

/*Table structure for table `mt_give` */

DROP TABLE IF EXISTS `mt_give`;

CREATE TABLE `mt_give` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增',
  `USER_ID` int NOT NULL DEFAULT '0' COMMENT '获赠者用户ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `GIVE_USER_ID` int NOT NULL DEFAULT '0' COMMENT '赠送者用户ID',
  `MOBILE` varchar(20) NOT NULL DEFAULT '' COMMENT '赠予对象手机号',
  `USER_MOBILE` varchar(20) NOT NULL DEFAULT '' COMMENT '用户手机',
  `GROUP_IDS` varchar(200) NOT NULL DEFAULT '' COMMENT '券组ID，逗号隔开',
  `GROUP_NAMES` varchar(500) NOT NULL DEFAULT '' COMMENT '券组名称，逗号隔开',
  `COUPON_IDS` varchar(200) NOT NULL DEFAULT '' COMMENT '券ID，逗号隔开',
  `COUPON_NAMES` varchar(500) NOT NULL DEFAULT '' COMMENT '券名称，逗号隔开',
  `NUM` int NOT NULL DEFAULT '0' COMMENT '数量',
  `MONEY` decimal(10,2) NOT NULL COMMENT '总金额',
  `NOTE` varchar(200) DEFAULT '' COMMENT '备注',
  `MESSAGE` varchar(500) DEFAULT '' COMMENT '留言',
  `CREATE_TIME` datetime NOT NULL COMMENT '赠送时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  `STATUS` char(1) NOT NULL DEFAULT 'A' COMMENT '状态，A正常；C取消',
  PRIMARY KEY (`ID`),
  KEY `index_user_id` (`USER_ID`) USING BTREE,
  KEY `index_give_user_id` (`GIVE_USER_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='转赠记录表';

/*Data for the table `mt_give` */

/*Table structure for table `mt_give_item` */

DROP TABLE IF EXISTS `mt_give_item`;

CREATE TABLE `mt_give_item` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `GIVE_ID` int NOT NULL COMMENT '转赠ID',
  `USER_COUPON_ID` int NOT NULL COMMENT '用户电子券ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  `STATUS` char(1) NOT NULL COMMENT '状态，A正常；D删除',
  PRIMARY KEY (`ID`),
  KEY `index_give_id` (`GIVE_ID`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='转赠明细表';

/*Data for the table `mt_give_item` */

/*Table structure for table `mt_goods` */

DROP TABLE IF EXISTS `mt_goods`;

CREATE TABLE `mt_goods` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `TYPE` varchar(30) DEFAULT 'product' COMMENT '商品类别',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺ID',
  `NAME` varchar(100) DEFAULT '' COMMENT '商品名称',
  `CATE_ID` int DEFAULT '0' COMMENT '分类ID',
  `GOODS_NO` varchar(100) DEFAULT '' COMMENT '商品编码',
  `IS_SINGLE_SPEC` char(1) NOT NULL DEFAULT 'Y' COMMENT '是否单规格',
  `LOGO` varchar(200) DEFAULT '' COMMENT '主图地址',
  `IMAGES` varchar(1000) DEFAULT '' COMMENT '图片地址',
  `PRICE` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '价格',
  `LINE_PRICE` decimal(10,2) unsigned DEFAULT '0.00' COMMENT '划线价格',
  `STOCK` int unsigned DEFAULT '0' COMMENT '库存',
  `WEIGHT` decimal(10,2) DEFAULT '0.00' COMMENT '重量',
  `COUPON_IDS` varchar(500) DEFAULT '' COMMENT '关联卡券ID',
  `SERVICE_TIME` int DEFAULT '0' COMMENT '服务时长，单位：分钟',
  `INIT_SALE` int DEFAULT '0' COMMENT '初始销量',
  `SALE_POINT` varchar(100) DEFAULT '' COMMENT '商品卖点',
  `CAN_USE_POINT` char(1) DEFAULT 'N' COMMENT '可否使用积分抵扣',
  `IS_MEMBER_DISCOUNT` char(1) DEFAULT 'Y' COMMENT '会员是否有折扣',
  `SORT` int DEFAULT '0' COMMENT '排序',
  `DESCRIPTION` text COMMENT '商品描述',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT NULL COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT 'A：正常；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=570 DEFAULT CHARSET=utf8 COMMENT='商品表';

/*Data for the table `mt_goods` */

insert  into `mt_goods`(`ID`,`TYPE`,`MERCHANT_ID`,`STORE_ID`,`NAME`,`CATE_ID`,`GOODS_NO`,`IS_SINGLE_SPEC`,`LOGO`,`IMAGES`,`PRICE`,`LINE_PRICE`,`STOCK`,`WEIGHT`,`COUPON_IDS`,`SERVICE_TIME`,`INIT_SALE`,`SALE_POINT`,`CAN_USE_POINT`,`IS_MEMBER_DISCOUNT`,`SORT`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`OPERATOR`,`STATUS`) values 
(1,'goods',1,0,'辣味煲仔饭',1,'133791810418538','Y','/static/uploadImages/20240508/8a9ab63f7aed43b992117e4476654031.jpg','[\"/static/uploadImages/20240508/8a9ab63f7aed43b992117e4476654031.jpg\"]',120.00,200.00,131,1.00,'',0,1100,'超级防水','Y','Y',0,'<p><br></p>','2021-10-13 13:56:04','2024-05-08 15:46:20','fuint','A'),
(2,'goods',1,0,'香橙茉莉茶',5,'6975486820418','Y','/static/uploadImages/20240508/34bdc8a42191463eadb5e71a429a3f8e.jpg','[\"/static/uploadImages/20240508/34bdc8a42191463eadb5e71a429a3f8e.jpg\"]',200.00,240.00,29928,0.00,'',0,2327,'小清新','Y','Y',1,'<h2>312312</h2>','2021-10-13 14:19:45','2024-05-08 16:02:47','fuint','A'),
(3,'service',1,0,'阿婆菠萝牛杂',1,'192609099895006','N','/static/uploadImages/20240508/ecc8e8849d5c4751998b661f791c5d75.jpg','[\"/static/uploadImages/20240508/ecc8e8849d5c4751998b661f791c5d75.jpg\"]',145.00,155.00,39970,1.00,'',60,177,'超级实惠','Y','Y',0,'<p>测试内容！</p>','2021-10-14 01:08:45','2024-05-08 15:49:42','fuint','A'),
(4,'goods',1,0,'铁板牛排饭或意面',1,'6914068026176','N','/static/uploadImages/20240508/4cf98cd735c64bbca4e11a6b7c6d23cc.jpg','[\"/static/uploadImages/20240508/4cf98cd735c64bbca4e11a6b7c6d23cc.jpg\"]',199.00,299.00,89985,1.00,'',0,1443,'一体成型','Y','N',1,'<p><br></p>','2021-10-14 05:46:23','2024-05-08 16:02:33','fuint','A'),
(5,'goods',1,0,'芒果雪糕杯',2,'6922577729501','N','/static/uploadImages/20240508/e34afd8cc9ad4f7c8d684d2a0118db1f.jpg','[\"/static/uploadImages/20240508/e34afd8cc9ad4f7c8d684d2a0118db1f.jpg\"]',80.00,120.00,179999,1.00,'',0,1286,'可伸缩','Y','Y',1,'<p><br></p>','2021-10-14 05:47:31','2024-05-08 16:03:17','fuint','A'),
(6,'goods',1,0,'香茜牛肉肠粉',1,'136131463356505','N','/static/uploadImages/20240508/327f586320734ed2a84fbd81da51e18e.jpg','[\"/static/uploadImages/20240508/327f586320734ed2a84fbd81da51e18e.jpg\",\"/static/uploadImages/20240508/198dc4b562674e22a68a4172c0a5deeb.jpg\"]',66.00,98.00,72434,1.00,'',11,4562,'厨房必备小帮手','Y','N',0,'<p><br></p>','2021-10-14 05:48:43','2024-05-08 17:39:54','fuint','A'),
(7,'goods',1,0,'养生粗粮拼盘',4,'135249719542604','Y','/static/uploadImages/20240508/d55d48eb7a394ab29f5e66039777ae21.jpg','[\"/static/uploadImages/20240508/d55d48eb7a394ab29f5e66039777ae21.jpg\"]',48.00,68.00,998,0.00,'',0,2,'健康','N','Y',0,NULL,'2024-05-08 16:04:19','2024-05-08 16:04:49','fuint','A');

/*Table structure for table `mt_goods_cate` */

DROP TABLE IF EXISTS `mt_goods_cate`;

CREATE TABLE `mt_goods_cate` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺',
  `NAME` varchar(100) DEFAULT '' COMMENT '分类名称',
  `LOGO` varchar(200) DEFAULT '' COMMENT 'LOGO地址',
  `DESCRIPTION` text COMMENT '分类描述',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT NULL COMMENT '最后操作人',
  `SORT` int DEFAULT '0' COMMENT '排序',
  `STATUS` char(1) DEFAULT 'A' COMMENT 'A：正常；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=266 DEFAULT CHARSET=utf8 COMMENT='商品分类表';

/*Data for the table `mt_goods_cate` */

insert  into `mt_goods_cate`(`ID`,`MERCHANT_ID`,`STORE_ID`,`NAME`,`LOGO`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`OPERATOR`,`SORT`,`STATUS`) values 
(1,1,0,'招牌菜','/static/defaultImage/love.png','1234','2021-10-09 06:27:11','2024-05-08 14:24:28','fuint',1,'A'),
(2,1,0,'甜品小食','/static/defaultImage/life.png','','2021-10-09 06:27:11','2024-05-08 14:25:35','fuint',4,'A'),
(3,1,0,'人气推荐','/static/defaultImage/hot.png','好物推荐','2021-10-09 06:27:11','2024-05-08 14:24:37','fuint',2,'A'),
(4,1,0,'健康主食','/static/defaultImage/main.png','低价甩卖','2021-10-14 02:20:00','2024-05-08 14:25:11','fuint',3,'A'),
(5,1,0,'酒水饮料','/static/defaultImage/drink.png','酒水饮料','2024-05-08 14:27:16','2024-05-08 14:29:41','fuint',5,'A');

/*Table structure for table `mt_goods_sku` */

DROP TABLE IF EXISTS `mt_goods_sku`;

CREATE TABLE `mt_goods_sku` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `SKU_NO` varchar(50) DEFAULT '' COMMENT 'sku编码',
  `LOGO` varchar(255) DEFAULT '' COMMENT '图片',
  `GOODS_ID` int NOT NULL DEFAULT '0' COMMENT '商品ID',
  `SPEC_IDS` varchar(100) NOT NULL DEFAULT '' COMMENT '规格ID',
  `STOCK` int NOT NULL DEFAULT '0' COMMENT '库存',
  `PRICE` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '价格',
  `LINE_PRICE` decimal(10,2) NOT NULL DEFAULT '0.00' COMMENT '划线价格',
  `WEIGHT` decimal(10,2) DEFAULT '0.00' COMMENT '重量',
  `STATUS` char(1) NOT NULL DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=1006 DEFAULT CHARSET=utf8 COMMENT='商品SKU表';

/*Data for the table `mt_goods_sku` */

insert  into `mt_goods_sku`(`ID`,`SKU_NO`,`LOGO`,`GOODS_ID`,`SPEC_IDS`,`STOCK`,`PRICE`,`LINE_PRICE`,`WEIGHT`,`STATUS`) values 
(142,'','',27,'46',0,0.00,0.00,0.00,'A'),
(143,'','',27,'47',0,0.00,0.00,0.00,'A'),
(145,'','',37,'56',51,200.00,300.00,1.20,'A'),
(189,'','',40,'66-69',1,1.00,1.00,1.00,'A'),
(190,'','',40,'66-70',1,1.00,1.00,1.00,'A'),
(191,'','',40,'67-69',1,1.00,1.00,1.00,'A'),
(192,'','',40,'67-70',1,1.00,1.00,1.00,'A'),
(193,'','',40,'68-69',1,1.00,1.00,1.00,'A'),
(194,'','',40,'68-70',1,1.00,1.00,1.00,'A'),
(261,'8888880','',45,'86',88,10.00,20.00,2.00,'A'),
(262,'8888881','',45,'87',90,10.00,20.00,2.00,'A'),
(263,'8888882','',45,'88',89,10.00,20.00,2.00,'A'),
(264,'8888883','',45,'89',90,10.00,20.00,2.00,'A'),
(265,'34234','',48,'90',220,21.00,112.00,1.00,'A'),
(266,'234230','',51,'92',100,10.00,12.00,1.00,'A'),
(267,'234231','',51,'93',98,10.00,12.00,1.00,'A'),
(269,'34235','',48,'95',887,20.00,120.00,1.00,'A'),
(315,'2222','/uploads/20230228/656a1aef1e8c4a8e8e80c5b817e15255.jpg',79,'138-142',999,24.00,24.00,2.00,'A'),
(317,'4444','',79,'139-142',999,24.00,24.00,2.00,'A'),
(339,'111111','/uploads/20230307/55126b64546945f4a6393ad5259cfc03.jpg',86,'153',100,0.01,99.00,0.00,'A'),
(340,'222222','/uploads/20230307/67d54c99787b4114affd86c5248d1431.jpg',86,'157',100,0.01,99.00,0.00,'A'),
(341,'333333','/uploads/20230307/72cc5a1c16ee4c21a8ea35612741f87e.jpg',86,'158',98,0.01,99.00,0.00,'A'),
(386,'','',91,'185',0,0.00,0.00,0.00,'A'),
(387,'SKU12434','/uploads/20230314/7333fe6a66884de59e560278c2de609b.jpg',95,'191-193',99,10.00,15.00,1.00,'A'),
(388,'SKU12432','/uploads/20230314/0d4f3ea2741e495f82a23f8443b54e18.jpg',95,'191-194',98,10.00,15.00,1.00,'A'),
(389,'SKU12442','/uploads/20230314/e87014e2f81c466398e35a01472cedb4.jpg',95,'192-193',100,19.00,25.00,1.00,'A'),
(390,'SKU12423','/uploads/20230314/4aab2fc389a54de6b66c19b69eff8ebc.jpg',95,'192-194',98,19.00,25.00,1.00,'A'),
(391,'瓶','',96,'195',4,3.00,3.00,500.00,'A'),
(424,'1230','',119,'220',100,1.00,0.00,1.00,'A'),
(425,'1231','',119,'223',100,1.00,0.00,1.00,'A'),
(426,'1232','',119,'224',100,1.00,0.00,1.00,'A'),
(427,'1233','',119,'225',100,1.00,0.00,1.00,'A'),
(519,'','',140,'262',0,0.00,0.00,0.00,'A'),
(547,'79232344230','',165,'274',100,3.99,5.00,0.25,'A'),
(548,'79232344231','',165,'275',100,6.00,7.00,0.25,'A'),
(555,'','',169,'282-286',9,189.00,289.00,0.00,'A'),
(556,'','',169,'282-287',10,189.00,289.00,0.00,'A'),
(557,'','',169,'282-288',10,189.00,289.00,0.00,'A'),
(558,'','',169,'282-289',10,189.00,289.00,0.00,'A'),
(559,'','',169,'282-290',10,189.00,289.00,0.00,'A'),
(560,'','',169,'283-286',10,189.00,289.00,0.00,'A'),
(561,'','',169,'283-287',9,189.00,289.00,0.00,'A'),
(562,'','',169,'283-288',9,189.00,289.00,0.00,'A'),
(563,'','',169,'283-289',10,189.00,289.00,0.00,'A'),
(564,'','',169,'283-290',10,189.00,289.00,0.00,'A'),
(565,'','',169,'285-286',10,189.00,289.00,0.00,'A'),
(566,'','',169,'285-287',10,189.00,289.00,0.00,'A'),
(567,'','',169,'285-288',8,189.00,289.00,0.00,'A'),
(568,'','',169,'285-289',9,189.00,289.00,0.00,'A'),
(569,'','',169,'285-290',6,189.00,289.00,0.00,'A'),
(583,'4353452','/uploads/20230609/92a39796268246c7865ff10d560a9d10.gif',176,'296',7,56.00,80.00,0.00,'A'),
(584,'45345','',177,'297',88,677.00,5888.00,0.00,'A'),
(585,'45345345','',177,'299',75,888.00,8888.00,0.00,'A'),
(604,'','',169,'309-286',0,0.00,0.00,0.00,'A'),
(605,'','',169,'309-287',0,0.00,0.00,0.00,'A'),
(606,'','',169,'309-288',0,0.00,0.00,0.00,'A'),
(607,'','',169,'309-289',0,0.00,0.00,0.00,'A'),
(608,'','',169,'309-290',0,0.00,0.00,0.00,'A'),
(609,'','',183,'310-311',10,99.00,0.00,0.00,'A'),
(610,'','',183,'310-312',10,99.00,0.00,0.00,'A'),
(611,'','',183,'310-313',10,99.00,0.00,0.00,'A'),
(612,'','',183,'310-314',10,99.00,0.00,0.00,'A'),
(613,'','',183,'310-315',10,99.00,0.00,0.00,'A'),
(614,'','',183,'310-316',10,99.00,0.00,0.00,'A'),
(615,'','',183,'317-311',10,99.00,0.00,0.00,'A'),
(616,'','',183,'317-312',10,99.00,0.00,0.00,'A'),
(617,'','',183,'317-313',10,99.00,0.00,0.00,'A'),
(618,'','',183,'317-314',10,99.00,0.00,0.00,'A'),
(619,'','',183,'317-315',10,99.00,0.00,0.00,'A'),
(620,'','',183,'317-316',10,99.00,0.00,0.00,'A'),
(621,'','',183,'318-311',10,99.00,0.00,0.00,'A'),
(622,'','',183,'318-312',10,99.00,0.00,0.00,'A'),
(623,'','',183,'318-313',10,99.00,0.00,0.00,'A'),
(624,'','',183,'318-314',10,99.00,0.00,0.00,'A'),
(625,'','',183,'318-315',10,99.00,0.00,0.00,'A'),
(626,'','',183,'318-316',9,99.00,0.00,0.00,'A'),
(627,'1','',187,'324',20,22.00,25.00,0.50,'A'),
(628,'2','',187,'325',20,40.00,50.00,1.00,'A'),
(629,'1','',185,'327',0,120.00,150.00,5.00,'A'),
(630,'2','',185,'328',1,150.00,180.00,5.00,'A'),
(631,'3','',185,'329',0,200.00,250.00,5.00,'A'),
(644,'','/uploads/20230628/0c17bcee89dc4838be59aba2db990e22.png',197,'339',997,68.00,0.00,0.00,'A'),
(659,'180521342436380','',207,'345-351-353',1,1.00,1.00,1.00,'A'),
(660,'180521342436381','',207,'348-351-353',1,1.00,1.00,1.00,'A'),
(661,'179699761475660','',232,'355-356',10,20.00,30.00,1.00,'A'),
(662,'179699761475661','',232,'355-357',10,30.00,60.00,1.00,'A'),
(663,'179699761475662','',232,'358-356',1,20.00,30.00,1.00,'A'),
(664,'179699761475663','',232,'358-357',2,30.00,60.00,1.00,'A'),
(669,'148503337738370','',305,'372-374',10,99.00,299.00,0.00,'A'),
(670,'148503337738371','',305,'372-375',10,99.00,299.00,0.00,'A'),
(671,'148503337738372','',305,'373-374',10,99.00,299.00,0.00,'A'),
(672,'148503337738373','',305,'373-375',10,99.00,299.00,0.00,'A'),
(673,'','',361,'388',0,0.00,0.00,0.00,'A'),
(674,'','',361,'389',0,0.00,0.00,0.00,'A'),
(675,'','',361,'390',0,0.00,0.00,0.00,'A'),
(676,'','',361,'391',0,0.00,0.00,0.00,'A'),
(677,'','',361,'393',0,0.00,0.00,0.00,'A'),
(689,'164348379384740','',382,'413-416-419',990,18.00,0.00,0.00,'A'),
(690,'164348379384741','',382,'413-417-419',990,18.00,0.00,0.00,'A'),
(691,'164348379384742','',382,'413-418-419',990,18.00,0.00,0.00,'A'),
(692,'164348379384743','',382,'414-416-419',990,18.00,0.00,0.00,'A'),
(693,'164348379384744','',382,'414-417-419',990,18.00,0.00,0.00,'A'),
(694,'164348379384745','',382,'414-418-419',990,18.00,0.00,0.00,'A'),
(695,'164348379384746','',382,'415-416-419',990,18.00,0.00,0.00,'A'),
(696,'164348379384747','',382,'415-417-419',990,18.00,0.00,0.00,'A'),
(697,'164348379384748','',382,'415-418-419',990,18.00,0.00,0.00,'A'),
(732,'155430306154340','',384,'430',1,1.00,1.00,1.00,'A'),
(754,'129192714545230','',409,'449-450-451-452-454-458',20,63.00,65.00,0.00,'A'),
(755,'129192714545230','',409,'449-450-451-452-455-458',20,420.00,450.00,0.00,'A'),
(756,'129192714545230','',409,'449-450-451-452-456-458',20,1700.00,2000.00,0.00,'A'),
(757,'129192714545230','',409,'449-450-451-452-457-458',20,2.78,3.00,0.00,'A'),
(758,'129192714545230','',409,'449-450-451-452-454-459',19,63.00,65.00,0.00,'A'),
(759,'129192714545230','',409,'449-450-451-452-455-459',18,420.00,450.00,0.00,'A'),
(760,'129192714545230','',409,'449-450-451-452-456-459',20,1700.00,2000.00,0.00,'A'),
(761,'129192714545230','',409,'449-450-451-452-457-459',20,2.78,3.00,0.00,'A'),
(762,'149521695678840','',415,'462-463',1211,123.00,333.00,1.00,'A'),
(763,'152156749387640','',417,'464',11223,222.00,333.00,0.00,'A'),
(765,'129851938585150','',434,'471',19,13.00,12.00,12.00,'A'),
(766,'129851938585151','',434,'472',14,13.00,12.00,12.00,'A'),
(777,'L','',442,'477',65,25.00,35.00,0.00,'A'),
(778,'M','',442,'478',55,20.00,25.00,0.00,'A'),
(779,'S','',442,'479',55,15.00,20.00,0.00,'A'),
(780,'11','',446,'481',11,12.00,1.00,1.00,'A'),
(781,'1112','',446,'482',11,33.00,2.00,1.00,'A'),
(803,'184020987693390','',5,'500-504-734',10000,80.00,120.00,1.00,'A'),
(804,'184020987693393','',5,'500-506-734',10001,80.00,120.00,1.00,'A'),
(805,'184020987693396','',5,'500-507-734',10001,80.00,120.00,1.00,'A'),
(806,'184020987693399','',5,'501-504-734',10001,80.00,120.00,1.00,'A'),
(807,'1840209876933912','',5,'501-506-734',10001,80.00,120.00,1.00,'A'),
(808,'1840209876933915','',5,'501-507-734',10001,80.00,120.00,1.00,'A'),
(832,'','',450,'516',30,36.00,0.00,0.00,'D'),
(833,'','',450,'517',1,3.00,0.00,0.00,'A'),
(836,'','',451,'521',30,3.00,1.00,0.00,'A'),
(837,'','',451,'522',2,36.00,15.00,0.00,'A'),
(838,'','',451,'520',0,0.00,0.00,0.00,'A'),
(839,'','',452,'524',17,3.00,0.00,0.00,'A'),
(840,'','',452,'525',1,36.00,0.00,0.00,'A'),
(867,'1','',486,'584-586',1,1.00,1.00,0.00,'A'),
(868,'1','',486,'585-586',1,3.00,1.00,0.00,'A'),
(869,'1','',486,'584-587',1,2.00,1.00,0.00,'A'),
(870,'1','',486,'585-587',1,4.00,1.00,0.00,'A'),
(871,'1','',488,'617',1000,0.01,0.01,0.00,'A'),
(872,'1','',488,'618',1000,0.01,0.01,0.00,'A'),
(893,'','',495,'623',0,0.00,0.00,0.00,'A'),
(894,'','',495,'624',0,0.00,0.00,0.00,'A'),
(895,'18840180844394','/uploads/20240305/43c590d138f44f7aa00b47c35014b713.jpeg',499,'640',100,2.00,2.50,0.00,'A'),
(896,'18840180844391','/uploads/20240305/f0fb2386971243aeb50d0fb23f63efa8.jpeg',499,'641',10,45.00,48.00,0.00,'A'),
(903,'182832072408890','',506,'655',2,1.00,1.00,1.00,'A'),
(941,'120546829322774','',4,'63-660-753',9999,199.00,299.00,1.00,'A'),
(942,'120546829322775','',4,'63-690-753',9999,199.00,299.00,1.00,'A'),
(943,'120546829322777','',4,'689-660-753',9994,199.00,299.00,1.00,'A'),
(944,'120546829322778','',4,'689-690-753',9991,199.00,299.00,1.00,'A'),
(945,'123564533666770','',6,'20-64-305',0,66.00,98.00,1.00,'A'),
(946,'123564533666771','',6,'20-64-646',10337,66.00,98.00,1.00,'A'),
(947,'123564533666772','',6,'20-692-305',10001,66.00,98.00,1.00,'A'),
(948,'123564533666773','',6,'20-692-646',10015,66.00,98.00,1.00,'A'),
(949,'123564533666774','',6,'673-64-305',12005,66.00,98.00,1.00,'A'),
(950,'123564533666775','',6,'673-64-646',10109,66.00,98.00,1.00,'A'),
(951,'123564533666776','',6,'673-692-305',9999,66.00,98.00,1.00,'A'),
(952,'123564533666777','',6,'673-692-646',9979,66.00,98.00,1.00,'A'),
(955,'164065556255050','',527,'698',130,20.00,25.00,1.00,'A'),
(956,'164065556255051','',527,'699',30,20.00,25.00,1.00,'A'),
(957,'153518315776260','',530,'701',10,13.00,16.00,0.50,'A'),
(958,'153518315776261','',530,'702',10,13.00,16.00,0.60,'A'),
(959,'100411284589940','',537,'715-718',54,50.00,60.00,0.10,'A'),
(960,'100411284589941','',537,'715-719',54,50.00,60.00,0.10,'A'),
(961,'100411284589942','',537,'716-718',54,50.00,60.00,0.10,'A'),
(962,'100411284589943','',537,'716-719',54,50.00,60.00,0.10,'A'),
(963,'100411284589944','',537,'717-718',82,50.00,60.00,0.10,'A'),
(964,'100411284589945','',537,'717-719',54,50.00,60.00,0.10,'A'),
(965,'115790470967510','',538,'721',29,60.00,70.00,1.00,'A'),
(966,'115790470967511','',538,'722',27,60.00,70.00,1.00,'A'),
(967,'115790470967512','',538,'723',32,60.00,70.00,1.00,'A'),
(968,'199209908341600','/uploads/20240409/7878604463a7410c96503fd1469b14c9.png',534,'704-720-706',100,1499.00,1699.00,13.50,'A'),
(969,'199209908341601','/uploads/20240409/7148eea5d2a141d7a4b8afc8dabfac00.png',534,'704-724-706',110,1699.00,1999.00,15.50,'A'),
(970,'','',539,'725-727',2,11.00,0.00,0.00,'A'),
(971,'','',539,'725-728',3,13.00,0.00,0.00,'A'),
(972,'','',539,'726-727',5,12.00,0.00,0.00,'A'),
(973,'','',539,'726-728',5,14.00,0.00,0.00,'A'),
(974,'185123118004130','',540,'729-731-733',11,11.00,11.00,11.00,'A'),
(975,'185123118004131','',540,'729-732-733',11,11.00,11.00,11.00,'A'),
(976,'185123118004132','',540,'730-731-733',11,11.00,11.00,11.00,'A'),
(977,'185123118004133','',540,'730-732-733',11,11.00,11.00,11.00,'A'),
(978,'184020987693391','',5,'500-504-735',10000,80.00,120.00,1.00,'A'),
(979,'184020987693394','',5,'500-506-735',10000,80.00,120.00,1.00,'A'),
(980,'184020987693397','',5,'500-507-735',10001,80.00,120.00,1.00,'A'),
(981,'1840209876933910','',5,'501-504-735',10001,80.00,120.00,1.00,'A'),
(982,'1840209876933913','',5,'501-506-735',9994,80.00,120.00,1.00,'A'),
(983,'1840209876933916','',5,'501-507-735',9998,80.00,120.00,1.00,'A'),
(985,'190917704741380','',544,'741',1,1.00,1.00,1.00,'A'),
(986,'184020987693392','',5,'500-504-736',10001,80.00,120.00,1.00,'A'),
(987,'184020987693395','',5,'500-506-736',10001,80.00,120.00,1.00,'A'),
(988,'184020987693398','',5,'500-507-736',10001,80.00,120.00,1.00,'A'),
(989,'1840209876933911','',5,'501-504-736',10001,80.00,120.00,1.00,'A'),
(990,'1840209876933914','',5,'501-506-736',10001,80.00,120.00,1.00,'A'),
(991,'1840209876933917','',5,'501-507-736',9995,80.00,120.00,1.00,'A'),
(994,'112998921627660','/uploads/20240420/a9906acf543c477ea68a061ff90de9de.jpeg',562,'745',9999,790.00,0.00,0.00,'A'),
(995,'112998921627661','/uploads/20240420/a918e47a8625468db688279f39bd8f09.jpeg',562,'746',9999,690.00,0.00,0.00,'A'),
(996,'112998921627662','/uploads/20240420/62931a7d68a24a628f5e2526ac6affda.jpeg',562,'747',9999,990.00,0.00,0.00,'A'),
(997,'169724635932790','',3,'548-752',0,145.00,155.00,1.00,'A'),
(998,'169724635932791','',3,'549-752',9986,145.00,155.00,1.00,'A'),
(999,'169724635932792','',3,'550-752',9988,145.00,155.00,1.00,'A'),
(1000,'169724635932793','',3,'551-752',9997,145.00,155.00,1.00,'A'),
(1001,'169724635932794','',3,'552-752',10008,145.00,155.00,1.00,'A');

/*Table structure for table `mt_goods_spec` */

DROP TABLE IF EXISTS `mt_goods_spec`;

CREATE TABLE `mt_goods_spec` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `GOODS_ID` int NOT NULL DEFAULT '0' COMMENT '商品ID',
  `NAME` varchar(100) NOT NULL DEFAULT '' COMMENT '规格名称',
  `VALUE` varchar(100) NOT NULL DEFAULT '' COMMENT '规格值',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=755 DEFAULT CHARSET=utf8 COMMENT='规格表';

/*Data for the table `mt_goods_spec` */

insert  into `mt_goods_spec`(`ID`,`GOODS_ID`,`NAME`,`VALUE`,`STATUS`) values 
(1,6,'厚度','正常','D'),
(2,6,'厚度','加厚','D'),
(3,6,'糖分','黑色','D'),
(4,6,'糖分','黄色','D'),
(5,6,'糖分','白色','D'),
(6,3,'数量','304不锈钢','D'),
(7,3,'数量','塑料','D'),
(8,6,'尺码','m','D'),
(9,6,'尺码','l','D'),
(10,6,'口味','1','D'),
(11,6,'口味','2','D'),
(12,6,'口味','3','D'),
(13,2,'m','m','D'),
(14,6,'厚度','超薄','D'),
(15,16,'风味','孜然','D'),
(16,16,'特色','炭火','D'),
(17,16,'风味','秘制','D'),
(18,16,'特色','木炭','D'),
(19,16,'特色','风味小吃','D'),
(20,6,'大小','s','A'),
(21,6,'大小','L','D'),
(22,24,'500ML','7','D'),
(23,24,'700ML','10','D'),
(24,24,'小杯','500ML','A'),
(25,24,'大杯','700ML','D'),
(26,24,'大杯','700ML','D'),
(27,24,'小杯','122','D'),
(28,24,'小杯','任天野','D'),
(29,24,'小杯','700ML','A'),
(30,6,'大小','12','D'),
(31,6,'2','3','D'),
(32,6,'1','','D'),
(33,6,'大小','M','D'),
(34,6,'大小','L','D'),
(35,11,'ddd','rr','A'),
(36,11,'gggg','ttt','A'),
(37,11,'ddd','yyu','A'),
(38,11,'gggg','==999','A'),
(39,12,'1','1,2','A'),
(40,16,'年份','82年','A'),
(41,16,'年份','90年','A'),
(42,16,'年份','00年','A'),
(43,6,'尺码','大','D'),
(44,6,'尺码','小','D'),
(45,2,'7','','D'),
(46,27,'颜色','红色','A'),
(47,27,'颜色','白色','A'),
(48,2,'大小','5','D'),
(49,2,'品牌','6','D'),
(50,2,'大小','IIS','D'),
(51,2,'品牌','8','D'),
(52,6,'xxx','','D'),
(53,6,'糖分','绿色','D'),
(54,37,'test1','12','D'),
(55,37,'test2','13','D'),
(56,37,'test1','123','A'),
(57,38,'颜色','黑色','A'),
(58,38,'颜色','蓝色','A'),
(59,38,'颜色','绿色','D'),
(60,38,'大小','大号','A'),
(61,38,'大小','小号','A'),
(62,38,'颜色','绿色','D'),
(63,4,'颜色','红色','A'),
(64,6,'重量','8kg','A'),
(65,6,'重量','10g','D'),
(66,40,'时长','两小时','A'),
(67,40,'时长','三小时','A'),
(68,40,'时长','四小时','A'),
(69,40,'面积','80平米','A'),
(70,40,'面积','100平米','A'),
(71,41,'11','1','A'),
(72,41,'11','11','A'),
(73,41,'11','111','A'),
(74,3,'颜色','黑','D'),
(75,3,'颜色','黑色','D'),
(76,6,'jj','','D'),
(77,6,'重量','nihao','D'),
(78,6,'糖分','红色','D'),
(79,6,'糖分','紫色','D'),
(80,6,'nih','hth','D'),
(81,2,'颜色','红色','D'),
(82,6,'糖分','蓝色','D'),
(83,6,'大小','XL','D'),
(84,6,'呃呃','','D'),
(86,45,'容量','300W','A'),
(87,45,'容量','300W','A'),
(88,45,'容量','300','A'),
(89,45,'容量','300','A'),
(90,48,'颜色','红色','A'),
(91,48,'1','','D'),
(92,51,'颜色','白色','A'),
(93,51,'颜色','黑色','A'),
(94,51,'颜色','绿色','D'),
(95,48,'颜色','黑色','A'),
(96,52,'eee','12','D'),
(97,52,'eee','222','D'),
(98,52,'dfsfsa','33','A'),
(99,52,'dfsfsa','22','A'),
(100,53,'ddd','','D'),
(101,53,'天泰','营业','A'),
(102,52,'eee','22','D'),
(103,1,'123','123','D'),
(104,1,'123123','','D'),
(105,1,'123','qwe123','D'),
(106,1,'123','qwe123','D'),
(107,1,'尺码','39','D'),
(108,1,'尺码','39','D'),
(109,1,'尺码','40','D'),
(110,6,'糖分','d','D'),
(111,5,'abg','1','D'),
(112,5,'abg','1234','D'),
(113,6,'款式','长','D'),
(114,6,'款式','A','D'),
(115,6,'款式','宽','D'),
(116,65,'套房','套一','A'),
(117,69,'小码','S','A'),
(118,69,'小码','d','A'),
(119,4,'颜色','蓝色','D'),
(120,4,'颜色','蓝色','D'),
(121,2,'大小','L','D'),
(122,2,'品牌','10','D'),
(123,4,'颜色','白色','D'),
(124,4,'颜色','黑色','D'),
(125,4,'颜色','黑色','D'),
(126,6,'test','1','D'),
(127,76,'颜色','黄色','A'),
(128,76,'颜色','黄','D'),
(129,76,'尺码','L','A'),
(130,76,'颜色','1','D'),
(131,76,'颜色','蓝色','A'),
(132,76,'尺码','M','A'),
(133,6,'款式','高','D'),
(134,6,'款式','宽','D'),
(135,6,'款式','宽','D'),
(136,79,'颜色','大小','D'),
(137,79,'颜色','规格','D'),
(138,79,'颜色','红色','A'),
(139,79,'颜色','绿色','A'),
(140,79,'大小','黄色','D'),
(141,79,'大小','500ml','D'),
(142,79,'大小','1L','A'),
(143,6,'款式','宽','D'),
(144,6,'款式','宽','D'),
(145,6,'款式','宽','D'),
(146,6,'款式','11','D'),
(147,81,'caoi','1','A'),
(148,81,'caoi','1','A'),
(149,81,'caoi','2','A'),
(150,81,'iphone','64g','A'),
(151,81,'iphone','128g','A'),
(152,81,'iphone','256g','D'),
(153,86,'颜色','红','A'),
(154,86,'颜色','黄色','D'),
(155,86,'颜色','红色','D'),
(156,86,'颜色','绿色','D'),
(157,86,'颜色','黄','A'),
(158,86,'颜色','绿','A'),
(159,90,'33','33','A'),
(160,90,'3','','A'),
(161,90,'4','5','A'),
(162,91,'华为','1号','D'),
(163,91,'hw','01','D'),
(164,91,'xm','01','A'),
(165,91,'xm','02','A'),
(166,91,'xm','03','A'),
(167,91,'hw','02','A'),
(168,91,'hw','03','A'),
(169,91,'xm','01','D'),
(170,91,'xm','02','D'),
(171,91,'xm','03','D'),
(172,91,'hw','01','D'),
(173,91,'xm','01','D'),
(174,91,'hw','02','D'),
(175,91,'xm','02','D'),
(176,91,'hw','03','D'),
(177,91,'xm','03','D'),
(178,91,'xm','04','D'),
(179,91,'xm','04','D'),
(180,1,'颜色','尺码','D'),
(181,1,'颜色','蓝色','D'),
(182,1,'颜色','黄色','D'),
(183,1,'卡通图','米老鼠','D'),
(184,1,'卡通图','唐老鸭','D'),
(185,91,'原味','','A'),
(186,91,'味道','原味','A'),
(187,91,'味道','酸辣','A'),
(188,93,'打包','小包','A'),
(189,93,'打包','的','A'),
(190,95,'数量','口味','D'),
(191,95,'数量','一包','A'),
(192,95,'数量','两包','A'),
(193,95,'口味','奶香味','A'),
(194,95,'口味','草莓味','A'),
(195,96,'口味','普通','A'),
(196,110,'lll','iiii','A'),
(197,110,'lll','iiii','A'),
(198,110,'9999','llll','A'),
(199,110,'9999','9999','A'),
(200,110,'8888','99999','A'),
(201,110,'8888','7777','A'),
(202,1,'尺码','43','D'),
(203,6,'大小','1','D'),
(204,6,'糖分','1','D'),
(205,6,'糖分','2','D'),
(206,6,'糖分','3','D'),
(207,6,'糖分','4','D'),
(208,6,'糖分','5','D'),
(209,6,'糖分','6','D'),
(210,6,'糖分','7','D'),
(211,6,'糖分','8','D'),
(212,6,'糖分','9','D'),
(213,6,'糖分','10','D'),
(214,5,'颜色','A-1','D'),
(215,5,'长度','B-1','D'),
(216,5,'颜色','A-2','D'),
(217,5,'长度','B-2','D'),
(218,6,'款式','13','D'),
(219,119,'代杀','1','D'),
(220,119,'可选备注','','A'),
(221,119,'代杀','代杀','A'),
(222,119,'代杀','不杀','A'),
(223,119,'可选备注','杀（清洗）','A'),
(224,119,'可选备注','杀（不清洗）','A'),
(225,119,'可选备注','不杀','A'),
(226,122,'大小','小','A'),
(227,122,'大小','中','A'),
(228,122,'大小','大','A'),
(229,122,'甜度','少','A'),
(230,122,'甜度','多','A'),
(231,122,'甜度','超多','A'),
(232,122,'abc','1','A'),
(233,122,'abc','23','A'),
(234,122,'abc','33','D'),
(235,131,'小轿车','','A'),
(236,131,'法拉利','','A'),
(237,131,'兰博基尼','','A'),
(238,122,'234','','A'),
(239,5,'qwe','','D'),
(240,5,'长度','w','D'),
(241,5,'长度','3','D'),
(242,6,'长度','1','D'),
(243,6,'长度','2','D'),
(244,6,'长度','0','D'),
(245,6,'款式','西米','D'),
(246,6,'款式','红豆','D'),
(247,135,'尺寸','大杯','A'),
(248,135,'尺寸','中杯','A'),
(249,135,'尺寸','小杯','A'),
(250,135,'温度','热','A'),
(251,135,'温度','常温','A'),
(252,135,'温度','冰','A'),
(253,135,'颜色','白色','A'),
(254,135,'颜色','白色','D'),
(255,135,'颜色','黑色','A'),
(258,6,'nihoa','','D'),
(260,2,'大小','M','D'),
(261,2,'大小','XL','D'),
(262,140,'222','333','A'),
(263,141,'123','345','D'),
(264,141,'小','1','D'),
(265,141,'小','2','D'),
(266,3,'颜色','白色','D'),
(267,6,'大小','s','D'),
(268,6,'大小','XXL','D'),
(269,6,'长度','3','D'),
(270,6,'长度','ww','D'),
(271,6,'宽度','1','D'),
(272,1,'卡通图','唐老鸭','D'),
(273,165,'单卖','包装','D'),
(274,165,'单卖','1','A'),
(275,165,'单卖','2','A'),
(276,6,'长度','1M','D'),
(277,6,'长度','2M','D'),
(278,6,'宽度','10CM','D'),
(279,6,'宽度','20CM','D'),
(280,6,'宽度','30CM','D'),
(281,6,'长度','3','D'),
(282,169,'颜色','红色','A'),
(283,169,'颜色','蓝色','A'),
(284,169,'颜色','皇上','D'),
(285,169,'颜色','黄色','A'),
(286,169,'尺码','S','A'),
(287,169,'尺码','M','A'),
(288,169,'尺码','L','A'),
(289,169,'尺码','XL','A'),
(290,169,'尺码','XXL','A'),
(291,1,'卡通图','3','D'),
(292,6,'长度','36','D'),
(293,6,'3','','D'),
(294,6,'宽度','3','D'),
(295,6,'656','','D'),
(296,176,'测试1','25','A'),
(297,177,'规格1','个','A'),
(298,177,'规格2','包','D'),
(299,177,'规格1','包','A'),
(300,181,'颜色','红色','A'),
(301,181,'尺码','S','A'),
(302,181,'颜色','蓝色','A'),
(303,181,'尺码','M','A'),
(304,181,'尺码','L','A'),
(305,6,'颜色','白色','A'),
(306,6,'宽度','6','D'),
(307,6,'长度','52','D'),
(308,1,'卡通图','cs','D'),
(309,169,'颜色','213','A'),
(310,183,'颜色','红','A'),
(311,183,'尺码','35','A'),
(312,183,'尺码','36','A'),
(313,183,'尺码','37','A'),
(314,183,'尺码','38','A'),
(315,183,'尺码','39','A'),
(316,183,'尺码','40','A'),
(317,183,'颜色','白','A'),
(318,183,'颜色','黑','A'),
(319,185,'八寸蛋糕','10寸蛋糕','D'),
(320,185,'八寸蛋糕','8寸','D'),
(321,185,'八寸蛋糕','12寸','D'),
(322,185,'八寸蛋糕','15寸','D'),
(323,187,'紫薯包','','D'),
(324,187,'紫薯包','x6','A'),
(325,187,'紫薯包','x12','A'),
(326,187,'紫薯包','x32','A'),
(327,185,'尺寸','8寸','A'),
(328,185,'尺寸','10寸','A'),
(329,185,'尺寸','14寸','A'),
(330,1,'尺码','40','D'),
(331,1,'颜色','白色','D'),
(332,1,'ddd','333','D'),
(333,1,'ddd','222','D'),
(334,1,'ddd','111','D'),
(335,1,'ddd','22','D'),
(336,6,'高度','2M','D'),
(337,6,'高度','3M','D'),
(338,6,'长度','3','D'),
(339,197,'颜色','款式','A'),
(340,199,'分享','20分钟','A'),
(341,199,'预约','30分钟','A'),
(342,6,'高度','黑','D'),
(343,6,'颜色','红','D'),
(344,6,'颜色','黄','D'),
(345,207,'颜色','黑色','A'),
(346,207,'12332','红色','D'),
(347,207,'颜色','黑色','D'),
(348,207,'颜色','红色','A'),
(349,196,'111','11111','A'),
(350,196,'111','1111111','A'),
(351,207,'尺码','S','A'),
(352,207,'尺码','L','A'),
(353,207,'赠礼','1','A'),
(354,207,'赠礼','2','A'),
(355,232,'颜色','白色','A'),
(356,232,'数量','5包','A'),
(357,232,'数量','10包','A'),
(358,232,'颜色','黑色','A'),
(359,233,'22222','22222','A'),
(360,245,'码数','34码','A'),
(361,245,'码数','35码','A'),
(362,245,'码数','36码','A'),
(363,245,'码数','37码','A'),
(364,245,'颜色','红色','A'),
(365,245,'颜色','白色','A'),
(366,245,'颜色','黑色','A'),
(367,2,'xl','xxl','D'),
(368,2,'xl','xl','D'),
(369,2,'l','l','D'),
(370,2,'m','l','D'),
(371,2,'l','22','D'),
(372,305,'颜色','蓝色','A'),
(373,305,'颜色','黑色','A'),
(374,305,'尺码','22','A'),
(375,305,'尺码','23','A'),
(376,2,'l','vv','D'),
(377,358,'热尔特谈','','D'),
(378,358,'111','111','D'),
(379,358,'热尔特谈','111','D'),
(380,358,'热尔特谈','1实物','D'),
(381,358,'热尔特谈','1是qqq','D'),
(382,358,'热尔特谈','111SSS安徽','D'),
(383,358,'热尔特谈','111sss汉字###','D'),
(384,358,'热尔特谈','汉字','D'),
(385,358,'热尔特谈','汉字sss','D'),
(386,358,'i0p','h8iuh','A'),
(387,358,'i0p','oljiho;ok\'','A'),
(388,361,'111','111','A'),
(389,361,'111','222','A'),
(390,361,'111','111','A'),
(391,361,'111','444','A'),
(392,361,'111','77','D'),
(393,361,'111','11111','A'),
(394,2,'m','隔热','D'),
(395,2,'l','1','D'),
(396,6,'chicun','1','D'),
(397,6,'chicun','2','D'),
(398,6,'chicun','3','D'),
(399,6,'颜色','中','D'),
(400,6,'颜色','小','D'),
(401,3,'大小','20','D'),
(402,4,'着色','白','D'),
(403,4,'着色','红','D'),
(404,4,'大小','11','D'),
(405,4,'大小','22','D'),
(406,378,'杯','60ml','D'),
(407,378,'瓶','750ml','D'),
(408,6,'颜色','111','D'),
(409,6,'大小','111111','D'),
(410,378,'5','6','D'),
(411,378,'5','6','D'),
(412,378,'瓶','100','D'),
(413,382,'温度','正常冰','A'),
(414,382,'温度','少冰','A'),
(415,382,'温度','去冰','A'),
(416,382,'甜度','正常糖(推荐)','A'),
(417,382,'甜度','少糖','A'),
(418,382,'甜度','不另外放糖(不推荐)','A'),
(419,382,'特殊口味','标准搭配','A'),
(420,382,'脆波波','','D'),
(421,1,'颜色','蓝色','D'),
(422,1,'重量','1KG','D'),
(423,1,'重量','2KG','D'),
(424,1,'长度','11','D'),
(425,1,'长度','22','D'),
(426,6,'大小','11','D'),
(427,6,'大小','11','D'),
(428,6,'大小','22','D'),
(429,3,'颜色','蓝色','D'),
(430,384,'时长','12','A'),
(431,1,'尺码','45','D'),
(432,1,'尺码','55','D'),
(433,392,'123','','A'),
(434,5,'颜色','A3','D'),
(435,6,'酸度','酸','D'),
(436,6,'酸度','更酸','D'),
(437,6,'辣度','了','D'),
(438,6,'辣度','啦啊','D'),
(439,401,'重量','11kg','A'),
(440,6,'颜色','12kg','D'),
(441,6,'颜色','13kg','D'),
(442,6,'尺寸','aaa','D'),
(443,408,'2332','33','A'),
(444,408,'2332','2323','A'),
(445,408,'2332','232332','A'),
(446,408,'werewe','wewewe','A'),
(447,408,'werewe','wewewe','A'),
(448,1,'尺码','wer','D'),
(449,409,'GPU','RTX 4090， 共 24.0 GB 显存','A'),
(450,409,'CPU','14 核 AMD EPYC 7453','A'),
(451,409,'内存','64GB','A'),
(452,409,'硬盘','451GB','A'),
(453,409,'计费方式','¥ 278每小时','D'),
(454,409,'计费方式','日租 ¥ 63每天','A'),
(455,409,'计费方式','周租 ¥ 420每周','A'),
(456,409,'计费方式','月租 ¥ 1700每月','A'),
(457,409,'计费方式','按量 ¥ 278每小时','A'),
(458,409,'选择镜像','Stable Diffusion 开始做图 SD Web v151 Python v31012 PyTorch v1131 Docker v201010','A'),
(459,409,'选择镜像','PyTorch 2 版本较新的环境 Docker v201010 Python v31012 PyTorch v201 TensorFlow v2130','A'),
(460,411,'11','22','A'),
(461,411,'11','33','A'),
(462,415,'斤','1','A'),
(463,415,'吨','1','A'),
(464,417,'吨','1','A'),
(465,3,'数量','次','D'),
(466,3,'次数','','D'),
(467,6,'大小','2','D'),
(468,6,'容量','3','D'),
(469,6,'容量','4','D'),
(470,421,'test1','12','A'),
(471,434,'2112','12','D'),
(472,434,'2112','43','D'),
(473,434,'嗯嗯','','D'),
(474,1,'yy','yy1','D'),
(475,1,'yy','yy2','D'),
(476,442,'规格','','A'),
(477,442,'规格','大杯','A'),
(478,442,'规格','中杯','A'),
(479,442,'规格','小杯','A'),
(480,446,'颜色','绿色','D'),
(481,446,'颜色','红','A'),
(482,446,'颜色','绿色·','A'),
(483,2,'颜色','1','D'),
(484,2,'颜色','2','D'),
(485,2,'大小','1','D'),
(486,2,'大小','2','D'),
(487,2,'颜色','蓝色','D'),
(488,2,'颜色','红色','D'),
(489,2,'颜色','绿色','D'),
(490,2,'大小','小','D'),
(491,2,'大小','中','D'),
(492,2,'大小','大','D'),
(493,1,'颜色','颜色','D'),
(494,1,'大小','尺码','D'),
(495,1,'颜色','红色','D'),
(496,1,'颜色','蓝色','D'),
(497,1,'尺码','大','D'),
(498,1,'尺码','中','D'),
(499,1,'尺码','小','D'),
(500,5,'颜色','粉色','A'),
(501,5,'颜色','白色','A'),
(502,6,'大小','大','D'),
(503,6,'大小','中','D'),
(504,5,'长度','1m','A'),
(505,6,'大小','小','D'),
(506,5,'长度','0.5m','A'),
(507,5,'长度','0.1m','A'),
(508,6,'容量','1kg','D'),
(509,6,'容量','2kg','D'),
(510,3,'数量','两个装','D'),
(511,3,'数量','五个装','D'),
(512,2,'大小','df ','D'),
(513,2,'味道','原味','D'),
(514,2,'味道','库位','D'),
(515,450,'瓶/箱','','A'),
(516,450,'瓶','箱','D'),
(517,450,'瓶/箱','瓶','A'),
(518,451,'瓶箱','瓶','D'),
(519,451,'瓶箱','箱','D'),
(520,451,'瓶箱','','A'),
(521,451,'瓶箱','瓶','A'),
(522,451,'瓶箱','箱','A'),
(523,452,'瓶','箱','D'),
(524,452,'瓶','瓶','A'),
(525,452,'瓶','箱','A'),
(526,462,'手机号','','A'),
(527,454,'c','23sdf','D'),
(528,454,'毫升','','D'),
(529,465,'测试规格','11','A'),
(530,465,'测试规格2','33','A'),
(531,465,'测试规格','22','A'),
(532,465,'测试规格2','55','A'),
(533,467,'颜色','白色','D'),
(534,467,'长度','1.5米','A'),
(535,467,'颜色','红色','D'),
(536,467,'长度','2米','A'),
(537,454,'大','','A'),
(538,3,'颜色','红','D'),
(539,3,'大小','50','D'),
(540,3,'大小','80','D'),
(541,3,'辣度','','D'),
(542,3,'颜色','红','D'),
(543,3,'颜色','粉','D'),
(544,3,'内存','32','D'),
(545,3,'内存','64','D'),
(546,3,'内存','128','D'),
(547,3,'颜色','白色','D'),
(548,3,'份量','100克','A'),
(549,3,'份量','200克','A'),
(550,3,'份量','300克','A'),
(551,3,'份量','400克','A'),
(552,3,'份量','500克','A'),
(553,3,'辣度','微微辣','D'),
(554,3,'辣度','微辣','D'),
(555,3,'辣度','中辣','D'),
(556,3,'辣度','不辣','D'),
(557,477,'份量','100克','A'),
(558,477,'份量','约200克','D'),
(559,477,'份量','约300克','D'),
(560,477,'辣度','不辣','A'),
(561,477,'辣度','微辣','D'),
(562,477,'辣度','中辣','D'),
(563,477,'辣度','爆辣','D'),
(564,477,'份量','200克','A'),
(565,6,'长度','1','D'),
(566,6,'长度','2','D'),
(567,6,'颜色','黄','D'),
(568,2,'大小','XL','D'),
(569,2,'尺寸','1xl','D'),
(570,2,'包装','袋子','D'),
(571,2,'订单','的','D'),
(572,2,'订单','单独','D'),
(573,2,'订单','天天','D'),
(574,2,'问问','11','D'),
(575,2,'问问','22','D'),
(576,2,'热热热','333','D'),
(577,2,'热热热','444','D'),
(578,2,'问问','3333','D'),
(579,2,'红色','4050','D'),
(580,2,'尺码','35','D'),
(581,2,'颜色','白色','D'),
(582,2,'尺码','36','D'),
(583,2,'尺码','37','D'),
(584,486,'大份','小份','A'),
(585,486,'大份','中份','A'),
(586,486,'糖度','无糖','A'),
(587,486,'糖度','少糖','A'),
(588,487,'份量','大份','A'),
(589,487,'糖度','0糖','D'),
(590,487,'份量','1','D'),
(591,487,'份量','2','D'),
(592,487,'份量','3','D'),
(593,487,'份量','4','D'),
(594,487,'份量','5','D'),
(595,487,'份量','6','D'),
(596,487,'份量','7','D'),
(597,487,'份量','8','D'),
(598,487,'份量','9','D'),
(599,487,'份量','10','D'),
(600,487,'份量','11','D'),
(601,487,'份量','12','D'),
(602,487,'糖度','半糖','D'),
(603,487,'糖度','123243534546457546775647','D'),
(604,487,'糖度','32534523465543654546546546546546546546435643564356345643','D'),
(605,487,'1','','A'),
(606,487,'份量','大份','D'),
(607,487,'份量','中份','D'),
(608,487,'份量','小份','D'),
(609,487,'糖度','全糖','D'),
(610,487,'糖度','无糖','D'),
(611,487,'口味','甜','D'),
(612,487,'口味','辣','D'),
(613,487,'份量','小份','A'),
(614,487,'糖量','无糖','A'),
(615,487,'糖量','半糖','A'),
(616,487,'糖量','全糖','A'),
(617,488,'2','1','A'),
(618,488,'2','1','A'),
(619,489,'内存','123','A'),
(620,489,'内存','456','A'),
(621,489,'容量','12g','A'),
(622,494,'1','','D'),
(623,495,'参数','参数的','A'),
(624,495,'参数','嗯嗯','A'),
(625,495,'东东','','A'),
(626,496,'尺寸','4寸','A'),
(627,496,'尺寸','5','A'),
(628,496,'测试','','A'),
(629,6,'长度','2cm','D'),
(630,498,'参数','1','D'),
(631,498,'东东','2','A'),
(632,498,'东东','33','D'),
(633,498,'参数','44','D'),
(634,498,'参数','1','A'),
(635,498,'东东','4','A'),
(636,499,'每/瓶','1','D'),
(637,499,'瓶','一瓶','A'),
(638,499,'瓶','一箱','D'),
(639,499,'箱','一箱','D'),
(640,499,'包装单位','瓶','A'),
(641,499,'包装单位','箱(24瓶)','A'),
(642,501,'1231','1','A'),
(643,501,'1231','2','A'),
(644,1,'尺码','75b','D'),
(645,1,'尺码','70a','D'),
(646,6,'颜色','黑色','A'),
(647,6,'大小','小杯','D'),
(648,476,'颜色','白色','A'),
(649,476,'颜色','黑色','A'),
(650,476,'大小','S','A'),
(651,476,'大小','M','A'),
(652,476,'大小','L','A'),
(653,506,'测试','50ml','D'),
(654,506,'测试','100ml','D'),
(655,506,'测试二','50ml','A'),
(656,506,'测试二','100','A'),
(657,1,'1','2','D'),
(658,1,'1','3','D'),
(659,1,'1','4','D'),
(660,4,'尺寸','m码','A'),
(661,6,'颜色','绿色','D'),
(662,1,'颜色分类','高脚','A'),
(663,1,'颜色分类','高脚+矮脚','A'),
(664,509,'测试','500ml','A'),
(665,509,'测试','1000ml','A'),
(666,509,'加料','鸡腿','D'),
(667,509,'加料','牛肉','D'),
(668,509,'加料','加糖','A'),
(669,509,'加料','加果肉','A'),
(670,6,'尺寸','bbb','D'),
(671,1,'规格','大','A'),
(672,1,'规格','中','A'),
(673,6,'大小','m','A'),
(674,421,'test1','13','A'),
(675,421,'test2','22','A'),
(676,421,'test2','23','A'),
(677,421,'重量','轻','A'),
(678,421,'重量','中','A'),
(679,3,'是是是','1','D'),
(680,3,'是是是','3','D'),
(681,3,'是是是','5','D'),
(682,3,'对对对','2','D'),
(683,3,'AAA','1','D'),
(684,3,'对对对','4','D'),
(685,3,'对对对','5','D'),
(686,3,'AAA','3','D'),
(687,3,'AAA','7','D'),
(688,6,'颜色','蓝色','D'),
(689,4,'颜色','黑色','A'),
(690,4,'尺寸','xxl码','A'),
(691,6,'颜色','kin','D'),
(692,6,'重量','16kg','A'),
(693,6,'大小','2','D'),
(694,523,'cesgu ','111','A'),
(695,523,'cesgu ','11','A'),
(696,524,'450ml','450','D'),
(697,524,'200ml','200','D'),
(698,527,'券','11','A'),
(699,527,'券','21','A'),
(700,523,'cesgu ','222','A'),
(701,530,'小份','小','A'),
(702,530,'小份','大','A'),
(703,6,'颜色','黄色','D'),
(704,534,'C15','白色','A'),
(705,534,'E22','白色','D'),
(706,534,'F25','灰色','A'),
(707,534,'C15','黑色','D'),
(708,535,'aa','11','D'),
(709,535,'aa','aa','A'),
(710,535,'bb','xx','A'),
(711,536,'11','11','A'),
(712,536,'11','222','A'),
(713,3,'份量','aa','D'),
(714,3,'aa','','D'),
(715,537,'颜色','红','A'),
(716,537,'颜色','黄','A'),
(717,537,'颜色','蓝','A'),
(718,537,'尺寸','S','A'),
(719,537,'尺寸','SM','A'),
(720,534,'E22','黑色','A'),
(721,538,'颜色','红','A'),
(722,538,'颜色','黄','A'),
(723,538,'颜色','白','A'),
(724,534,'E22','白色','A'),
(725,539,'尺码','s','A'),
(726,539,'尺码','l','A'),
(727,539,'颜色','蓝色','A'),
(728,539,'颜色','红色','A'),
(729,540,'022020','1','A'),
(730,540,'022020','2','A'),
(731,540,'21121','121','A'),
(732,540,'21121','1212','A'),
(733,540,'1111','222','A'),
(734,5,'111','111','A'),
(735,5,'111','222','A'),
(736,5,'111','333','A'),
(737,541,'111','111','D'),
(738,542,'111','111','A'),
(739,543,'11','11','A'),
(740,543,'22','22','A'),
(741,544,'11','11','A'),
(742,562,'秋实','秋实','D'),
(743,562,'夏涵','夏涵','D'),
(744,562,'包间','春华','D'),
(745,562,'包间','秋实','A'),
(746,562,'包间','夏涵','A'),
(747,562,'包间','冬蕴','A'),
(748,563,'面值50','50','A'),
(749,563,'100','100','A'),
(750,564,'50','50','A'),
(751,564,'100','100','A'),
(752,3,'嚷嚷着','','D'),
(753,4,'内存','12GB','A'),
(754,4,'内存','3GB','A');

/*Table structure for table `mt_merchant` */

DROP TABLE IF EXISTS `mt_merchant`;

CREATE TABLE `mt_merchant` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `TYPE` varchar(30) DEFAULT '' COMMENT '类型，restaurant：餐饮；retail：零售；service：服务；other：其他',
  `LOGO` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT 'logo',
  `NO` varchar(20) NOT NULL DEFAULT '' COMMENT '商户号',
  `NAME` varchar(50) NOT NULL DEFAULT '' COMMENT '商户名称',
  `CONTACT` varchar(30) DEFAULT '' COMMENT '联系人姓名',
  `PHONE` varchar(20) DEFAULT '' COMMENT '联系电话',
  `ADDRESS` varchar(100) DEFAULT '' COMMENT '联系地址',
  `WX_APP_ID` varchar(50) DEFAULT '' COMMENT '微信小程序appId',
  `WX_APP_SECRET` varchar(50) DEFAULT '' COMMENT '微信小程序秘钥',
  `WX_OFFICIAL_APP_ID` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '微信公众号appId',
  `WX_OFFICIAL_APP_SECRET` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '微信公众号秘钥',
  `DESCRIPTION` varchar(2000) DEFAULT '' COMMENT '备注信息',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A：有效/启用；D：无效',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=39 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='商户表';

/*Data for the table `mt_merchant` */

insert  into `mt_merchant`(`ID`,`TYPE`,`LOGO`,`NO`,`NAME`,`CONTACT`,`PHONE`,`ADDRESS`,`WX_APP_ID`,`WX_APP_SECRET`,`WX_OFFICIAL_APP_ID`,`WX_OFFICIAL_APP_SECRET`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`STATUS`,`OPERATOR`) values 
(1,'service','','10001','小隅安商行','李杰','18976679980','海南省海口市永万路8号','','','','','','2023-08-01 12:03:55','2024-04-30 11:23:23','A','admin'),
(2,'service','/uploads/********/11f9b3135db043488e4b9bdcadda9f56.png','10002','延禾技术','张x','12345678911','Think Pl','','','','','测试','2023-08-01 14:04:14','2023-11-14 16:54:56','A','admin');

/*Table structure for table `mt_message` */

DROP TABLE IF EXISTS `mt_message`;

CREATE TABLE `mt_message` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `USER_ID` int NOT NULL COMMENT '用户ID',
  `TYPE` varchar(30) NOT NULL DEFAULT '' COMMENT '消息类型',
  `TITLE` varchar(200) DEFAULT '友情提示' COMMENT '消息标题',
  `CONTENT` varchar(500) NOT NULL DEFAULT '' COMMENT '消息内容',
  `IS_READ` char(1) DEFAULT 'N' COMMENT '是否已读',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `PARAMS` varchar(1000) DEFAULT '' COMMENT '参数信息',
  `IS_SEND` char(1) DEFAULT 'N' COMMENT '是否已发送',
  `SEND_TIME` datetime DEFAULT NULL COMMENT '发送时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`),
  KEY `index_user_id` (`USER_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='系统消息表';

/*Data for the table `mt_message` */

/*Table structure for table `mt_open_gift` */

DROP TABLE IF EXISTS `mt_open_gift`;

CREATE TABLE `mt_open_gift` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int NOT NULL DEFAULT '0' COMMENT '门店ID',
  `GRADE_ID` int NOT NULL DEFAULT '0' COMMENT '会员等级ID',
  `POINT` int NOT NULL DEFAULT '0' COMMENT '赠送积分',
  `COUPON_ID` int NOT NULL DEFAULT '0' COMMENT '卡券ID',
  `COUPON_NUM` int NOT NULL DEFAULT '1' COMMENT '卡券数量',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='会员开卡赠礼';

/*Data for the table `mt_open_gift` */

/*Table structure for table `mt_open_gift_item` */

DROP TABLE IF EXISTS `mt_open_gift_item`;

CREATE TABLE `mt_open_gift_item` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `USER_ID` int NOT NULL COMMENT '会用ID',
  `OPEN_GIFT_ID` int NOT NULL COMMENT '赠礼ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `STATUS` char(1) NOT NULL COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf32 COMMENT='开卡赠礼明细表';

/*Data for the table `mt_open_gift_item` */

/*Table structure for table `mt_order` */

DROP TABLE IF EXISTS `mt_order`;

CREATE TABLE `mt_order` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `TYPE` varchar(30) DEFAULT NULL COMMENT '订单类型',
  `PAY_TYPE` varchar(30) DEFAULT 'JSAPI' COMMENT '支付类型',
  `ORDER_MODE` varchar(30) DEFAULT 'express' COMMENT '订单模式',
  `PLATFORM` varchar(30) DEFAULT '' COMMENT '平台',
  `ORDER_SN` varchar(32) NOT NULL DEFAULT '' COMMENT '订单号',
  `COUPON_ID` int DEFAULT '0' COMMENT '卡券ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺ID',
  `TABLE_ID` int DEFAULT '0' COMMENT '所属桌码ID',
  `USER_ID` int NOT NULL DEFAULT '0' COMMENT '用户ID',
  `VERIFY_CODE` varchar(10) DEFAULT '' COMMENT '核销验证码',
  `IS_VISITOR` char(1) DEFAULT 'N' COMMENT '是否游客',
  `AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '订单金额',
  `PAY_AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '支付金额',
  `USE_POINT` int DEFAULT '0' COMMENT '使用积分数量',
  `POINT_AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '积分金额',
  `DISCOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '折扣金额',
  `DELIVERY_FEE` decimal(10,2) DEFAULT '0.00' COMMENT '配送费用',
  `PARAM` varchar(500) DEFAULT '' COMMENT '订单参数',
  `EXPRESS_INFO` varchar(500) DEFAULT '' COMMENT '物流信息',
  `REMARK` varchar(500) DEFAULT '' COMMENT '用户备注',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '订单状态',
  `PAY_TIME` datetime DEFAULT NULL COMMENT '支付时间',
  `PAY_STATUS` char(1) DEFAULT '' COMMENT '支付状态',
  `SETTLE_STATUS` char(1) DEFAULT 'A' COMMENT '结算状态',
  `STAFF_ID` int DEFAULT '0' COMMENT '操作员工',
  `COMMISSION_STATUS` char(1) DEFAULT 'A' COMMENT '分佣提成计算状态',
  `COMMISSION_USER_ID` int DEFAULT '0' COMMENT '分佣用户ID',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `RESERVATION_TIME` datetime DEFAULT NULL COMMENT '预约取餐时间',
  `IS_RESERVATION` char(1) DEFAULT 'N' COMMENT '是否预约取餐订单',
  `RESERVATION_STATUS` char(1) DEFAULT 'A' COMMENT '预约状态：A-待处理，B-已处理',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单表';

/*Data for the table `mt_order` */

/*Table structure for table `mt_order_address` */

DROP TABLE IF EXISTS `mt_order_address`;

CREATE TABLE `mt_order_address` (
  `ID` int unsigned NOT NULL AUTO_INCREMENT COMMENT '地址ID',
  `NAME` varchar(30) NOT NULL DEFAULT '' COMMENT '收货人姓名',
  `MOBILE` varchar(20) NOT NULL DEFAULT '' COMMENT '联系电话',
  `PROVINCE_ID` int unsigned NOT NULL DEFAULT '0' COMMENT '省份ID',
  `CITY_ID` int unsigned NOT NULL DEFAULT '0' COMMENT '城市ID',
  `REGION_ID` int unsigned NOT NULL DEFAULT '0' COMMENT '区/县ID',
  `DETAIL` varchar(255) NOT NULL DEFAULT '' COMMENT '详细地址',
  `ORDER_ID` int unsigned NOT NULL DEFAULT '0' COMMENT '订单ID',
  `USER_ID` int unsigned NOT NULL DEFAULT '0' COMMENT '用户ID',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`ID`) USING BTREE,
  KEY `ORDER_ID` (`ORDER_ID`)
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8 COMMENT='订单收货地址记录表';

/*Data for the table `mt_order_address` */

insert  into `mt_order_address`(`ID`,`NAME`,`MOBILE`,`PROVINCE_ID`,`CITY_ID`,`REGION_ID`,`DETAIL`,`ORDER_ID`,`USER_ID`,`CREATE_TIME`) values 
(1,'毕先生','15653290692',1,2,3,'团结花园',1,3,'2024-05-13 16:57:52'),
(2,'毕先生','15653290692',1,2,3,'团结花园',2,3,'2024-05-13 17:00:08');

/*Table structure for table `mt_order_goods` */

DROP TABLE IF EXISTS `mt_order_goods`;

CREATE TABLE `mt_order_goods` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `ORDER_ID` int NOT NULL DEFAULT '0' COMMENT '订单ID',
  `GOODS_ID` int NOT NULL DEFAULT '0' COMMENT '商品ID',
  `SKU_ID` int DEFAULT '0' COMMENT 'skuID',
  `PRICE` decimal(10,2) DEFAULT '0.00' COMMENT '价格',
  `DISCOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '优惠价',
  `NUM` int NOT NULL DEFAULT '0' COMMENT '商品数量',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT 'A：正常；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='订单商品表';

/*Data for the table `mt_order_goods` */

/*Table structure for table `mt_point` */

DROP TABLE IF EXISTS `mt_point`;

CREATE TABLE `mt_point` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺ID',
  `USER_ID` int NOT NULL DEFAULT '0' COMMENT '用户ID',
  `ORDER_SN` varchar(32) DEFAULT '' COMMENT '订单号',
  `AMOUNT` int NOT NULL DEFAULT '0' COMMENT '积分变化数量',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `DESCRIPTION` varchar(200) DEFAULT '' COMMENT '备注说明',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A正常；D作废',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='积分变化表';

/*Data for the table `mt_point` */

/*Table structure for table `mt_printer` */

DROP TABLE IF EXISTS `mt_printer`;

CREATE TABLE `mt_printer` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺ID',
  `SN` varchar(64) DEFAULT NULL COMMENT '打印机编号',
  `NAME` varchar(64) DEFAULT NULL COMMENT '打印机名称',
  `TYPE` varchar(20) DEFAULT 'RECEIPT' COMMENT '打印机类型，RECEIPT小票，LABEL贴纸',
  `AUTO_PRINT` char(1) DEFAULT 'N' COMMENT '是否自动打印',
  `BEFORE_PAY` char(1) DEFAULT 'Y' COMMENT '支付前打印',
  `AFTER_PAY` char(1) DEFAULT 'N' COMMENT '支付后打印',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `DESCRIPTION` varchar(255) DEFAULT '' COMMENT '备注说明',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A正常；D作废',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='打印机表';

CREATE TABLE `mt_printer_cate` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `PRINTER_ID` int NOT NULL COMMENT '打印机ID',
  `CATE_ID` int NOT NULL COMMENT '商品分类ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺ID',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A正常；D作废',
  PRIMARY KEY (`ID`),
  KEY `idx_printer_id` (`PRINTER_ID`),
  KEY `idx_cate_id` (`CATE_ID`),
  KEY `idx_store_cate` (`STORE_ID`, `CATE_ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='打印机分类关联表';

/*Data for the table `mt_printer` */

/*Table structure for table `mt_refund` */

DROP TABLE IF EXISTS `mt_refund`;

CREATE TABLE `mt_refund` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `ORDER_ID` int NOT NULL COMMENT '订单ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `USER_ID` int NOT NULL COMMENT '会员ID',
  `AMOUNT` decimal(10,2) DEFAULT NULL COMMENT '退款金额',
  `TYPE` varchar(20) DEFAULT '' COMMENT '售后类型',
  `REMARK` varchar(500) DEFAULT '' COMMENT '退款备注',
  `EXPRESS_NAME` varchar(30) DEFAULT '' COMMENT '物流公司',
  `EXPRESS_NO` varchar(30) DEFAULT '' COMMENT '物流单号',
  `REJECT_REASON` varchar(1000) DEFAULT NULL COMMENT '拒绝原因',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态',
  `IMAGES` varchar(1000) DEFAULT NULL COMMENT '图片',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='售后表';


/*Data for the table `mt_refund` */

/*Table structure for table `mt_region` */

DROP TABLE IF EXISTS `mt_region`;

CREATE TABLE `mt_region` (
  `ID` int unsigned NOT NULL AUTO_INCREMENT COMMENT '区划信息ID',
  `NAME` varchar(255) NOT NULL DEFAULT '' COMMENT '区划名称',
  `PID` int unsigned NOT NULL DEFAULT '0' COMMENT '父级ID',
  `CODE` varchar(255) NOT NULL DEFAULT '' COMMENT '区划编码',
  `LEVEL` tinyint unsigned NOT NULL DEFAULT '1' COMMENT '层级(1省级 2市级 3区/县级)',
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3705 DEFAULT CHARSET=utf8 COMMENT='省市区数据表';

/*Data for the table `mt_region` */

insert  into `mt_region`(`ID`,`NAME`,`PID`,`CODE`,`LEVEL`) values 
(1,'北京',0,'110000',1),
(2,'北京市',1,'110010',2),
(3,'东城区',2,'110101',3),
(4,'西城区',2,'110102',3),
(5,'朝阳区',2,'110105',3),
(6,'丰台区',2,'110106',3),
(7,'石景山区',2,'110107',3),
(8,'海淀区',2,'110108',3),
(9,'门头沟区',2,'110109',3),
(10,'房山区',2,'110111',3),
(11,'通州区',2,'110112',3),
(12,'顺义区',2,'110113',3),
(13,'昌平区',2,'110114',3),
(14,'大兴区',2,'110115',3),
(15,'怀柔区',2,'110116',3),
(16,'平谷区',2,'110117',3),
(17,'密云区',2,'110118',3),
(18,'延庆区',2,'110119',3),
(19,'天津',0,'120000',1),
(20,'天津市',19,'120010',2),
(21,'和平区',20,'120101',3),
(22,'河东区',20,'120102',3),
(23,'河西区',20,'120103',3),
(24,'南开区',20,'120104',3),
(25,'河北区',20,'120105',3),
(26,'红桥区',20,'120106',3),
(27,'东丽区',20,'120110',3),
(28,'西青区',20,'120111',3),
(29,'津南区',20,'120112',3),
(30,'北辰区',20,'120113',3),
(31,'武清区',20,'120114',3),
(32,'宝坻区',20,'120115',3),
(33,'滨海新区',20,'120116',3),
(34,'宁河区',20,'120117',3),
(35,'静海区',20,'120118',3),
(36,'蓟州区',20,'120119',3),
(37,'河北省',0,'130000',1),
(38,'石家庄市',37,'130100',2),
(39,'长安区',38,'130102',3),
(40,'桥西区',38,'130104',3),
(41,'新华区',38,'130105',3),
(42,'井陉矿区',38,'130107',3),
(43,'裕华区',38,'130108',3),
(44,'藁城区',38,'130109',3),
(45,'鹿泉区',38,'130110',3),
(46,'栾城区',38,'130111',3),
(47,'井陉县',38,'130121',3),
(48,'正定县',38,'130123',3),
(49,'行唐县',38,'130125',3),
(50,'灵寿县',38,'130126',3),
(51,'高邑县',38,'130127',3),
(52,'深泽县',38,'130128',3),
(53,'赞皇县',38,'130129',3),
(54,'无极县',38,'130130',3),
(55,'平山县',38,'130131',3),
(56,'元氏县',38,'130132',3),
(57,'赵县',38,'130133',3),
(58,'辛集市',38,'130181',3),
(59,'晋州市',38,'130183',3),
(60,'新乐市',38,'130184',3),
(61,'唐山市',37,'130200',2),
(62,'路南区',61,'130202',3),
(63,'路北区',61,'130203',3),
(64,'古冶区',61,'130204',3),
(65,'开平区',61,'130205',3),
(66,'丰南区',61,'130207',3),
(67,'丰润区',61,'130208',3),
(68,'曹妃甸区',61,'130209',3),
(69,'滦南县',61,'130224',3),
(70,'乐亭县',61,'130225',3),
(71,'迁西县',61,'130227',3),
(72,'玉田县',61,'130229',3),
(73,'遵化市',61,'130281',3),
(74,'迁安市',61,'130283',3),
(75,'滦州市',61,'130284',3),
(76,'秦皇岛市',37,'130300',2),
(77,'海港区',76,'130302',3),
(78,'山海关区',76,'130303',3),
(79,'北戴河区',76,'130304',3),
(80,'抚宁区',76,'130306',3),
(81,'青龙满族自治县',76,'130321',3),
(82,'昌黎县',76,'130322',3),
(83,'卢龙县',76,'130324',3),
(84,'邯郸市',37,'130400',2),
(85,'邯山区',84,'130402',3),
(86,'丛台区',84,'130403',3),
(87,'复兴区',84,'130404',3),
(88,'峰峰矿区',84,'130406',3),
(89,'肥乡区',84,'130407',3),
(90,'永年区',84,'130408',3),
(91,'临漳县',84,'130423',3),
(92,'成安县',84,'130424',3),
(93,'大名县',84,'130425',3),
(94,'涉县',84,'130426',3),
(95,'磁县',84,'130427',3),
(96,'邱县',84,'130430',3),
(97,'鸡泽县',84,'130431',3),
(98,'广平县',84,'130432',3),
(99,'馆陶县',84,'130433',3),
(100,'魏县',84,'130434',3),
(101,'曲周县',84,'130435',3),
(102,'武安市',84,'130481',3),
(103,'邢台市',37,'130500',2),
(104,'桥东区',103,'130502',3),
(105,'桥西区',103,'130503',3),
(106,'邢台县',103,'130521',3),
(107,'临城县',103,'130522',3),
(108,'内丘县',103,'130523',3),
(109,'柏乡县',103,'130524',3),
(110,'隆尧县',103,'130525',3),
(111,'任县',103,'130526',3),
(112,'南和县',103,'130527',3),
(113,'宁晋县',103,'130528',3),
(114,'巨鹿县',103,'130529',3),
(115,'新河县',103,'130530',3),
(116,'广宗县',103,'130531',3),
(117,'平乡县',103,'130532',3),
(118,'威县',103,'130533',3),
(119,'清河县',103,'130534',3),
(120,'临西县',103,'130535',3),
(121,'南宫市',103,'130581',3),
(122,'沙河市',103,'130582',3),
(123,'保定市',37,'130600',2),
(124,'竞秀区',123,'130602',3),
(125,'莲池区',123,'130606',3),
(126,'满城区',123,'130607',3),
(127,'清苑区',123,'130608',3),
(128,'徐水区',123,'130609',3),
(129,'涞水县',123,'130623',3),
(130,'阜平县',123,'130624',3),
(131,'定兴县',123,'130626',3),
(132,'唐县',123,'130627',3),
(133,'高阳县',123,'130628',3),
(134,'容城县',123,'130629',3),
(135,'涞源县',123,'130630',3),
(136,'望都县',123,'130631',3),
(137,'安新县',123,'130632',3),
(138,'易县',123,'130633',3),
(139,'曲阳县',123,'130634',3),
(140,'蠡县',123,'130635',3),
(141,'顺平县',123,'130636',3),
(142,'博野县',123,'130637',3),
(143,'雄县',123,'130638',3),
(144,'涿州市',123,'130681',3),
(145,'定州市',123,'130682',3),
(146,'安国市',123,'130683',3),
(147,'高碑店市',123,'130684',3),
(148,'张家口市',37,'130700',2),
(149,'桥东区',148,'130702',3),
(150,'桥西区',148,'130703',3),
(151,'宣化区',148,'130705',3),
(152,'下花园区',148,'130706',3),
(153,'万全区',148,'130708',3),
(154,'崇礼区',148,'130709',3),
(155,'张北县',148,'130722',3),
(156,'康保县',148,'130723',3),
(157,'沽源县',148,'130724',3),
(158,'尚义县',148,'130725',3),
(159,'蔚县',148,'130726',3),
(160,'阳原县',148,'130727',3),
(161,'怀安县',148,'130728',3),
(162,'怀来县',148,'130730',3),
(163,'涿鹿县',148,'130731',3),
(164,'赤城县',148,'130732',3),
(165,'承德市',37,'130800',2),
(166,'双桥区',165,'130802',3),
(167,'双滦区',165,'130803',3),
(168,'鹰手营子矿区',165,'130804',3),
(169,'承德县',165,'130821',3),
(170,'兴隆县',165,'130822',3),
(171,'滦平县',165,'130824',3),
(172,'隆化县',165,'130825',3),
(173,'丰宁满族自治县',165,'130826',3),
(174,'宽城满族自治县',165,'130827',3),
(175,'围场满族蒙古族自治县',165,'130828',3),
(176,'平泉市',165,'130881',3),
(177,'沧州市',37,'130900',2),
(178,'新华区',177,'130902',3),
(179,'运河区',177,'130903',3),
(180,'沧县',177,'130921',3),
(181,'青县',177,'130922',3),
(182,'东光县',177,'130923',3),
(183,'海兴县',177,'130924',3),
(184,'盐山县',177,'130925',3),
(185,'肃宁县',177,'130926',3),
(186,'南皮县',177,'130927',3),
(187,'吴桥县',177,'130928',3),
(188,'献县',177,'130929',3),
(189,'孟村回族自治县',177,'130930',3),
(190,'泊头市',177,'130981',3),
(191,'任丘市',177,'130982',3),
(192,'黄骅市',177,'130983',3),
(193,'河间市',177,'130984',3),
(194,'廊坊市',37,'131000',2),
(195,'安次区',194,'131002',3),
(196,'广阳区',194,'131003',3),
(197,'固安县',194,'131022',3),
(198,'永清县',194,'131023',3),
(199,'香河县',194,'131024',3),
(200,'大城县',194,'131025',3),
(201,'文安县',194,'131026',3),
(202,'大厂回族自治县',194,'131028',3),
(203,'霸州市',194,'131081',3),
(204,'三河市',194,'131082',3),
(205,'衡水市',37,'131100',2),
(206,'桃城区',205,'131102',3),
(207,'冀州区',205,'131103',3),
(208,'枣强县',205,'131121',3),
(209,'武邑县',205,'131122',3),
(210,'武强县',205,'131123',3),
(211,'饶阳县',205,'131124',3),
(212,'安平县',205,'131125',3),
(213,'故城县',205,'131126',3),
(214,'景县',205,'131127',3),
(215,'阜城县',205,'131128',3),
(216,'深州市',205,'131182',3),
(217,'山西省',0,'140000',1),
(218,'太原市',217,'140100',2),
(219,'小店区',218,'140105',3),
(220,'迎泽区',218,'140106',3),
(221,'杏花岭区',218,'140107',3),
(222,'尖草坪区',218,'140108',3),
(223,'万柏林区',218,'140109',3),
(224,'晋源区',218,'140110',3),
(225,'清徐县',218,'140121',3),
(226,'阳曲县',218,'140122',3),
(227,'娄烦县',218,'140123',3),
(228,'古交市',218,'140181',3),
(229,'大同市',217,'140200',2),
(230,'新荣区',229,'140212',3),
(231,'平城区',229,'140213',3),
(232,'云冈区',229,'140214',3),
(233,'云州区',229,'140215',3),
(234,'阳高县',229,'140221',3),
(235,'天镇县',229,'140222',3),
(236,'广灵县',229,'140223',3),
(237,'灵丘县',229,'140224',3),
(238,'浑源县',229,'140225',3),
(239,'左云县',229,'140226',3),
(240,'阳泉市',217,'140300',2),
(241,'城区',240,'140302',3),
(242,'矿区',240,'140303',3),
(243,'郊区',240,'140311',3),
(244,'平定县',240,'140321',3),
(245,'盂县',240,'140322',3),
(246,'长治市',217,'140400',2),
(247,'潞州区',246,'140403',3),
(248,'上党区',246,'140404',3),
(249,'屯留区',246,'140405',3),
(250,'潞城区',246,'140406',3),
(251,'襄垣县',246,'140423',3),
(252,'平顺县',246,'140425',3),
(253,'黎城县',246,'140426',3),
(254,'壶关县',246,'140427',3),
(255,'长子县',246,'140428',3),
(256,'武乡县',246,'140429',3),
(257,'沁县',246,'140430',3),
(258,'沁源县',246,'140431',3),
(259,'晋城市',217,'140500',2),
(260,'城区',259,'140502',3),
(261,'沁水县',259,'140521',3),
(262,'阳城县',259,'140522',3),
(263,'陵川县',259,'140524',3),
(264,'泽州县',259,'140525',3),
(265,'高平市',259,'140581',3),
(266,'朔州市',217,'140600',2),
(267,'朔城区',266,'140602',3),
(268,'平鲁区',266,'140603',3),
(269,'山阴县',266,'140621',3),
(270,'应县',266,'140622',3),
(271,'右玉县',266,'140623',3),
(272,'怀仁市',266,'140681',3),
(273,'晋中市',217,'140700',2),
(274,'榆次区',273,'140702',3),
(275,'太谷区',273,'140703',3),
(276,'榆社县',273,'140721',3),
(277,'左权县',273,'140722',3),
(278,'和顺县',273,'140723',3),
(279,'昔阳县',273,'140724',3),
(280,'寿阳县',273,'140725',3),
(281,'祁县',273,'140727',3),
(282,'平遥县',273,'140728',3),
(283,'灵石县',273,'140729',3),
(284,'介休市',273,'140781',3),
(285,'运城市',217,'140800',2),
(286,'盐湖区',285,'140802',3),
(287,'临猗县',285,'140821',3),
(288,'万荣县',285,'140822',3),
(289,'闻喜县',285,'140823',3),
(290,'稷山县',285,'140824',3),
(291,'新绛县',285,'140825',3),
(292,'绛县',285,'140826',3),
(293,'垣曲县',285,'140827',3),
(294,'夏县',285,'140828',3),
(295,'平陆县',285,'140829',3),
(296,'芮城县',285,'140830',3),
(297,'永济市',285,'140881',3),
(298,'河津市',285,'140882',3),
(299,'忻州市',217,'140900',2),
(300,'忻府区',299,'140902',3),
(301,'定襄县',299,'140921',3),
(302,'五台县',299,'140922',3),
(303,'代县',299,'140923',3),
(304,'繁峙县',299,'140924',3),
(305,'宁武县',299,'140925',3),
(306,'静乐县',299,'140926',3),
(307,'神池县',299,'140927',3),
(308,'五寨县',299,'140928',3),
(309,'岢岚县',299,'140929',3),
(310,'河曲县',299,'140930',3),
(311,'保德县',299,'140931',3),
(312,'偏关县',299,'140932',3),
(313,'原平市',299,'140981',3),
(314,'临汾市',217,'141000',2),
(315,'尧都区',314,'141002',3),
(316,'曲沃县',314,'141021',3),
(317,'翼城县',314,'141022',3),
(318,'襄汾县',314,'141023',3),
(319,'洪洞县',314,'141024',3),
(320,'古县',314,'141025',3),
(321,'安泽县',314,'141026',3),
(322,'浮山县',314,'141027',3),
(323,'吉县',314,'141028',3),
(324,'乡宁县',314,'141029',3),
(325,'大宁县',314,'141030',3),
(326,'隰县',314,'141031',3),
(327,'永和县',314,'141032',3),
(328,'蒲县',314,'141033',3),
(329,'汾西县',314,'141034',3),
(330,'侯马市',314,'141081',3),
(331,'霍州市',314,'141082',3),
(332,'吕梁市',217,'141100',2),
(333,'离石区',332,'141102',3),
(334,'文水县',332,'141121',3),
(335,'交城县',332,'141122',3),
(336,'兴县',332,'141123',3),
(337,'临县',332,'141124',3),
(338,'柳林县',332,'141125',3),
(339,'石楼县',332,'141126',3),
(340,'岚县',332,'141127',3),
(341,'方山县',332,'141128',3),
(342,'中阳县',332,'141129',3),
(343,'交口县',332,'141130',3),
(344,'孝义市',332,'141181',3),
(345,'汾阳市',332,'141182',3),
(346,'内蒙古自治区',0,'150000',1),
(347,'呼和浩特市',346,'150100',2),
(348,'新城区',347,'150102',3),
(349,'回民区',347,'150103',3),
(350,'玉泉区',347,'150104',3),
(351,'赛罕区',347,'150105',3),
(352,'土默特左旗',347,'150121',3),
(353,'托克托县',347,'150122',3),
(354,'和林格尔县',347,'150123',3),
(355,'清水河县',347,'150124',3),
(356,'武川县',347,'150125',3),
(357,'包头市',346,'150200',2),
(358,'东河区',357,'150202',3),
(359,'昆都仑区',357,'150203',3),
(360,'青山区',357,'150204',3),
(361,'石拐区',357,'150205',3),
(362,'白云鄂博矿区',357,'150206',3),
(363,'九原区',357,'150207',3),
(364,'土默特右旗',357,'150221',3),
(365,'固阳县',357,'150222',3),
(366,'达尔罕茂明安联合旗',357,'150223',3),
(367,'乌海市',346,'150300',2),
(368,'海勃湾区',367,'150302',3),
(369,'海南区',367,'150303',3),
(370,'乌达区',367,'150304',3),
(371,'赤峰市',346,'150400',2),
(372,'红山区',371,'150402',3),
(373,'元宝山区',371,'150403',3),
(374,'松山区',371,'150404',3),
(375,'阿鲁科尔沁旗',371,'150421',3),
(376,'巴林左旗',371,'150422',3),
(377,'巴林右旗',371,'150423',3),
(378,'林西县',371,'150424',3),
(379,'克什克腾旗',371,'150425',3),
(380,'翁牛特旗',371,'150426',3),
(381,'喀喇沁旗',371,'150428',3),
(382,'宁城县',371,'150429',3),
(383,'敖汉旗',371,'150430',3),
(384,'通辽市',346,'150500',2),
(385,'科尔沁区',384,'150502',3),
(386,'科尔沁左翼中旗',384,'150521',3),
(387,'科尔沁左翼后旗',384,'150522',3),
(388,'开鲁县',384,'150523',3),
(389,'库伦旗',384,'150524',3),
(390,'奈曼旗',384,'150525',3),
(391,'扎鲁特旗',384,'150526',3),
(392,'霍林郭勒市',384,'150581',3),
(393,'鄂尔多斯市',346,'150600',2),
(394,'东胜区',393,'150602',3),
(395,'康巴什区',393,'150603',3),
(396,'达拉特旗',393,'150621',3),
(397,'准格尔旗',393,'150622',3),
(398,'鄂托克前旗',393,'150623',3),
(399,'鄂托克旗',393,'150624',3),
(400,'杭锦旗',393,'150625',3),
(401,'乌审旗',393,'150626',3),
(402,'伊金霍洛旗',393,'150627',3),
(403,'呼伦贝尔市',346,'150700',2),
(404,'海拉尔区',403,'150702',3),
(405,'扎赉诺尔区',403,'150703',3),
(406,'阿荣旗',403,'150721',3),
(407,'莫力达瓦达斡尔族自治旗',403,'150722',3),
(408,'鄂伦春自治旗',403,'150723',3),
(409,'鄂温克族自治旗',403,'150724',3),
(410,'陈巴尔虎旗',403,'150725',3),
(411,'新巴尔虎左旗',403,'150726',3),
(412,'新巴尔虎右旗',403,'150727',3),
(413,'满洲里市',403,'150781',3),
(414,'牙克石市',403,'150782',3),
(415,'扎兰屯市',403,'150783',3),
(416,'额尔古纳市',403,'150784',3),
(417,'根河市',403,'150785',3),
(418,'巴彦淖尔市',346,'150800',2),
(419,'临河区',418,'150802',3),
(420,'五原县',418,'150821',3),
(421,'磴口县',418,'150822',3),
(422,'乌拉特前旗',418,'150823',3),
(423,'乌拉特中旗',418,'150824',3),
(424,'乌拉特后旗',418,'150825',3),
(425,'杭锦后旗',418,'150826',3),
(426,'乌兰察布市',346,'150900',2),
(427,'集宁区',426,'150902',3),
(428,'卓资县',426,'150921',3),
(429,'化德县',426,'150922',3),
(430,'商都县',426,'150923',3),
(431,'兴和县',426,'150924',3),
(432,'凉城县',426,'150925',3),
(433,'察哈尔右翼前旗',426,'150926',3),
(434,'察哈尔右翼中旗',426,'150927',3),
(435,'察哈尔右翼后旗',426,'150928',3),
(436,'四子王旗',426,'150929',3),
(437,'丰镇市',426,'150981',3),
(438,'兴安盟',346,'152200',2),
(439,'乌兰浩特市',438,'152201',3),
(440,'阿尔山市',438,'152202',3),
(441,'科尔沁右翼前旗',438,'152221',3),
(442,'科尔沁右翼中旗',438,'152222',3),
(443,'扎赉特旗',438,'152223',3),
(444,'突泉县',438,'152224',3),
(445,'锡林郭勒盟',346,'152500',2),
(446,'二连浩特市',445,'152501',3),
(447,'锡林浩特市',445,'152502',3),
(448,'阿巴嘎旗',445,'152522',3),
(449,'苏尼特左旗',445,'152523',3),
(450,'苏尼特右旗',445,'152524',3),
(451,'东乌珠穆沁旗',445,'152525',3),
(452,'西乌珠穆沁旗',445,'152526',3),
(453,'太仆寺旗',445,'152527',3),
(454,'镶黄旗',445,'152528',3),
(455,'正镶白旗',445,'152529',3),
(456,'正蓝旗',445,'152530',3),
(457,'多伦县',445,'152531',3),
(458,'阿拉善盟',346,'152900',2),
(459,'阿拉善左旗',458,'152921',3),
(460,'阿拉善右旗',458,'152922',3),
(461,'额济纳旗',458,'152923',3),
(462,'辽宁省',0,'210000',1),
(463,'沈阳市',462,'210100',2),
(464,'和平区',463,'210102',3),
(465,'沈河区',463,'210103',3),
(466,'大东区',463,'210104',3),
(467,'皇姑区',463,'210105',3),
(468,'铁西区',463,'210106',3),
(469,'苏家屯区',463,'210111',3),
(470,'浑南区',463,'210112',3),
(471,'沈北新区',463,'210113',3),
(472,'于洪区',463,'210114',3),
(473,'辽中区',463,'210115',3),
(474,'康平县',463,'210123',3),
(475,'法库县',463,'210124',3),
(476,'新民市',463,'210181',3),
(477,'大连市',462,'210200',2),
(478,'中山区',477,'210202',3),
(479,'西岗区',477,'210203',3),
(480,'沙河口区',477,'210204',3),
(481,'甘井子区',477,'210211',3),
(482,'旅顺口区',477,'210212',3),
(483,'金州区',477,'210213',3),
(484,'普兰店区',477,'210214',3),
(485,'长海县',477,'210224',3),
(486,'瓦房店市',477,'210281',3),
(487,'庄河市',477,'210283',3),
(488,'鞍山市',462,'210300',2),
(489,'铁东区',488,'210302',3),
(490,'铁西区',488,'210303',3),
(491,'立山区',488,'210304',3),
(492,'千山区',488,'210311',3),
(493,'台安县',488,'210321',3),
(494,'岫岩满族自治县',488,'210323',3),
(495,'海城市',488,'210381',3),
(496,'抚顺市',462,'210400',2),
(497,'新抚区',496,'210402',3),
(498,'东洲区',496,'210403',3),
(499,'望花区',496,'210404',3),
(500,'顺城区',496,'210411',3),
(501,'抚顺县',496,'210421',3),
(502,'新宾满族自治县',496,'210422',3),
(503,'清原满族自治县',496,'210423',3),
(504,'本溪市',462,'210500',2),
(505,'平山区',504,'210502',3),
(506,'溪湖区',504,'210503',3),
(507,'明山区',504,'210504',3),
(508,'南芬区',504,'210505',3),
(509,'本溪满族自治县',504,'210521',3),
(510,'桓仁满族自治县',504,'210522',3),
(511,'丹东市',462,'210600',2),
(512,'元宝区',511,'210602',3),
(513,'振兴区',511,'210603',3),
(514,'振安区',511,'210604',3),
(515,'宽甸满族自治县',511,'210624',3),
(516,'东港市',511,'210681',3),
(517,'凤城市',511,'210682',3),
(518,'锦州市',462,'210700',2),
(519,'古塔区',518,'210702',3),
(520,'凌河区',518,'210703',3),
(521,'太和区',518,'210711',3),
(522,'黑山县',518,'210726',3),
(523,'义县',518,'210727',3),
(524,'凌海市',518,'210781',3),
(525,'北镇市',518,'210782',3),
(526,'营口市',462,'210800',2),
(527,'站前区',526,'210802',3),
(528,'西市区',526,'210803',3),
(529,'鲅鱼圈区',526,'210804',3),
(530,'老边区',526,'210811',3),
(531,'盖州市',526,'210881',3),
(532,'大石桥市',526,'210882',3),
(533,'阜新市',462,'210900',2),
(534,'海州区',533,'210902',3),
(535,'新邱区',533,'210903',3),
(536,'太平区',533,'210904',3),
(537,'清河门区',533,'210905',3),
(538,'细河区',533,'210911',3),
(539,'阜新蒙古族自治县',533,'210921',3),
(540,'彰武县',533,'210922',3),
(541,'辽阳市',462,'211000',2),
(542,'白塔区',541,'211002',3),
(543,'文圣区',541,'211003',3),
(544,'宏伟区',541,'211004',3),
(545,'弓长岭区',541,'211005',3),
(546,'太子河区',541,'211011',3),
(547,'辽阳县',541,'211021',3),
(548,'灯塔市',541,'211081',3),
(549,'盘锦市',462,'211100',2),
(550,'双台子区',549,'211102',3),
(551,'兴隆台区',549,'211103',3),
(552,'大洼区',549,'211104',3),
(553,'盘山县',549,'211122',3),
(554,'铁岭市',462,'211200',2),
(555,'银州区',554,'211202',3),
(556,'清河区',554,'211204',3),
(557,'铁岭县',554,'211221',3),
(558,'西丰县',554,'211223',3),
(559,'昌图县',554,'211224',3),
(560,'调兵山市',554,'211281',3),
(561,'开原市',554,'211282',3),
(562,'朝阳市',462,'211300',2),
(563,'双塔区',562,'211302',3),
(564,'龙城区',562,'211303',3),
(565,'朝阳县',562,'211321',3),
(566,'建平县',562,'211322',3),
(567,'喀喇沁左翼蒙古族自治县',562,'211324',3),
(568,'北票市',562,'211381',3),
(569,'凌源市',562,'211382',3),
(570,'葫芦岛市',462,'211400',2),
(571,'连山区',570,'211402',3),
(572,'龙港区',570,'211403',3),
(573,'南票区',570,'211404',3),
(574,'绥中县',570,'211421',3),
(575,'建昌县',570,'211422',3),
(576,'兴城市',570,'211481',3),
(577,'吉林省',0,'220000',1),
(578,'长春市',577,'220100',2),
(579,'南关区',578,'220102',3),
(580,'宽城区',578,'220103',3),
(581,'朝阳区',578,'220104',3),
(582,'二道区',578,'220105',3),
(583,'绿园区',578,'220106',3),
(584,'双阳区',578,'220112',3),
(585,'九台区',578,'220113',3),
(586,'农安县',578,'220122',3),
(587,'榆树市',578,'220182',3),
(588,'德惠市',578,'220183',3),
(589,'吉林市',577,'220200',2),
(590,'昌邑区',589,'220202',3),
(591,'龙潭区',589,'220203',3),
(592,'船营区',589,'220204',3),
(593,'丰满区',589,'220211',3),
(594,'永吉县',589,'220221',3),
(595,'蛟河市',589,'220281',3),
(596,'桦甸市',589,'220282',3),
(597,'舒兰市',589,'220283',3),
(598,'磐石市',589,'220284',3),
(599,'四平市',577,'220300',2),
(600,'铁西区',599,'220302',3),
(601,'铁东区',599,'220303',3),
(602,'梨树县',599,'220322',3),
(603,'伊通满族自治县',599,'220323',3),
(604,'公主岭市',599,'220381',3),
(605,'双辽市',599,'220382',3),
(606,'辽源市',577,'220400',2),
(607,'龙山区',606,'220402',3),
(608,'西安区',606,'220403',3),
(609,'东丰县',606,'220421',3),
(610,'东辽县',606,'220422',3),
(611,'通化市',577,'220500',2),
(612,'东昌区',611,'220502',3),
(613,'二道江区',611,'220503',3),
(614,'通化县',611,'220521',3),
(615,'辉南县',611,'220523',3),
(616,'柳河县',611,'220524',3),
(617,'梅河口市',611,'220581',3),
(618,'集安市',611,'220582',3),
(619,'白山市',577,'220600',2),
(620,'浑江区',619,'220602',3),
(621,'江源区',619,'220605',3),
(622,'抚松县',619,'220621',3),
(623,'靖宇县',619,'220622',3),
(624,'长白朝鲜族自治县',619,'220623',3),
(625,'临江市',619,'220681',3),
(626,'松原市',577,'220700',2),
(627,'宁江区',626,'220702',3),
(628,'前郭尔罗斯蒙古族自治县',626,'220721',3),
(629,'长岭县',626,'220722',3),
(630,'乾安县',626,'220723',3),
(631,'扶余市',626,'220781',3),
(632,'白城市',577,'220800',2),
(633,'洮北区',632,'220802',3),
(634,'镇赉县',632,'220821',3),
(635,'通榆县',632,'220822',3),
(636,'洮南市',632,'220881',3),
(637,'大安市',632,'220882',3),
(638,'延边朝鲜族自治州',577,'222400',2),
(639,'延吉市',638,'222401',3),
(640,'图们市',638,'222402',3),
(641,'敦化市',638,'222403',3),
(642,'珲春市',638,'222404',3),
(643,'龙井市',638,'222405',3),
(644,'和龙市',638,'222406',3),
(645,'汪清县',638,'222424',3),
(646,'安图县',638,'222426',3),
(647,'黑龙江省',0,'230000',1),
(648,'哈尔滨市',647,'230100',2),
(649,'道里区',648,'230102',3),
(650,'南岗区',648,'230103',3),
(651,'道外区',648,'230104',3),
(652,'平房区',648,'230108',3),
(653,'松北区',648,'230109',3),
(654,'香坊区',648,'230110',3),
(655,'呼兰区',648,'230111',3),
(656,'阿城区',648,'230112',3),
(657,'双城区',648,'230113',3),
(658,'依兰县',648,'230123',3),
(659,'方正县',648,'230124',3),
(660,'宾县',648,'230125',3),
(661,'巴彦县',648,'230126',3),
(662,'木兰县',648,'230127',3),
(663,'通河县',648,'230128',3),
(664,'延寿县',648,'230129',3),
(665,'尚志市',648,'230183',3),
(666,'五常市',648,'230184',3),
(667,'齐齐哈尔市',647,'230200',2),
(668,'龙沙区',667,'230202',3),
(669,'建华区',667,'230203',3),
(670,'铁锋区',667,'230204',3),
(671,'昂昂溪区',667,'230205',3),
(672,'富拉尔基区',667,'230206',3),
(673,'碾子山区',667,'230207',3),
(674,'梅里斯达斡尔族区',667,'230208',3),
(675,'龙江县',667,'230221',3),
(676,'依安县',667,'230223',3),
(677,'泰来县',667,'230224',3),
(678,'甘南县',667,'230225',3),
(679,'富裕县',667,'230227',3),
(680,'克山县',667,'230229',3),
(681,'克东县',667,'230230',3),
(682,'拜泉县',667,'230231',3),
(683,'讷河市',667,'230281',3),
(684,'鸡西市',647,'230300',2),
(685,'鸡冠区',684,'230302',3),
(686,'恒山区',684,'230303',3),
(687,'滴道区',684,'230304',3),
(688,'梨树区',684,'230305',3),
(689,'城子河区',684,'230306',3),
(690,'麻山区',684,'230307',3),
(691,'鸡东县',684,'230321',3),
(692,'虎林市',684,'230381',3),
(693,'密山市',684,'230382',3),
(694,'鹤岗市',647,'230400',2),
(695,'向阳区',694,'230402',3),
(696,'工农区',694,'230403',3),
(697,'南山区',694,'230404',3),
(698,'兴安区',694,'230405',3),
(699,'东山区',694,'230406',3),
(700,'兴山区',694,'230407',3),
(701,'萝北县',694,'230421',3),
(702,'绥滨县',694,'230422',3),
(703,'双鸭山市',647,'230500',2),
(704,'尖山区',703,'230502',3),
(705,'岭东区',703,'230503',3),
(706,'四方台区',703,'230505',3),
(707,'宝山区',703,'230506',3),
(708,'集贤县',703,'230521',3),
(709,'友谊县',703,'230522',3),
(710,'宝清县',703,'230523',3),
(711,'饶河县',703,'230524',3),
(712,'大庆市',647,'230600',2),
(713,'萨尔图区',712,'230602',3),
(714,'龙凤区',712,'230603',3),
(715,'让胡路区',712,'230604',3),
(716,'红岗区',712,'230605',3),
(717,'大同区',712,'230606',3),
(718,'肇州县',712,'230621',3),
(719,'肇源县',712,'230622',3),
(720,'林甸县',712,'230623',3),
(721,'杜尔伯特蒙古族自治县',712,'230624',3),
(722,'伊春市',647,'230700',2),
(723,'伊美区',722,'230717',3),
(724,'乌翠区',722,'230718',3),
(725,'友好区',722,'230719',3),
(726,'嘉荫县',722,'230722',3),
(727,'汤旺县',722,'230723',3),
(728,'丰林县',722,'230724',3),
(729,'大箐山县',722,'230725',3),
(730,'南岔县',722,'230726',3),
(731,'金林区',722,'230751',3),
(732,'铁力市',722,'230781',3),
(733,'佳木斯市',647,'230800',2),
(734,'向阳区',733,'230803',3),
(735,'前进区',733,'230804',3),
(736,'东风区',733,'230805',3),
(737,'郊区',733,'230811',3),
(738,'桦南县',733,'230822',3),
(739,'桦川县',733,'230826',3),
(740,'汤原县',733,'230828',3),
(741,'同江市',733,'230881',3),
(742,'富锦市',733,'230882',3),
(743,'抚远市',733,'230883',3),
(744,'七台河市',647,'230900',2),
(745,'新兴区',744,'230902',3),
(746,'桃山区',744,'230903',3),
(747,'茄子河区',744,'230904',3),
(748,'勃利县',744,'230921',3),
(749,'牡丹江市',647,'231000',2),
(750,'东安区',749,'231002',3),
(751,'阳明区',749,'231003',3),
(752,'爱民区',749,'231004',3),
(753,'西安区',749,'231005',3),
(754,'林口县',749,'231025',3),
(755,'绥芬河市',749,'231081',3),
(756,'海林市',749,'231083',3),
(757,'宁安市',749,'231084',3),
(758,'穆棱市',749,'231085',3),
(759,'东宁市',749,'231086',3),
(760,'黑河市',647,'231100',2),
(761,'爱辉区',760,'231102',3),
(762,'逊克县',760,'231123',3),
(763,'孙吴县',760,'231124',3),
(764,'北安市',760,'231181',3),
(765,'五大连池市',760,'231182',3),
(766,'嫩江市',760,'231183',3),
(767,'绥化市',647,'231200',2),
(768,'北林区',767,'231202',3),
(769,'望奎县',767,'231221',3),
(770,'兰西县',767,'231222',3),
(771,'青冈县',767,'231223',3),
(772,'庆安县',767,'231224',3),
(773,'明水县',767,'231225',3),
(774,'绥棱县',767,'231226',3),
(775,'安达市',767,'231281',3),
(776,'肇东市',767,'231282',3),
(777,'海伦市',767,'231283',3),
(778,'大兴安岭地区',647,'232700',2),
(779,'漠河市',778,'232701',3),
(780,'呼玛县',778,'232721',3),
(781,'塔河县',778,'232722',3),
(782,'上海',0,'310000',1),
(783,'上海市',782,'310010',2),
(784,'黄浦区',783,'310101',3),
(785,'徐汇区',783,'310104',3),
(786,'长宁区',783,'310105',3),
(787,'静安区',783,'310106',3),
(788,'普陀区',783,'310107',3),
(789,'虹口区',783,'310109',3),
(790,'杨浦区',783,'310110',3),
(791,'闵行区',783,'310112',3),
(792,'宝山区',783,'310113',3),
(793,'嘉定区',783,'310114',3),
(794,'浦东新区',783,'310115',3),
(795,'金山区',783,'310116',3),
(796,'松江区',783,'310117',3),
(797,'青浦区',783,'310118',3),
(798,'奉贤区',783,'310120',3),
(799,'崇明区',783,'310151',3),
(800,'江苏省',0,'320000',1),
(801,'南京市',800,'320100',2),
(802,'玄武区',801,'320102',3),
(803,'秦淮区',801,'320104',3),
(804,'建邺区',801,'320105',3),
(805,'鼓楼区',801,'320106',3),
(806,'浦口区',801,'320111',3),
(807,'栖霞区',801,'320113',3),
(808,'雨花台区',801,'320114',3),
(809,'江宁区',801,'320115',3),
(810,'六合区',801,'320116',3),
(811,'溧水区',801,'320117',3),
(812,'高淳区',801,'320118',3),
(813,'无锡市',800,'320200',2),
(814,'锡山区',813,'320205',3),
(815,'惠山区',813,'320206',3),
(816,'滨湖区',813,'320211',3),
(817,'梁溪区',813,'320213',3),
(818,'新吴区',813,'320214',3),
(819,'江阴市',813,'320281',3),
(820,'宜兴市',813,'320282',3),
(821,'徐州市',800,'320300',2),
(822,'鼓楼区',821,'320302',3),
(823,'云龙区',821,'320303',3),
(824,'贾汪区',821,'320305',3),
(825,'泉山区',821,'320311',3),
(826,'铜山区',821,'320312',3),
(827,'丰县',821,'320321',3),
(828,'沛县',821,'320322',3),
(829,'睢宁县',821,'320324',3),
(830,'新沂市',821,'320381',3),
(831,'邳州市',821,'320382',3),
(832,'常州市',800,'320400',2),
(833,'天宁区',832,'320402',3),
(834,'钟楼区',832,'320404',3),
(835,'新北区',832,'320411',3),
(836,'武进区',832,'320412',3),
(837,'金坛区',832,'320413',3),
(838,'溧阳市',832,'320481',3),
(839,'苏州市',800,'320500',2),
(840,'虎丘区',839,'320505',3),
(841,'吴中区',839,'320506',3),
(842,'相城区',839,'320507',3),
(843,'姑苏区',839,'320508',3),
(844,'吴江区',839,'320509',3),
(845,'常熟市',839,'320581',3),
(846,'张家港市',839,'320582',3),
(847,'昆山市',839,'320583',3),
(848,'太仓市',839,'320585',3),
(849,'南通市',800,'320600',2),
(850,'崇川区',849,'320602',3),
(851,'港闸区',849,'320611',3),
(852,'通州区',849,'320612',3),
(853,'如东县',849,'320623',3),
(854,'启东市',849,'320681',3),
(855,'如皋市',849,'320682',3),
(856,'海门市',849,'320684',3),
(857,'海安市',849,'320685',3),
(858,'连云港市',800,'320700',2),
(859,'连云区',858,'320703',3),
(860,'海州区',858,'320706',3),
(861,'赣榆区',858,'320707',3),
(862,'东海县',858,'320722',3),
(863,'灌云县',858,'320723',3),
(864,'灌南县',858,'320724',3),
(865,'淮安市',800,'320800',2),
(866,'淮安区',865,'320803',3),
(867,'淮阴区',865,'320804',3),
(868,'清江浦区',865,'320812',3),
(869,'洪泽区',865,'320813',3),
(870,'涟水县',865,'320826',3),
(871,'盱眙县',865,'320830',3),
(872,'金湖县',865,'320831',3),
(873,'盐城市',800,'320900',2),
(874,'亭湖区',873,'320902',3),
(875,'盐都区',873,'320903',3),
(876,'大丰区',873,'320904',3),
(877,'响水县',873,'320921',3),
(878,'滨海县',873,'320922',3),
(879,'阜宁县',873,'320923',3),
(880,'射阳县',873,'320924',3),
(881,'建湖县',873,'320925',3),
(882,'东台市',873,'320981',3),
(883,'扬州市',800,'321000',2),
(884,'广陵区',883,'321002',3),
(885,'邗江区',883,'321003',3),
(886,'江都区',883,'321012',3),
(887,'宝应县',883,'321023',3),
(888,'仪征市',883,'321081',3),
(889,'高邮市',883,'321084',3),
(890,'镇江市',800,'321100',2),
(891,'京口区',890,'321102',3),
(892,'润州区',890,'321111',3),
(893,'丹徒区',890,'321112',3),
(894,'丹阳市',890,'321181',3),
(895,'扬中市',890,'321182',3),
(896,'句容市',890,'321183',3),
(897,'泰州市',800,'321200',2),
(898,'海陵区',897,'321202',3),
(899,'高港区',897,'321203',3),
(900,'姜堰区',897,'321204',3),
(901,'兴化市',897,'321281',3),
(902,'靖江市',897,'321282',3),
(903,'泰兴市',897,'321283',3),
(904,'宿迁市',800,'321300',2),
(905,'宿城区',904,'321302',3),
(906,'宿豫区',904,'321311',3),
(907,'沭阳县',904,'321322',3),
(908,'泗阳县',904,'321323',3),
(909,'泗洪县',904,'321324',3),
(910,'浙江省',0,'330000',1),
(911,'杭州市',910,'330100',2),
(912,'上城区',911,'330102',3),
(913,'下城区',911,'330103',3),
(914,'江干区',911,'330104',3),
(915,'拱墅区',911,'330105',3),
(916,'西湖区',911,'330106',3),
(917,'滨江区',911,'330108',3),
(918,'萧山区',911,'330109',3),
(919,'余杭区',911,'330110',3),
(920,'富阳区',911,'330111',3),
(921,'临安区',911,'330112',3),
(922,'桐庐县',911,'330122',3),
(923,'淳安县',911,'330127',3),
(924,'建德市',911,'330182',3),
(925,'宁波市',910,'330200',2),
(926,'海曙区',925,'330203',3),
(927,'江北区',925,'330205',3),
(928,'北仑区',925,'330206',3),
(929,'镇海区',925,'330211',3),
(930,'鄞州区',925,'330212',3),
(931,'奉化区',925,'330213',3),
(932,'象山县',925,'330225',3),
(933,'宁海县',925,'330226',3),
(934,'余姚市',925,'330281',3),
(935,'慈溪市',925,'330282',3),
(936,'温州市',910,'330300',2),
(937,'鹿城区',936,'330302',3),
(938,'龙湾区',936,'330303',3),
(939,'瓯海区',936,'330304',3),
(940,'洞头区',936,'330305',3),
(941,'永嘉县',936,'330324',3),
(942,'平阳县',936,'330326',3),
(943,'苍南县',936,'330327',3),
(944,'文成县',936,'330328',3),
(945,'泰顺县',936,'330329',3),
(946,'瑞安市',936,'330381',3),
(947,'乐清市',936,'330382',3),
(948,'龙港市',936,'330383',3),
(949,'嘉兴市',910,'330400',2),
(950,'南湖区',949,'330402',3),
(951,'秀洲区',949,'330411',3),
(952,'嘉善县',949,'330421',3),
(953,'海盐县',949,'330424',3),
(954,'海宁市',949,'330481',3),
(955,'平湖市',949,'330482',3),
(956,'桐乡市',949,'330483',3),
(957,'湖州市',910,'330500',2),
(958,'吴兴区',957,'330502',3),
(959,'南浔区',957,'330503',3),
(960,'德清县',957,'330521',3),
(961,'长兴县',957,'330522',3),
(962,'安吉县',957,'330523',3),
(963,'绍兴市',910,'330600',2),
(964,'越城区',963,'330602',3),
(965,'柯桥区',963,'330603',3),
(966,'上虞区',963,'330604',3),
(967,'新昌县',963,'330624',3),
(968,'诸暨市',963,'330681',3),
(969,'嵊州市',963,'330683',3),
(970,'金华市',910,'330700',2),
(971,'婺城区',970,'330702',3),
(972,'金东区',970,'330703',3),
(973,'武义县',970,'330723',3),
(974,'浦江县',970,'330726',3),
(975,'磐安县',970,'330727',3),
(976,'兰溪市',970,'330781',3),
(977,'义乌市',970,'330782',3),
(978,'东阳市',970,'330783',3),
(979,'永康市',970,'330784',3),
(980,'衢州市',910,'330800',2),
(981,'柯城区',980,'330802',3),
(982,'衢江区',980,'330803',3),
(983,'常山县',980,'330822',3),
(984,'开化县',980,'330824',3),
(985,'龙游县',980,'330825',3),
(986,'江山市',980,'330881',3),
(987,'舟山市',910,'330900',2),
(988,'定海区',987,'330902',3),
(989,'普陀区',987,'330903',3),
(990,'岱山县',987,'330921',3),
(991,'嵊泗县',987,'330922',3),
(992,'台州市',910,'331000',2),
(993,'椒江区',992,'331002',3),
(994,'黄岩区',992,'331003',3),
(995,'路桥区',992,'331004',3),
(996,'三门县',992,'331022',3),
(997,'天台县',992,'331023',3),
(998,'仙居县',992,'331024',3),
(999,'温岭市',992,'331081',3),
(1000,'临海市',992,'331082',3),
(1001,'玉环市',992,'331083',3),
(1002,'丽水市',910,'331100',2),
(1003,'莲都区',1002,'331102',3),
(1004,'青田县',1002,'331121',3),
(1005,'缙云县',1002,'331122',3),
(1006,'遂昌县',1002,'331123',3),
(1007,'松阳县',1002,'331124',3),
(1008,'云和县',1002,'331125',3),
(1009,'庆元县',1002,'331126',3),
(1010,'景宁畲族自治县',1002,'331127',3),
(1011,'龙泉市',1002,'331181',3),
(1012,'安徽省',0,'340000',1),
(1013,'合肥市',1012,'340100',2),
(1014,'瑶海区',1013,'340102',3),
(1015,'庐阳区',1013,'340103',3),
(1016,'蜀山区',1013,'340104',3),
(1017,'包河区',1013,'340111',3),
(1018,'长丰县',1013,'340121',3),
(1019,'肥东县',1013,'340122',3),
(1020,'肥西县',1013,'340123',3),
(1021,'庐江县',1013,'340124',3),
(1022,'巢湖市',1013,'340181',3),
(1023,'芜湖市',1012,'340200',2),
(1024,'镜湖区',1023,'340202',3),
(1025,'弋江区',1023,'340203',3),
(1026,'鸠江区',1023,'340207',3),
(1027,'三山区',1023,'340208',3),
(1028,'芜湖县',1023,'340221',3),
(1029,'繁昌县',1023,'340222',3),
(1030,'南陵县',1023,'340223',3),
(1031,'无为市',1023,'340281',3),
(1032,'蚌埠市',1012,'340300',2),
(1033,'龙子湖区',1032,'340302',3),
(1034,'蚌山区',1032,'340303',3),
(1035,'禹会区',1032,'340304',3),
(1036,'淮上区',1032,'340311',3),
(1037,'怀远县',1032,'340321',3),
(1038,'五河县',1032,'340322',3),
(1039,'固镇县',1032,'340323',3),
(1040,'淮南市',1012,'340400',2),
(1041,'大通区',1040,'340402',3),
(1042,'田家庵区',1040,'340403',3),
(1043,'谢家集区',1040,'340404',3),
(1044,'八公山区',1040,'340405',3),
(1045,'潘集区',1040,'340406',3),
(1046,'凤台县',1040,'340421',3),
(1047,'寿县',1040,'340422',3),
(1048,'马鞍山市',1012,'340500',2),
(1049,'花山区',1048,'340503',3),
(1050,'雨山区',1048,'340504',3),
(1051,'博望区',1048,'340506',3),
(1052,'当涂县',1048,'340521',3),
(1053,'含山县',1048,'340522',3),
(1054,'和县',1048,'340523',3),
(1055,'淮北市',1012,'340600',2),
(1056,'杜集区',1055,'340602',3),
(1057,'相山区',1055,'340603',3),
(1058,'烈山区',1055,'340604',3),
(1059,'濉溪县',1055,'340621',3),
(1060,'铜陵市',1012,'340700',2),
(1061,'铜官区',1060,'340705',3),
(1062,'义安区',1060,'340706',3),
(1063,'郊区',1060,'340711',3),
(1064,'枞阳县',1060,'340722',3),
(1065,'安庆市',1012,'340800',2),
(1066,'迎江区',1065,'340802',3),
(1067,'大观区',1065,'340803',3),
(1068,'宜秀区',1065,'340811',3),
(1069,'怀宁县',1065,'340822',3),
(1070,'太湖县',1065,'340825',3),
(1071,'宿松县',1065,'340826',3),
(1072,'望江县',1065,'340827',3),
(1073,'岳西县',1065,'340828',3),
(1074,'桐城市',1065,'340881',3),
(1075,'潜山市',1065,'340882',3),
(1076,'黄山市',1012,'341000',2),
(1077,'屯溪区',1076,'341002',3),
(1078,'黄山区',1076,'341003',3),
(1079,'徽州区',1076,'341004',3),
(1080,'歙县',1076,'341021',3),
(1081,'休宁县',1076,'341022',3),
(1082,'黟县',1076,'341023',3),
(1083,'祁门县',1076,'341024',3),
(1084,'滁州市',1012,'341100',2),
(1085,'琅琊区',1084,'341102',3),
(1086,'南谯区',1084,'341103',3),
(1087,'来安县',1084,'341122',3),
(1088,'全椒县',1084,'341124',3),
(1089,'定远县',1084,'341125',3),
(1090,'凤阳县',1084,'341126',3),
(1091,'天长市',1084,'341181',3),
(1092,'明光市',1084,'341182',3),
(1093,'阜阳市',1012,'341200',2),
(1094,'颍州区',1093,'341202',3),
(1095,'颍东区',1093,'341203',3),
(1096,'颍泉区',1093,'341204',3),
(1097,'临泉县',1093,'341221',3),
(1098,'太和县',1093,'341222',3),
(1099,'阜南县',1093,'341225',3),
(1100,'颍上县',1093,'341226',3),
(1101,'界首市',1093,'341282',3),
(1102,'宿州市',1012,'341300',2),
(1103,'埇桥区',1102,'341302',3),
(1104,'砀山县',1102,'341321',3),
(1105,'萧县',1102,'341322',3),
(1106,'灵璧县',1102,'341323',3),
(1107,'泗县',1102,'341324',3),
(1108,'六安市',1012,'341500',2),
(1109,'金安区',1108,'341502',3),
(1110,'裕安区',1108,'341503',3),
(1111,'叶集区',1108,'341504',3),
(1112,'霍邱县',1108,'341522',3),
(1113,'舒城县',1108,'341523',3),
(1114,'金寨县',1108,'341524',3),
(1115,'霍山县',1108,'341525',3),
(1116,'亳州市',1012,'341600',2),
(1117,'谯城区',1116,'341602',3),
(1118,'涡阳县',1116,'341621',3),
(1119,'蒙城县',1116,'341622',3),
(1120,'利辛县',1116,'341623',3),
(1121,'池州市',1012,'341700',2),
(1122,'贵池区',1121,'341702',3),
(1123,'东至县',1121,'341721',3),
(1124,'石台县',1121,'341722',3),
(1125,'青阳县',1121,'341723',3),
(1126,'宣城市',1012,'341800',2),
(1127,'宣州区',1126,'341802',3),
(1128,'郎溪县',1126,'341821',3),
(1129,'泾县',1126,'341823',3),
(1130,'绩溪县',1126,'341824',3),
(1131,'旌德县',1126,'341825',3),
(1132,'宁国市',1126,'341881',3),
(1133,'广德市',1126,'341882',3),
(1134,'福建省',0,'350000',1),
(1135,'福州市',1134,'350100',2),
(1136,'鼓楼区',1135,'350102',3),
(1137,'台江区',1135,'350103',3),
(1138,'仓山区',1135,'350104',3),
(1139,'马尾区',1135,'350105',3),
(1140,'晋安区',1135,'350111',3),
(1141,'长乐区',1135,'350112',3),
(1142,'闽侯县',1135,'350121',3),
(1143,'连江县',1135,'350122',3),
(1144,'罗源县',1135,'350123',3),
(1145,'闽清县',1135,'350124',3),
(1146,'永泰县',1135,'350125',3),
(1147,'平潭县',1135,'350128',3),
(1148,'福清市',1135,'350181',3),
(1149,'厦门市',1134,'350200',2),
(1150,'思明区',1149,'350203',3),
(1151,'海沧区',1149,'350205',3),
(1152,'湖里区',1149,'350206',3),
(1153,'集美区',1149,'350211',3),
(1154,'同安区',1149,'350212',3),
(1155,'翔安区',1149,'350213',3),
(1156,'莆田市',1134,'350300',2),
(1157,'城厢区',1156,'350302',3),
(1158,'涵江区',1156,'350303',3),
(1159,'荔城区',1156,'350304',3),
(1160,'秀屿区',1156,'350305',3),
(1161,'仙游县',1156,'350322',3),
(1162,'三明市',1134,'350400',2),
(1163,'梅列区',1162,'350402',3),
(1164,'三元区',1162,'350403',3),
(1165,'明溪县',1162,'350421',3),
(1166,'清流县',1162,'350423',3),
(1167,'宁化县',1162,'350424',3),
(1168,'大田县',1162,'350425',3),
(1169,'尤溪县',1162,'350426',3),
(1170,'沙县',1162,'350427',3),
(1171,'将乐县',1162,'350428',3),
(1172,'泰宁县',1162,'350429',3),
(1173,'建宁县',1162,'350430',3),
(1174,'永安市',1162,'350481',3),
(1175,'泉州市',1134,'350500',2),
(1176,'鲤城区',1175,'350502',3),
(1177,'丰泽区',1175,'350503',3),
(1178,'洛江区',1175,'350504',3),
(1179,'泉港区',1175,'350505',3),
(1180,'惠安县',1175,'350521',3),
(1181,'安溪县',1175,'350524',3),
(1182,'永春县',1175,'350525',3),
(1183,'德化县',1175,'350526',3),
(1184,'金门县',1175,'350527',3),
(1185,'石狮市',1175,'350581',3),
(1186,'晋江市',1175,'350582',3),
(1187,'南安市',1175,'350583',3),
(1188,'漳州市',1134,'350600',2),
(1189,'芗城区',1188,'350602',3),
(1190,'龙文区',1188,'350603',3),
(1191,'云霄县',1188,'350622',3),
(1192,'漳浦县',1188,'350623',3),
(1193,'诏安县',1188,'350624',3),
(1194,'长泰县',1188,'350625',3),
(1195,'东山县',1188,'350626',3),
(1196,'南靖县',1188,'350627',3),
(1197,'平和县',1188,'350628',3),
(1198,'华安县',1188,'350629',3),
(1199,'龙海市',1188,'350681',3),
(1200,'南平市',1134,'350700',2),
(1201,'延平区',1200,'350702',3),
(1202,'建阳区',1200,'350703',3),
(1203,'顺昌县',1200,'350721',3),
(1204,'浦城县',1200,'350722',3),
(1205,'光泽县',1200,'350723',3),
(1206,'松溪县',1200,'350724',3),
(1207,'政和县',1200,'350725',3),
(1208,'邵武市',1200,'350781',3),
(1209,'武夷山市',1200,'350782',3),
(1210,'建瓯市',1200,'350783',3),
(1211,'龙岩市',1134,'350800',2),
(1212,'新罗区',1211,'350802',3),
(1213,'永定区',1211,'350803',3),
(1214,'长汀县',1211,'350821',3),
(1215,'上杭县',1211,'350823',3),
(1216,'武平县',1211,'350824',3),
(1217,'连城县',1211,'350825',3),
(1218,'漳平市',1211,'350881',3),
(1219,'宁德市',1134,'350900',2),
(1220,'蕉城区',1219,'350902',3),
(1221,'霞浦县',1219,'350921',3),
(1222,'古田县',1219,'350922',3),
(1223,'屏南县',1219,'350923',3),
(1224,'寿宁县',1219,'350924',3),
(1225,'周宁县',1219,'350925',3),
(1226,'柘荣县',1219,'350926',3),
(1227,'福安市',1219,'350981',3),
(1228,'福鼎市',1219,'350982',3),
(1229,'江西省',0,'360000',1),
(1230,'南昌市',1229,'360100',2),
(1231,'东湖区',1230,'360102',3),
(1232,'西湖区',1230,'360103',3),
(1233,'青云谱区',1230,'360104',3),
(1234,'青山湖区',1230,'360111',3),
(1235,'新建区',1230,'360112',3),
(1236,'红谷滩区',1230,'360113',3),
(1237,'南昌县',1230,'360121',3),
(1238,'安义县',1230,'360123',3),
(1239,'进贤县',1230,'360124',3),
(1240,'景德镇市',1229,'360200',2),
(1241,'昌江区',1240,'360202',3),
(1242,'珠山区',1240,'360203',3),
(1243,'浮梁县',1240,'360222',3),
(1244,'乐平市',1240,'360281',3),
(1245,'萍乡市',1229,'360300',2),
(1246,'安源区',1245,'360302',3),
(1247,'湘东区',1245,'360313',3),
(1248,'莲花县',1245,'360321',3),
(1249,'上栗县',1245,'360322',3),
(1250,'芦溪县',1245,'360323',3),
(1251,'九江市',1229,'360400',2),
(1252,'濂溪区',1251,'360402',3),
(1253,'浔阳区',1251,'360403',3),
(1254,'柴桑区',1251,'360404',3),
(1255,'武宁县',1251,'360423',3),
(1256,'修水县',1251,'360424',3),
(1257,'永修县',1251,'360425',3),
(1258,'德安县',1251,'360426',3),
(1259,'都昌县',1251,'360428',3),
(1260,'湖口县',1251,'360429',3),
(1261,'彭泽县',1251,'360430',3),
(1262,'瑞昌市',1251,'360481',3),
(1263,'共青城市',1251,'360482',3),
(1264,'庐山市',1251,'360483',3),
(1265,'新余市',1229,'360500',2),
(1266,'渝水区',1265,'360502',3),
(1267,'分宜县',1265,'360521',3),
(1268,'鹰潭市',1229,'360600',2),
(1269,'月湖区',1268,'360602',3),
(1270,'余江区',1268,'360603',3),
(1271,'贵溪市',1268,'360681',3),
(1272,'赣州市',1229,'360700',2),
(1273,'章贡区',1272,'360702',3),
(1274,'南康区',1272,'360703',3),
(1275,'赣县区',1272,'360704',3),
(1276,'信丰县',1272,'360722',3),
(1277,'大余县',1272,'360723',3),
(1278,'上犹县',1272,'360724',3),
(1279,'崇义县',1272,'360725',3),
(1280,'安远县',1272,'360726',3),
(1281,'龙南县',1272,'360727',3),
(1282,'定南县',1272,'360728',3),
(1283,'全南县',1272,'360729',3),
(1284,'宁都县',1272,'360730',3),
(1285,'于都县',1272,'360731',3),
(1286,'兴国县',1272,'360732',3),
(1287,'会昌县',1272,'360733',3),
(1288,'寻乌县',1272,'360734',3),
(1289,'石城县',1272,'360735',3),
(1290,'瑞金市',1272,'360781',3),
(1291,'吉安市',1229,'360800',2),
(1292,'吉州区',1291,'360802',3),
(1293,'青原区',1291,'360803',3),
(1294,'吉安县',1291,'360821',3),
(1295,'吉水县',1291,'360822',3),
(1296,'峡江县',1291,'360823',3),
(1297,'新干县',1291,'360824',3),
(1298,'永丰县',1291,'360825',3),
(1299,'泰和县',1291,'360826',3),
(1300,'遂川县',1291,'360827',3),
(1301,'万安县',1291,'360828',3),
(1302,'安福县',1291,'360829',3),
(1303,'永新县',1291,'360830',3),
(1304,'井冈山市',1291,'360881',3),
(1305,'宜春市',1229,'360900',2),
(1306,'袁州区',1305,'360902',3),
(1307,'奉新县',1305,'360921',3),
(1308,'万载县',1305,'360922',3),
(1309,'上高县',1305,'360923',3),
(1310,'宜丰县',1305,'360924',3),
(1311,'靖安县',1305,'360925',3),
(1312,'铜鼓县',1305,'360926',3),
(1313,'丰城市',1305,'360981',3),
(1314,'樟树市',1305,'360982',3),
(1315,'高安市',1305,'360983',3),
(1316,'抚州市',1229,'361000',2),
(1317,'临川区',1316,'361002',3),
(1318,'东乡区',1316,'361003',3),
(1319,'南城县',1316,'361021',3),
(1320,'黎川县',1316,'361022',3),
(1321,'南丰县',1316,'361023',3),
(1322,'崇仁县',1316,'361024',3),
(1323,'乐安县',1316,'361025',3),
(1324,'宜黄县',1316,'361026',3),
(1325,'金溪县',1316,'361027',3),
(1326,'资溪县',1316,'361028',3),
(1327,'广昌县',1316,'361030',3),
(1328,'上饶市',1229,'361100',2),
(1329,'信州区',1328,'361102',3),
(1330,'广丰区',1328,'361103',3),
(1331,'广信区',1328,'361104',3),
(1332,'玉山县',1328,'361123',3),
(1333,'铅山县',1328,'361124',3),
(1334,'横峰县',1328,'361125',3),
(1335,'弋阳县',1328,'361126',3),
(1336,'余干县',1328,'361127',3),
(1337,'鄱阳县',1328,'361128',3),
(1338,'万年县',1328,'361129',3),
(1339,'婺源县',1328,'361130',3),
(1340,'德兴市',1328,'361181',3),
(1341,'山东省',0,'370000',1),
(1342,'济南市',1341,'370100',2),
(1343,'历下区',1342,'370102',3),
(1344,'市中区',1342,'370103',3),
(1345,'槐荫区',1342,'370104',3),
(1346,'天桥区',1342,'370105',3),
(1347,'历城区',1342,'370112',3),
(1348,'长清区',1342,'370113',3),
(1349,'章丘区',1342,'370114',3),
(1350,'济阳区',1342,'370115',3),
(1351,'莱芜区',1342,'370116',3),
(1352,'钢城区',1342,'370117',3),
(1353,'平阴县',1342,'370124',3),
(1354,'商河县',1342,'370126',3),
(1355,'青岛市',1341,'370200',2),
(1356,'市南区',1355,'370202',3),
(1357,'市北区',1355,'370203',3),
(1358,'黄岛区',1355,'370211',3),
(1359,'崂山区',1355,'370212',3),
(1360,'李沧区',1355,'370213',3),
(1361,'城阳区',1355,'370214',3),
(1362,'即墨区',1355,'370215',3),
(1363,'胶州市',1355,'370281',3),
(1364,'平度市',1355,'370283',3),
(1365,'莱西市',1355,'370285',3),
(1366,'淄博市',1341,'370300',2),
(1367,'淄川区',1366,'370302',3),
(1368,'张店区',1366,'370303',3),
(1369,'博山区',1366,'370304',3),
(1370,'临淄区',1366,'370305',3),
(1371,'周村区',1366,'370306',3),
(1372,'桓台县',1366,'370321',3),
(1373,'高青县',1366,'370322',3),
(1374,'沂源县',1366,'370323',3),
(1375,'枣庄市',1341,'370400',2),
(1376,'市中区',1375,'370402',3),
(1377,'薛城区',1375,'370403',3),
(1378,'峄城区',1375,'370404',3),
(1379,'台儿庄区',1375,'370405',3),
(1380,'山亭区',1375,'370406',3),
(1381,'滕州市',1375,'370481',3),
(1382,'东营市',1341,'370500',2),
(1383,'东营区',1382,'370502',3),
(1384,'河口区',1382,'370503',3),
(1385,'垦利区',1382,'370505',3),
(1386,'利津县',1382,'370522',3),
(1387,'广饶县',1382,'370523',3),
(1388,'烟台市',1341,'370600',2),
(1389,'芝罘区',1388,'370602',3),
(1390,'福山区',1388,'370611',3),
(1391,'牟平区',1388,'370612',3),
(1392,'莱山区',1388,'370613',3),
(1393,'长岛县',1388,'370634',3),
(1394,'龙口市',1388,'370681',3),
(1395,'莱阳市',1388,'370682',3),
(1396,'莱州市',1388,'370683',3),
(1397,'蓬莱市',1388,'370684',3),
(1398,'招远市',1388,'370685',3),
(1399,'栖霞市',1388,'370686',3),
(1400,'海阳市',1388,'370687',3),
(1401,'潍坊市',1341,'370700',2),
(1402,'潍城区',1401,'370702',3),
(1403,'寒亭区',1401,'370703',3),
(1404,'坊子区',1401,'370704',3),
(1405,'奎文区',1401,'370705',3),
(1406,'临朐县',1401,'370724',3),
(1407,'昌乐县',1401,'370725',3),
(1408,'青州市',1401,'370781',3),
(1409,'诸城市',1401,'370782',3),
(1410,'寿光市',1401,'370783',3),
(1411,'安丘市',1401,'370784',3),
(1412,'高密市',1401,'370785',3),
(1413,'昌邑市',1401,'370786',3),
(1414,'济宁市',1341,'370800',2),
(1415,'任城区',1414,'370811',3),
(1416,'兖州区',1414,'370812',3),
(1417,'微山县',1414,'370826',3),
(1418,'鱼台县',1414,'370827',3),
(1419,'金乡县',1414,'370828',3),
(1420,'嘉祥县',1414,'370829',3),
(1421,'汶上县',1414,'370830',3),
(1422,'泗水县',1414,'370831',3),
(1423,'梁山县',1414,'370832',3),
(1424,'曲阜市',1414,'370881',3),
(1425,'邹城市',1414,'370883',3),
(1426,'泰安市',1341,'370900',2),
(1427,'泰山区',1426,'370902',3),
(1428,'岱岳区',1426,'370911',3),
(1429,'宁阳县',1426,'370921',3),
(1430,'东平县',1426,'370923',3),
(1431,'新泰市',1426,'370982',3),
(1432,'肥城市',1426,'370983',3),
(1433,'威海市',1341,'371000',2),
(1434,'环翠区',1433,'371002',3),
(1435,'文登区',1433,'371003',3),
(1436,'荣成市',1433,'371082',3),
(1437,'乳山市',1433,'371083',3),
(1438,'日照市',1341,'371100',2),
(1439,'东港区',1438,'371102',3),
(1440,'岚山区',1438,'371103',3),
(1441,'五莲县',1438,'371121',3),
(1442,'莒县',1438,'371122',3),
(1443,'临沂市',1341,'371300',2),
(1444,'兰山区',1443,'371302',3),
(1445,'罗庄区',1443,'371311',3),
(1446,'河东区',1443,'371312',3),
(1447,'沂南县',1443,'371321',3),
(1448,'郯城县',1443,'371322',3),
(1449,'沂水县',1443,'371323',3),
(1450,'兰陵县',1443,'371324',3),
(1451,'费县',1443,'371325',3),
(1452,'平邑县',1443,'371326',3),
(1453,'莒南县',1443,'371327',3),
(1454,'蒙阴县',1443,'371328',3),
(1455,'临沭县',1443,'371329',3),
(1456,'德州市',1341,'371400',2),
(1457,'德城区',1456,'371402',3),
(1458,'陵城区',1456,'371403',3),
(1459,'宁津县',1456,'371422',3),
(1460,'庆云县',1456,'371423',3),
(1461,'临邑县',1456,'371424',3),
(1462,'齐河县',1456,'371425',3),
(1463,'平原县',1456,'371426',3),
(1464,'夏津县',1456,'371427',3),
(1465,'武城县',1456,'371428',3),
(1466,'乐陵市',1456,'371481',3),
(1467,'禹城市',1456,'371482',3),
(1468,'聊城市',1341,'371500',2),
(1469,'东昌府区',1468,'371502',3),
(1470,'茌平区',1468,'371503',3),
(1471,'阳谷县',1468,'371521',3),
(1472,'莘县',1468,'371522',3),
(1473,'东阿县',1468,'371524',3),
(1474,'冠县',1468,'371525',3),
(1475,'高唐县',1468,'371526',3),
(1476,'临清市',1468,'371581',3),
(1477,'滨州市',1341,'371600',2),
(1478,'滨城区',1477,'371602',3),
(1479,'沾化区',1477,'371603',3),
(1480,'惠民县',1477,'371621',3),
(1481,'阳信县',1477,'371622',3),
(1482,'无棣县',1477,'371623',3),
(1483,'博兴县',1477,'371625',3),
(1484,'邹平市',1477,'371681',3),
(1485,'菏泽市',1341,'371700',2),
(1486,'牡丹区',1485,'371702',3),
(1487,'定陶区',1485,'371703',3),
(1488,'曹县',1485,'371721',3),
(1489,'单县',1485,'371722',3),
(1490,'成武县',1485,'371723',3),
(1491,'巨野县',1485,'371724',3),
(1492,'郓城县',1485,'371725',3),
(1493,'鄄城县',1485,'371726',3),
(1494,'东明县',1485,'371728',3),
(1495,'河南省',0,'410000',1),
(1496,'郑州市',1495,'410100',2),
(1497,'中原区',1496,'410102',3),
(1498,'二七区',1496,'410103',3),
(1499,'管城回族区',1496,'410104',3),
(1500,'金水区',1496,'410105',3),
(1501,'上街区',1496,'410106',3),
(1502,'惠济区',1496,'410108',3),
(1503,'中牟县',1496,'410122',3),
(1504,'巩义市',1496,'410181',3),
(1505,'荥阳市',1496,'410182',3),
(1506,'新密市',1496,'410183',3),
(1507,'新郑市',1496,'410184',3),
(1508,'登封市',1496,'410185',3),
(1509,'开封市',1495,'410200',2),
(1510,'龙亭区',1509,'410202',3),
(1511,'顺河回族区',1509,'410203',3),
(1512,'鼓楼区',1509,'410204',3),
(1513,'禹王台区',1509,'410205',3),
(1514,'祥符区',1509,'410212',3),
(1515,'杞县',1509,'410221',3),
(1516,'通许县',1509,'410222',3),
(1517,'尉氏县',1509,'410223',3),
(1518,'兰考县',1509,'410225',3),
(1519,'洛阳市',1495,'410300',2),
(1520,'老城区',1519,'410302',3),
(1521,'西工区',1519,'410303',3),
(1522,'瀍河回族区',1519,'410304',3),
(1523,'涧西区',1519,'410305',3),
(1524,'吉利区',1519,'410306',3),
(1525,'洛龙区',1519,'410311',3),
(1526,'孟津县',1519,'410322',3),
(1527,'新安县',1519,'410323',3),
(1528,'栾川县',1519,'410324',3),
(1529,'嵩县',1519,'410325',3),
(1530,'汝阳县',1519,'410326',3),
(1531,'宜阳县',1519,'410327',3),
(1532,'洛宁县',1519,'410328',3),
(1533,'伊川县',1519,'410329',3),
(1534,'偃师市',1519,'410381',3),
(1535,'平顶山市',1495,'410400',2),
(1536,'新华区',1535,'410402',3),
(1537,'卫东区',1535,'410403',3),
(1538,'石龙区',1535,'410404',3),
(1539,'湛河区',1535,'410411',3),
(1540,'宝丰县',1535,'410421',3),
(1541,'叶县',1535,'410422',3),
(1542,'鲁山县',1535,'410423',3),
(1543,'郏县',1535,'410425',3),
(1544,'舞钢市',1535,'410481',3),
(1545,'汝州市',1535,'410482',3),
(1546,'安阳市',1495,'410500',2),
(1547,'文峰区',1546,'410502',3),
(1548,'北关区',1546,'410503',3),
(1549,'殷都区',1546,'410505',3),
(1550,'龙安区',1546,'410506',3),
(1551,'安阳县',1546,'410522',3),
(1552,'汤阴县',1546,'410523',3),
(1553,'滑县',1546,'410526',3),
(1554,'内黄县',1546,'410527',3),
(1555,'林州市',1546,'410581',3),
(1556,'鹤壁市',1495,'410600',2),
(1557,'鹤山区',1556,'410602',3),
(1558,'山城区',1556,'410603',3),
(1559,'淇滨区',1556,'410611',3),
(1560,'浚县',1556,'410621',3),
(1561,'淇县',1556,'410622',3),
(1562,'新乡市',1495,'410700',2),
(1563,'红旗区',1562,'410702',3),
(1564,'卫滨区',1562,'410703',3),
(1565,'凤泉区',1562,'410704',3),
(1566,'牧野区',1562,'410711',3),
(1567,'新乡县',1562,'410721',3),
(1568,'获嘉县',1562,'410724',3),
(1569,'原阳县',1562,'410725',3),
(1570,'延津县',1562,'410726',3),
(1571,'封丘县',1562,'410727',3),
(1572,'卫辉市',1562,'410781',3),
(1573,'辉县市',1562,'410782',3),
(1574,'长垣市',1562,'410783',3),
(1575,'焦作市',1495,'410800',2),
(1576,'解放区',1575,'410802',3),
(1577,'中站区',1575,'410803',3),
(1578,'马村区',1575,'410804',3),
(1579,'山阳区',1575,'410811',3),
(1580,'修武县',1575,'410821',3),
(1581,'博爱县',1575,'410822',3),
(1582,'武陟县',1575,'410823',3),
(1583,'温县',1575,'410825',3),
(1584,'沁阳市',1575,'410882',3),
(1585,'孟州市',1575,'410883',3),
(1586,'濮阳市',1495,'410900',2),
(1587,'华龙区',1586,'410902',3),
(1588,'清丰县',1586,'410922',3),
(1589,'南乐县',1586,'410923',3),
(1590,'范县',1586,'410926',3),
(1591,'台前县',1586,'410927',3),
(1592,'濮阳县',1586,'410928',3),
(1593,'许昌市',1495,'411000',2),
(1594,'魏都区',1593,'411002',3),
(1595,'建安区',1593,'411003',3),
(1596,'鄢陵县',1593,'411024',3),
(1597,'襄城县',1593,'411025',3),
(1598,'禹州市',1593,'411081',3),
(1599,'长葛市',1593,'411082',3),
(1600,'漯河市',1495,'411100',2),
(1601,'源汇区',1600,'411102',3),
(1602,'郾城区',1600,'411103',3),
(1603,'召陵区',1600,'411104',3),
(1604,'舞阳县',1600,'411121',3),
(1605,'临颍县',1600,'411122',3),
(1606,'三门峡市',1495,'411200',2),
(1607,'湖滨区',1606,'411202',3),
(1608,'陕州区',1606,'411203',3),
(1609,'渑池县',1606,'411221',3),
(1610,'卢氏县',1606,'411224',3),
(1611,'义马市',1606,'411281',3),
(1612,'灵宝市',1606,'411282',3),
(1613,'南阳市',1495,'411300',2),
(1614,'宛城区',1613,'411302',3),
(1615,'卧龙区',1613,'411303',3),
(1616,'南召县',1613,'411321',3),
(1617,'方城县',1613,'411322',3),
(1618,'西峡县',1613,'411323',3),
(1619,'镇平县',1613,'411324',3),
(1620,'内乡县',1613,'411325',3),
(1621,'淅川县',1613,'411326',3),
(1622,'社旗县',1613,'411327',3),
(1623,'唐河县',1613,'411328',3),
(1624,'新野县',1613,'411329',3),
(1625,'桐柏县',1613,'411330',3),
(1626,'邓州市',1613,'411381',3),
(1627,'商丘市',1495,'411400',2),
(1628,'梁园区',1627,'411402',3),
(1629,'睢阳区',1627,'411403',3),
(1630,'民权县',1627,'411421',3),
(1631,'睢县',1627,'411422',3),
(1632,'宁陵县',1627,'411423',3),
(1633,'柘城县',1627,'411424',3),
(1634,'虞城县',1627,'411425',3),
(1635,'夏邑县',1627,'411426',3),
(1636,'永城市',1627,'411481',3),
(1637,'信阳市',1495,'411500',2),
(1638,'浉河区',1637,'411502',3),
(1639,'平桥区',1637,'411503',3),
(1640,'罗山县',1637,'411521',3),
(1641,'光山县',1637,'411522',3),
(1642,'新县',1637,'411523',3),
(1643,'商城县',1637,'411524',3),
(1644,'固始县',1637,'411525',3),
(1645,'潢川县',1637,'411526',3),
(1646,'淮滨县',1637,'411527',3),
(1647,'息县',1637,'411528',3),
(1648,'周口市',1495,'411600',2),
(1649,'川汇区',1648,'411602',3),
(1650,'淮阳区',1648,'411603',3),
(1651,'扶沟县',1648,'411621',3),
(1652,'西华县',1648,'411622',3),
(1653,'商水县',1648,'411623',3),
(1654,'沈丘县',1648,'411624',3),
(1655,'郸城县',1648,'411625',3),
(1656,'太康县',1648,'411627',3),
(1657,'鹿邑县',1648,'411628',3),
(1658,'项城市',1648,'411681',3),
(1659,'驻马店市',1495,'411700',2),
(1660,'驿城区',1659,'411702',3),
(1661,'西平县',1659,'411721',3),
(1662,'上蔡县',1659,'411722',3),
(1663,'平舆县',1659,'411723',3),
(1664,'正阳县',1659,'411724',3),
(1665,'确山县',1659,'411725',3),
(1666,'泌阳县',1659,'411726',3),
(1667,'汝南县',1659,'411727',3),
(1668,'遂平县',1659,'411728',3),
(1669,'新蔡县',1659,'411729',3),
(1670,'济源市',1495,'419001',3),
(1671,'湖北省',0,'420000',1),
(1672,'武汉市',1671,'420100',2),
(1673,'江岸区',1672,'420102',3),
(1674,'江汉区',1672,'420103',3),
(1675,'硚口区',1672,'420104',3),
(1676,'汉阳区',1672,'420105',3),
(1677,'武昌区',1672,'420106',3),
(1678,'青山区',1672,'420107',3),
(1679,'洪山区',1672,'420111',3),
(1680,'东西湖区',1672,'420112',3),
(1681,'汉南区',1672,'420113',3),
(1682,'蔡甸区',1672,'420114',3),
(1683,'江夏区',1672,'420115',3),
(1684,'黄陂区',1672,'420116',3),
(1685,'新洲区',1672,'420117',3),
(1686,'黄石市',1671,'420200',2),
(1687,'黄石港区',1686,'420202',3),
(1688,'西塞山区',1686,'420203',3),
(1689,'下陆区',1686,'420204',3),
(1690,'铁山区',1686,'420205',3),
(1691,'阳新县',1686,'420222',3),
(1692,'大冶市',1686,'420281',3),
(1693,'十堰市',1671,'420300',2),
(1694,'茅箭区',1693,'420302',3),
(1695,'张湾区',1693,'420303',3),
(1696,'郧阳区',1693,'420304',3),
(1697,'郧西县',1693,'420322',3),
(1698,'竹山县',1693,'420323',3),
(1699,'竹溪县',1693,'420324',3),
(1700,'房县',1693,'420325',3),
(1701,'丹江口市',1693,'420381',3),
(1702,'宜昌市',1671,'420500',2),
(1703,'西陵区',1702,'420502',3),
(1704,'伍家岗区',1702,'420503',3),
(1705,'点军区',1702,'420504',3),
(1706,'猇亭区',1702,'420505',3),
(1707,'夷陵区',1702,'420506',3),
(1708,'远安县',1702,'420525',3),
(1709,'兴山县',1702,'420526',3),
(1710,'秭归县',1702,'420527',3),
(1711,'长阳土家族自治县',1702,'420528',3),
(1712,'五峰土家族自治县',1702,'420529',3),
(1713,'宜都市',1702,'420581',3),
(1714,'当阳市',1702,'420582',3),
(1715,'枝江市',1702,'420583',3),
(1716,'襄阳市',1671,'420600',2),
(1717,'襄城区',1716,'420602',3),
(1718,'樊城区',1716,'420606',3),
(1719,'襄州区',1716,'420607',3),
(1720,'南漳县',1716,'420624',3),
(1721,'谷城县',1716,'420625',3),
(1722,'保康县',1716,'420626',3),
(1723,'老河口市',1716,'420682',3),
(1724,'枣阳市',1716,'420683',3),
(1725,'宜城市',1716,'420684',3),
(1726,'鄂州市',1671,'420700',2),
(1727,'梁子湖区',1726,'420702',3),
(1728,'华容区',1726,'420703',3),
(1729,'鄂城区',1726,'420704',3),
(1730,'荆门市',1671,'420800',2),
(1731,'东宝区',1730,'420802',3),
(1732,'掇刀区',1730,'420804',3),
(1733,'沙洋县',1730,'420822',3),
(1734,'钟祥市',1730,'420881',3),
(1735,'京山市',1730,'420882',3),
(1736,'孝感市',1671,'420900',2),
(1737,'孝南区',1736,'420902',3),
(1738,'孝昌县',1736,'420921',3),
(1739,'大悟县',1736,'420922',3),
(1740,'云梦县',1736,'420923',3),
(1741,'应城市',1736,'420981',3),
(1742,'安陆市',1736,'420982',3),
(1743,'汉川市',1736,'420984',3),
(1744,'荆州市',1671,'421000',2),
(1745,'沙市区',1744,'421002',3),
(1746,'荆州区',1744,'421003',3),
(1747,'公安县',1744,'421022',3),
(1748,'监利县',1744,'421023',3),
(1749,'江陵县',1744,'421024',3),
(1750,'石首市',1744,'421081',3),
(1751,'洪湖市',1744,'421083',3),
(1752,'松滋市',1744,'421087',3),
(1753,'黄冈市',1671,'421100',2),
(1754,'黄州区',1753,'421102',3),
(1755,'团风县',1753,'421121',3),
(1756,'红安县',1753,'421122',3),
(1757,'罗田县',1753,'421123',3),
(1758,'英山县',1753,'421124',3),
(1759,'浠水县',1753,'421125',3),
(1760,'蕲春县',1753,'421126',3),
(1761,'黄梅县',1753,'421127',3),
(1762,'麻城市',1753,'421181',3),
(1763,'武穴市',1753,'421182',3),
(1764,'咸宁市',1671,'421200',2),
(1765,'咸安区',1764,'421202',3),
(1766,'嘉鱼县',1764,'421221',3),
(1767,'通城县',1764,'421222',3),
(1768,'崇阳县',1764,'421223',3),
(1769,'通山县',1764,'421224',3),
(1770,'赤壁市',1764,'421281',3),
(1771,'随州市',1671,'421300',2),
(1772,'曾都区',1771,'421303',3),
(1773,'随县',1771,'421321',3),
(1774,'广水市',1771,'421381',3),
(1775,'恩施土家族苗族自治州',1671,'422800',2),
(1776,'恩施市',1775,'422801',3),
(1777,'利川市',1775,'422802',3),
(1778,'建始县',1775,'422822',3),
(1779,'巴东县',1775,'422823',3),
(1780,'宣恩县',1775,'422825',3),
(1781,'咸丰县',1775,'422826',3),
(1782,'来凤县',1775,'422827',3),
(1783,'鹤峰县',1775,'422828',3),
(1784,'仙桃市',1671,'429004',3),
(1785,'潜江市',1671,'429005',3),
(1786,'天门市',1671,'429006',3),
(1787,'神农架林区',1671,'429021',3),
(1788,'湖南省',0,'430000',1),
(1789,'长沙市',1788,'430100',2),
(1790,'芙蓉区',1789,'430102',3),
(1791,'天心区',1789,'430103',3),
(1792,'岳麓区',1789,'430104',3),
(1793,'开福区',1789,'430105',3),
(1794,'雨花区',1789,'430111',3),
(1795,'望城区',1789,'430112',3),
(1796,'长沙县',1789,'430121',3),
(1797,'浏阳市',1789,'430181',3),
(1798,'宁乡市',1789,'430182',3),
(1799,'株洲市',1788,'430200',2),
(1800,'荷塘区',1799,'430202',3),
(1801,'芦淞区',1799,'430203',3),
(1802,'石峰区',1799,'430204',3),
(1803,'天元区',1799,'430211',3),
(1804,'渌口区',1799,'430212',3),
(1805,'攸县',1799,'430223',3),
(1806,'茶陵县',1799,'430224',3),
(1807,'炎陵县',1799,'430225',3),
(1808,'醴陵市',1799,'430281',3),
(1809,'湘潭市',1788,'430300',2),
(1810,'雨湖区',1809,'430302',3),
(1811,'岳塘区',1809,'430304',3),
(1812,'湘潭县',1809,'430321',3),
(1813,'湘乡市',1809,'430381',3),
(1814,'韶山市',1809,'430382',3),
(1815,'衡阳市',1788,'430400',2),
(1816,'珠晖区',1815,'430405',3),
(1817,'雁峰区',1815,'430406',3),
(1818,'石鼓区',1815,'430407',3),
(1819,'蒸湘区',1815,'430408',3),
(1820,'南岳区',1815,'430412',3),
(1821,'衡阳县',1815,'430421',3),
(1822,'衡南县',1815,'430422',3),
(1823,'衡山县',1815,'430423',3),
(1824,'衡东县',1815,'430424',3),
(1825,'祁东县',1815,'430426',3),
(1826,'耒阳市',1815,'430481',3),
(1827,'常宁市',1815,'430482',3),
(1828,'邵阳市',1788,'430500',2),
(1829,'双清区',1828,'430502',3),
(1830,'大祥区',1828,'430503',3),
(1831,'北塔区',1828,'430511',3),
(1832,'新邵县',1828,'430522',3),
(1833,'邵阳县',1828,'430523',3),
(1834,'隆回县',1828,'430524',3),
(1835,'洞口县',1828,'430525',3),
(1836,'绥宁县',1828,'430527',3),
(1837,'新宁县',1828,'430528',3),
(1838,'城步苗族自治县',1828,'430529',3),
(1839,'武冈市',1828,'430581',3),
(1840,'邵东市',1828,'430582',3),
(1841,'岳阳市',1788,'430600',2),
(1842,'岳阳楼区',1841,'430602',3),
(1843,'云溪区',1841,'430603',3),
(1844,'君山区',1841,'430611',3),
(1845,'岳阳县',1841,'430621',3),
(1846,'华容县',1841,'430623',3),
(1847,'湘阴县',1841,'430624',3),
(1848,'平江县',1841,'430626',3),
(1849,'汨罗市',1841,'430681',3),
(1850,'临湘市',1841,'430682',3),
(1851,'常德市',1788,'430700',2),
(1852,'武陵区',1851,'430702',3),
(1853,'鼎城区',1851,'430703',3),
(1854,'安乡县',1851,'430721',3),
(1855,'汉寿县',1851,'430722',3),
(1856,'澧县',1851,'430723',3),
(1857,'临澧县',1851,'430724',3),
(1858,'桃源县',1851,'430725',3),
(1859,'石门县',1851,'430726',3),
(1860,'津市市',1851,'430781',3),
(1861,'张家界市',1788,'430800',2),
(1862,'永定区',1861,'430802',3),
(1863,'武陵源区',1861,'430811',3),
(1864,'慈利县',1861,'430821',3),
(1865,'桑植县',1861,'430822',3),
(1866,'益阳市',1788,'430900',2),
(1867,'资阳区',1866,'430902',3),
(1868,'赫山区',1866,'430903',3),
(1869,'南县',1866,'430921',3),
(1870,'桃江县',1866,'430922',3),
(1871,'安化县',1866,'430923',3),
(1872,'沅江市',1866,'430981',3),
(1873,'郴州市',1788,'431000',2),
(1874,'北湖区',1873,'431002',3),
(1875,'苏仙区',1873,'431003',3),
(1876,'桂阳县',1873,'431021',3),
(1877,'宜章县',1873,'431022',3),
(1878,'永兴县',1873,'431023',3),
(1879,'嘉禾县',1873,'431024',3),
(1880,'临武县',1873,'431025',3),
(1881,'汝城县',1873,'431026',3),
(1882,'桂东县',1873,'431027',3),
(1883,'安仁县',1873,'431028',3),
(1884,'资兴市',1873,'431081',3),
(1885,'永州市',1788,'431100',2),
(1886,'零陵区',1885,'431102',3),
(1887,'冷水滩区',1885,'431103',3),
(1888,'祁阳县',1885,'431121',3),
(1889,'东安县',1885,'431122',3),
(1890,'双牌县',1885,'431123',3),
(1891,'道县',1885,'431124',3),
(1892,'江永县',1885,'431125',3),
(1893,'宁远县',1885,'431126',3),
(1894,'蓝山县',1885,'431127',3),
(1895,'新田县',1885,'431128',3),
(1896,'江华瑶族自治县',1885,'431129',3),
(1897,'怀化市',1788,'431200',2),
(1898,'鹤城区',1897,'431202',3),
(1899,'中方县',1897,'431221',3),
(1900,'沅陵县',1897,'431222',3),
(1901,'辰溪县',1897,'431223',3),
(1902,'溆浦县',1897,'431224',3),
(1903,'会同县',1897,'431225',3),
(1904,'麻阳苗族自治县',1897,'431226',3),
(1905,'新晃侗族自治县',1897,'431227',3),
(1906,'芷江侗族自治县',1897,'431228',3),
(1907,'靖州苗族侗族自治县',1897,'431229',3),
(1908,'通道侗族自治县',1897,'431230',3),
(1909,'洪江市',1897,'431281',3),
(1910,'娄底市',1788,'431300',2),
(1911,'娄星区',1910,'431302',3),
(1912,'双峰县',1910,'431321',3),
(1913,'新化县',1910,'431322',3),
(1914,'冷水江市',1910,'431381',3),
(1915,'涟源市',1910,'431382',3),
(1916,'湘西土家族苗族自治州',1788,'433100',2),
(1917,'吉首市',1916,'433101',3),
(1918,'泸溪县',1916,'433122',3),
(1919,'凤凰县',1916,'433123',3),
(1920,'花垣县',1916,'433124',3),
(1921,'保靖县',1916,'433125',3),
(1922,'古丈县',1916,'433126',3),
(1923,'永顺县',1916,'433127',3),
(1924,'龙山县',1916,'433130',3),
(1925,'广东省',0,'440000',1),
(1926,'广州市',1925,'440100',2),
(1927,'荔湾区',1926,'440103',3),
(1928,'越秀区',1926,'440104',3),
(1929,'海珠区',1926,'440105',3),
(1930,'天河区',1926,'440106',3),
(1931,'白云区',1926,'440111',3),
(1932,'黄埔区',1926,'440112',3),
(1933,'番禺区',1926,'440113',3),
(1934,'花都区',1926,'440114',3),
(1935,'南沙区',1926,'440115',3),
(1936,'从化区',1926,'440117',3),
(1937,'增城区',1926,'440118',3),
(1938,'韶关市',1925,'440200',2),
(1939,'武江区',1938,'440203',3),
(1940,'浈江区',1938,'440204',3),
(1941,'曲江区',1938,'440205',3),
(1942,'始兴县',1938,'440222',3),
(1943,'仁化县',1938,'440224',3),
(1944,'翁源县',1938,'440229',3),
(1945,'乳源瑶族自治县',1938,'440232',3),
(1946,'新丰县',1938,'440233',3),
(1947,'乐昌市',1938,'440281',3),
(1948,'南雄市',1938,'440282',3),
(1949,'深圳市',1925,'440300',2),
(1950,'罗湖区',1949,'440303',3),
(1951,'福田区',1949,'440304',3),
(1952,'南山区',1949,'440305',3),
(1953,'宝安区',1949,'440306',3),
(1954,'龙岗区',1949,'440307',3),
(1955,'盐田区',1949,'440308',3),
(1956,'龙华区',1949,'440309',3),
(1957,'坪山区',1949,'440310',3),
(1958,'光明区',1949,'440311',3),
(1959,'珠海市',1925,'440400',2),
(1960,'香洲区',1959,'440402',3),
(1961,'斗门区',1959,'440403',3),
(1962,'金湾区',1959,'440404',3),
(1963,'汕头市',1925,'440500',2),
(1964,'龙湖区',1963,'440507',3),
(1965,'金平区',1963,'440511',3),
(1966,'濠江区',1963,'440512',3),
(1967,'潮阳区',1963,'440513',3),
(1968,'潮南区',1963,'440514',3),
(1969,'澄海区',1963,'440515',3),
(1970,'南澳县',1963,'440523',3),
(1971,'佛山市',1925,'440600',2),
(1972,'禅城区',1971,'440604',3),
(1973,'南海区',1971,'440605',3),
(1974,'顺德区',1971,'440606',3),
(1975,'三水区',1971,'440607',3),
(1976,'高明区',1971,'440608',3),
(1977,'江门市',1925,'440700',2),
(1978,'蓬江区',1977,'440703',3),
(1979,'江海区',1977,'440704',3),
(1980,'新会区',1977,'440705',3),
(1981,'台山市',1977,'440781',3),
(1982,'开平市',1977,'440783',3),
(1983,'鹤山市',1977,'440784',3),
(1984,'恩平市',1977,'440785',3),
(1985,'湛江市',1925,'440800',2),
(1986,'赤坎区',1985,'440802',3),
(1987,'霞山区',1985,'440803',3),
(1988,'坡头区',1985,'440804',3),
(1989,'麻章区',1985,'440811',3),
(1990,'遂溪县',1985,'440823',3),
(1991,'徐闻县',1985,'440825',3),
(1992,'廉江市',1985,'440881',3),
(1993,'雷州市',1985,'440882',3),
(1994,'吴川市',1985,'440883',3),
(1995,'茂名市',1925,'440900',2),
(1996,'茂南区',1995,'440902',3),
(1997,'电白区',1995,'440904',3),
(1998,'高州市',1995,'440981',3),
(1999,'化州市',1995,'440982',3),
(2000,'信宜市',1995,'440983',3),
(2001,'肇庆市',1925,'441200',2),
(2002,'端州区',2001,'441202',3),
(2003,'鼎湖区',2001,'441203',3),
(2004,'高要区',2001,'441204',3),
(2005,'广宁县',2001,'441223',3),
(2006,'怀集县',2001,'441224',3),
(2007,'封开县',2001,'441225',3),
(2008,'德庆县',2001,'441226',3),
(2009,'四会市',2001,'441284',3),
(2010,'惠州市',1925,'441300',2),
(2011,'惠城区',2010,'441302',3),
(2012,'惠阳区',2010,'441303',3),
(2013,'博罗县',2010,'441322',3),
(2014,'惠东县',2010,'441323',3),
(2015,'龙门县',2010,'441324',3),
(2016,'梅州市',1925,'441400',2),
(2017,'梅江区',2016,'441402',3),
(2018,'梅县区',2016,'441403',3),
(2019,'大埔县',2016,'441422',3),
(2020,'丰顺县',2016,'441423',3),
(2021,'五华县',2016,'441424',3),
(2022,'平远县',2016,'441426',3),
(2023,'蕉岭县',2016,'441427',3),
(2024,'兴宁市',2016,'441481',3),
(2025,'汕尾市',1925,'441500',2),
(2026,'城区',2025,'441502',3),
(2027,'海丰县',2025,'441521',3),
(2028,'陆河县',2025,'441523',3),
(2029,'陆丰市',2025,'441581',3),
(2030,'河源市',1925,'441600',2),
(2031,'源城区',2030,'441602',3),
(2032,'紫金县',2030,'441621',3),
(2033,'龙川县',2030,'441622',3),
(2034,'连平县',2030,'441623',3),
(2035,'和平县',2030,'441624',3),
(2036,'东源县',2030,'441625',3),
(2037,'阳江市',1925,'441700',2),
(2038,'江城区',2037,'441702',3),
(2039,'阳东区',2037,'441704',3),
(2040,'阳西县',2037,'441721',3),
(2041,'阳春市',2037,'441781',3),
(2042,'清远市',1925,'441800',2),
(2043,'清城区',2042,'441802',3),
(2044,'清新区',2042,'441803',3),
(2045,'佛冈县',2042,'441821',3),
(2046,'阳山县',2042,'441823',3),
(2047,'连山壮族瑶族自治县',2042,'441825',3),
(2048,'连南瑶族自治县',2042,'441826',3),
(2049,'英德市',2042,'441881',3),
(2050,'连州市',2042,'441882',3),
(2051,'东莞市',1925,'441900',2),
(2052,'中山市',1925,'442000',2),
(2053,'潮州市',1925,'445100',2),
(2054,'湘桥区',2053,'445102',3),
(2055,'潮安区',2053,'445103',3),
(2056,'饶平县',2053,'445122',3),
(2057,'揭阳市',1925,'445200',2),
(2058,'榕城区',2057,'445202',3),
(2059,'揭东区',2057,'445203',3),
(2060,'揭西县',2057,'445222',3),
(2061,'惠来县',2057,'445224',3),
(2062,'普宁市',2057,'445281',3),
(2063,'云浮市',1925,'445300',2),
(2064,'云城区',2063,'445302',3),
(2065,'云安区',2063,'445303',3),
(2066,'新兴县',2063,'445321',3),
(2067,'郁南县',2063,'445322',3),
(2068,'罗定市',2063,'445381',3),
(2069,'广西壮族自治区',0,'450000',1),
(2070,'南宁市',2069,'450100',2),
(2071,'兴宁区',2070,'450102',3),
(2072,'青秀区',2070,'450103',3),
(2073,'江南区',2070,'450105',3),
(2074,'西乡塘区',2070,'450107',3),
(2075,'良庆区',2070,'450108',3),
(2076,'邕宁区',2070,'450109',3),
(2077,'武鸣区',2070,'450110',3),
(2078,'隆安县',2070,'450123',3),
(2079,'马山县',2070,'450124',3),
(2080,'上林县',2070,'450125',3),
(2081,'宾阳县',2070,'450126',3),
(2082,'横县',2070,'450127',3),
(2083,'柳州市',2069,'450200',2),
(2084,'城中区',2083,'450202',3),
(2085,'鱼峰区',2083,'450203',3),
(2086,'柳南区',2083,'450204',3),
(2087,'柳北区',2083,'450205',3),
(2088,'柳江区',2083,'450206',3),
(2089,'柳城县',2083,'450222',3),
(2090,'鹿寨县',2083,'450223',3),
(2091,'融安县',2083,'450224',3),
(2092,'融水苗族自治县',2083,'450225',3),
(2093,'三江侗族自治县',2083,'450226',3),
(2094,'桂林市',2069,'450300',2),
(2095,'秀峰区',2094,'450302',3),
(2096,'叠彩区',2094,'450303',3),
(2097,'象山区',2094,'450304',3),
(2098,'七星区',2094,'450305',3),
(2099,'雁山区',2094,'450311',3),
(2100,'临桂区',2094,'450312',3),
(2101,'阳朔县',2094,'450321',3),
(2102,'灵川县',2094,'450323',3),
(2103,'全州县',2094,'450324',3),
(2104,'兴安县',2094,'450325',3),
(2105,'永福县',2094,'450326',3),
(2106,'灌阳县',2094,'450327',3),
(2107,'龙胜各族自治县',2094,'450328',3),
(2108,'资源县',2094,'450329',3),
(2109,'平乐县',2094,'450330',3),
(2110,'荔浦市',2094,'450381',3),
(2111,'恭城瑶族自治县',2094,'450332',3),
(2112,'梧州市',2069,'450400',2),
(2113,'万秀区',2112,'450403',3),
(2114,'长洲区',2112,'450405',3),
(2115,'龙圩区',2112,'450406',3),
(2116,'苍梧县',2112,'450421',3),
(2117,'藤县',2112,'450422',3),
(2118,'蒙山县',2112,'450423',3),
(2119,'岑溪市',2112,'450481',3),
(2120,'北海市',2069,'450500',2),
(2121,'海城区',2120,'450502',3),
(2122,'银海区',2120,'450503',3),
(2123,'铁山港区',2120,'450512',3),
(2124,'合浦县',2120,'450521',3),
(2125,'防城港市',2069,'450600',2),
(2126,'港口区',2125,'450602',3),
(2127,'防城区',2125,'450603',3),
(2128,'上思县',2125,'450621',3),
(2129,'东兴市',2125,'450681',3),
(2130,'钦州市',2069,'450700',2),
(2131,'钦南区',2130,'450702',3),
(2132,'钦北区',2130,'450703',3),
(2133,'灵山县',2130,'450721',3),
(2134,'浦北县',2130,'450722',3),
(2135,'贵港市',2069,'450800',2),
(2136,'港北区',2135,'450802',3),
(2137,'港南区',2135,'450803',3),
(2138,'覃塘区',2135,'450804',3),
(2139,'平南县',2135,'450821',3),
(2140,'桂平市',2135,'450881',3),
(2141,'玉林市',2069,'450900',2),
(2142,'玉州区',2141,'450902',3),
(2143,'福绵区',2141,'450903',3),
(2144,'容县',2141,'450921',3),
(2145,'陆川县',2141,'450922',3),
(2146,'博白县',2141,'450923',3),
(2147,'兴业县',2141,'450924',3),
(2148,'北流市',2141,'450981',3),
(2149,'百色市',2069,'451000',2),
(2150,'右江区',2149,'451002',3),
(2151,'田阳区',2149,'451003',3),
(2152,'田东县',2149,'451022',3),
(2153,'德保县',2149,'451024',3),
(2154,'那坡县',2149,'451026',3),
(2155,'凌云县',2149,'451027',3),
(2156,'乐业县',2149,'451028',3),
(2157,'田林县',2149,'451029',3),
(2158,'西林县',2149,'451030',3),
(2159,'隆林各族自治县',2149,'451031',3),
(2160,'靖西市',2149,'451081',3),
(2161,'平果市',2149,'451082',3),
(2162,'贺州市',2069,'451100',2),
(2163,'八步区',2162,'451102',3),
(2164,'平桂区',2162,'451103',3),
(2165,'昭平县',2162,'451121',3),
(2166,'钟山县',2162,'451122',3),
(2167,'富川瑶族自治县',2162,'451123',3),
(2168,'河池市',2069,'451200',2),
(2169,'金城江区',2168,'451202',3),
(2170,'宜州区',2168,'451203',3),
(2171,'南丹县',2168,'451221',3),
(2172,'天峨县',2168,'451222',3),
(2173,'凤山县',2168,'451223',3),
(2174,'东兰县',2168,'451224',3),
(2175,'罗城仫佬族自治县',2168,'451225',3),
(2176,'环江毛南族自治县',2168,'451226',3),
(2177,'巴马瑶族自治县',2168,'451227',3),
(2178,'都安瑶族自治县',2168,'451228',3),
(2179,'大化瑶族自治县',2168,'451229',3),
(2180,'来宾市',2069,'451300',2),
(2181,'兴宾区',2180,'451302',3),
(2182,'忻城县',2180,'451321',3),
(2183,'象州县',2180,'451322',3),
(2184,'武宣县',2180,'451323',3),
(2185,'金秀瑶族自治县',2180,'451324',3),
(2186,'合山市',2180,'451381',3),
(2187,'崇左市',2069,'451400',2),
(2188,'江州区',2187,'451402',3),
(2189,'扶绥县',2187,'451421',3),
(2190,'宁明县',2187,'451422',3),
(2191,'龙州县',2187,'451423',3),
(2192,'大新县',2187,'451424',3),
(2193,'天等县',2187,'451425',3),
(2194,'凭祥市',2187,'451481',3),
(2195,'海南省',0,'460000',1),
(2196,'海口市',2195,'460100',2),
(2197,'秀英区',2196,'460105',3),
(2198,'龙华区',2196,'460106',3),
(2199,'琼山区',2196,'460107',3),
(2200,'美兰区',2196,'460108',3),
(2201,'三亚市',2195,'460200',2),
(2202,'海棠区',2201,'460202',3),
(2203,'吉阳区',2201,'460203',3),
(2204,'天涯区',2201,'460204',3),
(2205,'崖州区',2201,'460205',3),
(2206,'三沙市',2195,'460300',2),
(2207,'儋州市',2195,'460400',2),
(2208,'五指山市',2195,'469001',3),
(2209,'琼海市',2195,'469002',3),
(2210,'文昌市',2195,'469005',3),
(2211,'万宁市',2195,'469006',3),
(2212,'东方市',2195,'469007',3),
(2213,'定安县',2195,'469021',3),
(2214,'屯昌县',2195,'469022',3),
(2215,'澄迈县',2195,'469023',3),
(2216,'临高县',2195,'469024',3),
(2217,'白沙黎族自治县',2195,'469025',3),
(2218,'昌江黎族自治县',2195,'469026',3),
(2219,'乐东黎族自治县',2195,'469027',3),
(2220,'陵水黎族自治县',2195,'469028',3),
(2221,'保亭黎族苗族自治县',2195,'469029',3),
(2222,'琼中黎族苗族自治县',2195,'469030',3),
(2223,'重庆',0,'500000',1),
(2224,'重庆市',2223,'500010',2),
(2225,'万州区',2224,'500101',3),
(2226,'涪陵区',2224,'500102',3),
(2227,'渝中区',2224,'500103',3),
(2228,'大渡口区',2224,'500104',3),
(2229,'江北区',2224,'500105',3),
(2230,'沙坪坝区',2224,'500106',3),
(2231,'九龙坡区',2224,'500107',3),
(2232,'南岸区',2224,'500108',3),
(2233,'北碚区',2224,'500109',3),
(2234,'綦江区',2224,'500110',3),
(2235,'大足区',2224,'500111',3),
(2236,'渝北区',2224,'500112',3),
(2237,'巴南区',2224,'500113',3),
(2238,'黔江区',2224,'500114',3),
(2239,'长寿区',2224,'500115',3),
(2240,'江津区',2224,'500116',3),
(2241,'合川区',2224,'500117',3),
(2242,'永川区',2224,'500118',3),
(2243,'南川区',2224,'500119',3),
(2244,'璧山区',2224,'500120',3),
(2245,'铜梁区',2224,'500151',3),
(2246,'潼南区',2224,'500152',3),
(2247,'荣昌区',2224,'500153',3),
(2248,'开州区',2224,'500154',3),
(2249,'梁平区',2224,'500155',3),
(2250,'武隆区',2224,'500156',3),
(2251,'城口县',2224,'500229',3),
(2252,'丰都县',2224,'500230',3),
(2253,'垫江县',2224,'500231',3),
(2254,'忠县',2224,'500233',3),
(2255,'云阳县',2224,'500235',3),
(2256,'奉节县',2224,'500236',3),
(2257,'巫山县',2224,'500237',3),
(2258,'巫溪县',2224,'500238',3),
(2259,'石柱土家族自治县',2224,'500240',3),
(2260,'秀山土家族苗族自治县',2224,'500241',3),
(2261,'酉阳土家族苗族自治县',2224,'500242',3),
(2262,'彭水苗族土家族自治县',2224,'500243',3),
(2263,'四川省',0,'510000',1),
(2264,'成都市',2263,'510100',2),
(2265,'锦江区',2264,'510104',3),
(2266,'青羊区',2264,'510105',3),
(2267,'金牛区',2264,'510106',3),
(2268,'武侯区',2264,'510107',3),
(2269,'成华区',2264,'510108',3),
(2270,'龙泉驿区',2264,'510112',3),
(2271,'青白江区',2264,'510113',3),
(2272,'新都区',2264,'510114',3),
(2273,'温江区',2264,'510115',3),
(2274,'双流区',2264,'510116',3),
(2275,'郫都区',2264,'510117',3),
(2276,'金堂县',2264,'510121',3),
(2277,'大邑县',2264,'510129',3),
(2278,'蒲江县',2264,'510131',3),
(2279,'新津县',2264,'510132',3),
(2280,'都江堰市',2264,'510181',3),
(2281,'彭州市',2264,'510182',3),
(2282,'邛崃市',2264,'510183',3),
(2283,'崇州市',2264,'510184',3),
(2284,'简阳市',2264,'510185',3),
(2285,'自贡市',2263,'510300',2),
(2286,'自流井区',2285,'510302',3),
(2287,'贡井区',2285,'510303',3),
(2288,'大安区',2285,'510304',3),
(2289,'沿滩区',2285,'510311',3),
(2290,'荣县',2285,'510321',3),
(2291,'富顺县',2285,'510322',3),
(2292,'攀枝花市',2263,'510400',2),
(2293,'东区',2292,'510402',3),
(2294,'西区',2292,'510403',3),
(2295,'仁和区',2292,'510411',3),
(2296,'米易县',2292,'510421',3),
(2297,'盐边县',2292,'510422',3),
(2298,'泸州市',2263,'510500',2),
(2299,'江阳区',2298,'510502',3),
(2300,'纳溪区',2298,'510503',3),
(2301,'龙马潭区',2298,'510504',3),
(2302,'泸县',2298,'510521',3),
(2303,'合江县',2298,'510522',3),
(2304,'叙永县',2298,'510524',3),
(2305,'古蔺县',2298,'510525',3),
(2306,'德阳市',2263,'510600',2),
(2307,'旌阳区',2306,'510603',3),
(2308,'罗江区',2306,'510604',3),
(2309,'中江县',2306,'510623',3),
(2310,'广汉市',2306,'510681',3),
(2311,'什邡市',2306,'510682',3),
(2312,'绵竹市',2306,'510683',3),
(2313,'绵阳市',2263,'510700',2),
(2314,'涪城区',2313,'510703',3),
(2315,'游仙区',2313,'510704',3),
(2316,'安州区',2313,'510705',3),
(2317,'三台县',2313,'510722',3),
(2318,'盐亭县',2313,'510723',3),
(2319,'梓潼县',2313,'510725',3),
(2320,'北川羌族自治县',2313,'510726',3),
(2321,'平武县',2313,'510727',3),
(2322,'江油市',2313,'510781',3),
(2323,'广元市',2263,'510800',2),
(2324,'利州区',2323,'510802',3),
(2325,'昭化区',2323,'510811',3),
(2326,'朝天区',2323,'510812',3),
(2327,'旺苍县',2323,'510821',3),
(2328,'青川县',2323,'510822',3),
(2329,'剑阁县',2323,'510823',3),
(2330,'苍溪县',2323,'510824',3),
(2331,'遂宁市',2263,'510900',2),
(2332,'船山区',2331,'510903',3),
(2333,'安居区',2331,'510904',3),
(2334,'蓬溪县',2331,'510921',3),
(2335,'大英县',2331,'510923',3),
(2336,'射洪市',2331,'510981',3),
(2337,'内江市',2263,'511000',2),
(2338,'市中区',2337,'511002',3),
(2339,'东兴区',2337,'511011',3),
(2340,'威远县',2337,'511024',3),
(2341,'资中县',2337,'511025',3),
(2342,'隆昌市',2337,'511083',3),
(2343,'乐山市',2263,'511100',2),
(2344,'市中区',2343,'511102',3),
(2345,'沙湾区',2343,'511111',3),
(2346,'五通桥区',2343,'511112',3),
(2347,'金口河区',2343,'511113',3),
(2348,'犍为县',2343,'511123',3),
(2349,'井研县',2343,'511124',3),
(2350,'夹江县',2343,'511126',3),
(2351,'沐川县',2343,'511129',3),
(2352,'峨边彝族自治县',2343,'511132',3),
(2353,'马边彝族自治县',2343,'511133',3),
(2354,'峨眉山市',2343,'511181',3),
(2355,'南充市',2263,'511300',2),
(2356,'顺庆区',2355,'511302',3),
(2357,'高坪区',2355,'511303',3),
(2358,'嘉陵区',2355,'511304',3),
(2359,'南部县',2355,'511321',3),
(2360,'营山县',2355,'511322',3),
(2361,'蓬安县',2355,'511323',3),
(2362,'仪陇县',2355,'511324',3),
(2363,'西充县',2355,'511325',3),
(2364,'阆中市',2355,'511381',3),
(2365,'眉山市',2263,'511400',2),
(2366,'东坡区',2365,'511402',3),
(2367,'彭山区',2365,'511403',3),
(2368,'仁寿县',2365,'511421',3),
(2369,'洪雅县',2365,'511423',3),
(2370,'丹棱县',2365,'511424',3),
(2371,'青神县',2365,'511425',3),
(2372,'宜宾市',2263,'511500',2),
(2373,'翠屏区',2372,'511502',3),
(2374,'南溪区',2372,'511503',3),
(2375,'叙州区',2372,'511504',3),
(2376,'江安县',2372,'511523',3),
(2377,'长宁县',2372,'511524',3),
(2378,'高县',2372,'511525',3),
(2379,'珙县',2372,'511526',3),
(2380,'筠连县',2372,'511527',3),
(2381,'兴文县',2372,'511528',3),
(2382,'屏山县',2372,'511529',3),
(2383,'广安市',2263,'511600',2),
(2384,'广安区',2383,'511602',3),
(2385,'前锋区',2383,'511603',3),
(2386,'岳池县',2383,'511621',3),
(2387,'武胜县',2383,'511622',3),
(2388,'邻水县',2383,'511623',3),
(2389,'华蓥市',2383,'511681',3),
(2390,'达州市',2263,'511700',2),
(2391,'通川区',2390,'511702',3),
(2392,'达川区',2390,'511703',3),
(2393,'宣汉县',2390,'511722',3),
(2394,'开江县',2390,'511723',3),
(2395,'大竹县',2390,'511724',3),
(2396,'渠县',2390,'511725',3),
(2397,'万源市',2390,'511781',3),
(2398,'雅安市',2263,'511800',2),
(2399,'雨城区',2398,'511802',3),
(2400,'名山区',2398,'511803',3),
(2401,'荥经县',2398,'511822',3),
(2402,'汉源县',2398,'511823',3),
(2403,'石棉县',2398,'511824',3),
(2404,'天全县',2398,'511825',3),
(2405,'芦山县',2398,'511826',3),
(2406,'宝兴县',2398,'511827',3),
(2407,'巴中市',2263,'511900',2),
(2408,'巴州区',2407,'511902',3),
(2409,'恩阳区',2407,'511903',3),
(2410,'通江县',2407,'511921',3),
(2411,'南江县',2407,'511922',3),
(2412,'平昌县',2407,'511923',3),
(2413,'资阳市',2263,'512000',2),
(2414,'雁江区',2413,'512002',3),
(2415,'安岳县',2413,'512021',3),
(2416,'乐至县',2413,'512022',3),
(2417,'阿坝藏族羌族自治州',2263,'513200',2),
(2418,'马尔康市',2417,'513201',3),
(2419,'汶川县',2417,'513221',3),
(2420,'理县',2417,'513222',3),
(2421,'茂县',2417,'513223',3),
(2422,'松潘县',2417,'513224',3),
(2423,'九寨沟县',2417,'513225',3),
(2424,'金川县',2417,'513226',3),
(2425,'小金县',2417,'513227',3),
(2426,'黑水县',2417,'513228',3),
(2427,'壤塘县',2417,'513230',3),
(2428,'阿坝县',2417,'513231',3),
(2429,'若尔盖县',2417,'513232',3),
(2430,'红原县',2417,'513233',3),
(2431,'甘孜藏族自治州',2263,'513300',2),
(2432,'康定市',2431,'513301',3),
(2433,'泸定县',2431,'513322',3),
(2434,'丹巴县',2431,'513323',3),
(2435,'九龙县',2431,'513324',3),
(2436,'雅江县',2431,'513325',3),
(2437,'道孚县',2431,'513326',3),
(2438,'炉霍县',2431,'513327',3),
(2439,'甘孜县',2431,'513328',3),
(2440,'新龙县',2431,'513329',3),
(2441,'德格县',2431,'513330',3),
(2442,'白玉县',2431,'513331',3),
(2443,'石渠县',2431,'513332',3),
(2444,'色达县',2431,'513333',3),
(2445,'理塘县',2431,'513334',3),
(2446,'巴塘县',2431,'513335',3),
(2447,'乡城县',2431,'513336',3),
(2448,'稻城县',2431,'513337',3),
(2449,'得荣县',2431,'513338',3),
(2450,'凉山彝族自治州',2263,'513400',2),
(2451,'西昌市',2450,'513401',3),
(2452,'木里藏族自治县',2450,'513422',3),
(2453,'盐源县',2450,'513423',3),
(2454,'德昌县',2450,'513424',3),
(2455,'会理县',2450,'513425',3),
(2456,'会东县',2450,'513426',3),
(2457,'宁南县',2450,'513427',3),
(2458,'普格县',2450,'513428',3),
(2459,'布拖县',2450,'513429',3),
(2460,'金阳县',2450,'513430',3),
(2461,'昭觉县',2450,'513431',3),
(2462,'喜德县',2450,'513432',3),
(2463,'冕宁县',2450,'513433',3),
(2464,'越西县',2450,'513434',3),
(2465,'甘洛县',2450,'513435',3),
(2466,'美姑县',2450,'513436',3),
(2467,'雷波县',2450,'513437',3),
(2468,'贵州省',0,'520000',1),
(2469,'贵阳市',2468,'520100',2),
(2470,'南明区',2469,'520102',3),
(2471,'云岩区',2469,'520103',3),
(2472,'花溪区',2469,'520111',3),
(2473,'乌当区',2469,'520112',3),
(2474,'白云区',2469,'520113',3),
(2475,'观山湖区',2469,'520115',3),
(2476,'开阳县',2469,'520121',3),
(2477,'息烽县',2469,'520122',3),
(2478,'修文县',2469,'520123',3),
(2479,'清镇市',2469,'520181',3),
(2480,'六盘水市',2468,'520200',2),
(2481,'钟山区',2480,'520201',3),
(2482,'六枝特区',2480,'520203',3),
(2483,'水城县',2480,'520221',3),
(2484,'盘州市',2480,'520281',3),
(2485,'遵义市',2468,'520300',2),
(2486,'红花岗区',2485,'520302',3),
(2487,'汇川区',2485,'520303',3),
(2488,'播州区',2485,'520304',3),
(2489,'桐梓县',2485,'520322',3),
(2490,'绥阳县',2485,'520323',3),
(2491,'正安县',2485,'520324',3),
(2492,'道真仡佬族苗族自治县',2485,'520325',3),
(2493,'务川仡佬族苗族自治县',2485,'520326',3),
(2494,'凤冈县',2485,'520327',3),
(2495,'湄潭县',2485,'520328',3),
(2496,'余庆县',2485,'520329',3),
(2497,'习水县',2485,'520330',3),
(2498,'赤水市',2485,'520381',3),
(2499,'仁怀市',2485,'520382',3),
(2500,'安顺市',2468,'520400',2),
(2501,'西秀区',2500,'520402',3),
(2502,'平坝区',2500,'520403',3),
(2503,'普定县',2500,'520422',3),
(2504,'镇宁布依族苗族自治县',2500,'520423',3),
(2505,'关岭布依族苗族自治县',2500,'520424',3),
(2506,'紫云苗族布依族自治县',2500,'520425',3),
(2507,'毕节市',2468,'520500',2),
(2508,'七星关区',2507,'520502',3),
(2509,'大方县',2507,'520521',3),
(2510,'黔西县',2507,'520522',3),
(2511,'金沙县',2507,'520523',3),
(2512,'织金县',2507,'520524',3),
(2513,'纳雍县',2507,'520525',3),
(2514,'威宁彝族回族苗族自治县',2507,'520526',3),
(2515,'赫章县',2507,'520527',3),
(2516,'铜仁市',2468,'520600',2),
(2517,'碧江区',2516,'520602',3),
(2518,'万山区',2516,'520603',3),
(2519,'江口县',2516,'520621',3),
(2520,'玉屏侗族自治县',2516,'520622',3),
(2521,'石阡县',2516,'520623',3),
(2522,'思南县',2516,'520624',3),
(2523,'印江土家族苗族自治县',2516,'520625',3),
(2524,'德江县',2516,'520626',3),
(2525,'沿河土家族自治县',2516,'520627',3),
(2526,'松桃苗族自治县',2516,'520628',3),
(2527,'黔西南布依族苗族自治州',2468,'522300',2),
(2528,'兴义市',2527,'522301',3),
(2529,'兴仁市',2527,'522302',3),
(2530,'普安县',2527,'522323',3),
(2531,'晴隆县',2527,'522324',3),
(2532,'贞丰县',2527,'522325',3),
(2533,'望谟县',2527,'522326',3),
(2534,'册亨县',2527,'522327',3),
(2535,'安龙县',2527,'522328',3),
(2536,'黔东南苗族侗族自治州',2468,'522600',2),
(2537,'凯里市',2536,'522601',3),
(2538,'黄平县',2536,'522622',3),
(2539,'施秉县',2536,'522623',3),
(2540,'三穗县',2536,'522624',3),
(2541,'镇远县',2536,'522625',3),
(2542,'岑巩县',2536,'522626',3),
(2543,'天柱县',2536,'522627',3),
(2544,'锦屏县',2536,'522628',3),
(2545,'剑河县',2536,'522629',3),
(2546,'台江县',2536,'522630',3),
(2547,'黎平县',2536,'522631',3),
(2548,'榕江县',2536,'522632',3),
(2549,'从江县',2536,'522633',3),
(2550,'雷山县',2536,'522634',3),
(2551,'麻江县',2536,'522635',3),
(2552,'丹寨县',2536,'522636',3),
(2553,'黔南布依族苗族自治州',2468,'522700',2),
(2554,'都匀市',2553,'522701',3),
(2555,'福泉市',2553,'522702',3),
(2556,'荔波县',2553,'522722',3),
(2557,'贵定县',2553,'522723',3),
(2558,'瓮安县',2553,'522725',3),
(2559,'独山县',2553,'522726',3),
(2560,'平塘县',2553,'522727',3),
(2561,'罗甸县',2553,'522728',3),
(2562,'长顺县',2553,'522729',3),
(2563,'龙里县',2553,'522730',3),
(2564,'惠水县',2553,'522731',3),
(2565,'三都水族自治县',2553,'522732',3),
(2566,'云南省',0,'530000',1),
(2567,'昆明市',2566,'530100',2),
(2568,'五华区',2567,'530102',3),
(2569,'盘龙区',2567,'530103',3),
(2570,'官渡区',2567,'530111',3),
(2571,'西山区',2567,'530112',3),
(2572,'东川区',2567,'530113',3),
(2573,'呈贡区',2567,'530114',3),
(2574,'晋宁区',2567,'530115',3),
(2575,'富民县',2567,'530124',3),
(2576,'宜良县',2567,'530125',3),
(2577,'石林彝族自治县',2567,'530126',3),
(2578,'嵩明县',2567,'530127',3),
(2579,'禄劝彝族苗族自治县',2567,'530128',3),
(2580,'寻甸回族彝族自治县',2567,'530129',3),
(2581,'安宁市',2567,'530181',3),
(2582,'曲靖市',2566,'530300',2),
(2583,'麒麟区',2582,'530302',3),
(2584,'沾益区',2582,'530303',3),
(2585,'马龙区',2582,'530304',3),
(2586,'陆良县',2582,'530322',3),
(2587,'师宗县',2582,'530323',3),
(2588,'罗平县',2582,'530324',3),
(2589,'富源县',2582,'530325',3),
(2590,'会泽县',2582,'530326',3),
(2591,'宣威市',2582,'530381',3),
(2592,'玉溪市',2566,'530400',2),
(2593,'红塔区',2592,'530402',3),
(2594,'江川区',2592,'530403',3),
(2595,'通海县',2592,'530423',3),
(2596,'华宁县',2592,'530424',3),
(2597,'易门县',2592,'530425',3),
(2598,'峨山彝族自治县',2592,'530426',3),
(2599,'新平彝族傣族自治县',2592,'530427',3),
(2600,'元江哈尼族彝族傣族自治县',2592,'530428',3),
(2601,'澄江市',2592,'530481',3),
(2602,'保山市',2566,'530500',2),
(2603,'隆阳区',2602,'530502',3),
(2604,'施甸县',2602,'530521',3),
(2605,'龙陵县',2602,'530523',3),
(2606,'昌宁县',2602,'530524',3),
(2607,'腾冲市',2602,'530581',3),
(2608,'昭通市',2566,'530600',2),
(2609,'昭阳区',2608,'530602',3),
(2610,'鲁甸县',2608,'530621',3),
(2611,'巧家县',2608,'530622',3),
(2612,'盐津县',2608,'530623',3),
(2613,'大关县',2608,'530624',3),
(2614,'永善县',2608,'530625',3),
(2615,'绥江县',2608,'530626',3),
(2616,'镇雄县',2608,'530627',3),
(2617,'彝良县',2608,'530628',3),
(2618,'威信县',2608,'530629',3),
(2619,'水富市',2608,'530681',3),
(2620,'丽江市',2566,'530700',2),
(2621,'古城区',2620,'530702',3),
(2622,'玉龙纳西族自治县',2620,'530721',3),
(2623,'永胜县',2620,'530722',3),
(2624,'华坪县',2620,'530723',3),
(2625,'宁蒗彝族自治县',2620,'530724',3),
(2626,'普洱市',2566,'530800',2),
(2627,'思茅区',2626,'530802',3),
(2628,'宁洱哈尼族彝族自治县',2626,'530821',3),
(2629,'墨江哈尼族自治县',2626,'530822',3),
(2630,'景东彝族自治县',2626,'530823',3),
(2631,'景谷傣族彝族自治县',2626,'530824',3),
(2632,'镇沅彝族哈尼族拉祜族自治县',2626,'530825',3),
(2633,'江城哈尼族彝族自治县',2626,'530826',3),
(2634,'孟连傣族拉祜族佤族自治县',2626,'530827',3),
(2635,'澜沧拉祜族自治县',2626,'530828',3),
(2636,'西盟佤族自治县',2626,'530829',3),
(2637,'临沧市',2566,'530900',2),
(2638,'临翔区',2637,'530902',3),
(2639,'凤庆县',2637,'530921',3),
(2640,'云县',2637,'530922',3),
(2641,'永德县',2637,'530923',3),
(2642,'镇康县',2637,'530924',3),
(2643,'双江拉祜族佤族布朗族傣族自治县',2637,'530925',3),
(2644,'耿马傣族佤族自治县',2637,'530926',3),
(2645,'沧源佤族自治县',2637,'530927',3),
(2646,'楚雄彝族自治州',2566,'532300',2),
(2647,'楚雄市',2646,'532301',3),
(2648,'双柏县',2646,'532322',3),
(2649,'牟定县',2646,'532323',3),
(2650,'南华县',2646,'532324',3),
(2651,'姚安县',2646,'532325',3),
(2652,'大姚县',2646,'532326',3),
(2653,'永仁县',2646,'532327',3),
(2654,'元谋县',2646,'532328',3),
(2655,'武定县',2646,'532329',3),
(2656,'禄丰县',2646,'532331',3),
(2657,'红河哈尼族彝族自治州',2566,'532500',2),
(2658,'个旧市',2657,'532501',3),
(2659,'开远市',2657,'532502',3),
(2660,'蒙自市',2657,'532503',3),
(2661,'弥勒市',2657,'532504',3),
(2662,'屏边苗族自治县',2657,'532523',3),
(2663,'建水县',2657,'532524',3),
(2664,'石屏县',2657,'532525',3),
(2665,'泸西县',2657,'532527',3),
(2666,'元阳县',2657,'532528',3),
(2667,'红河县',2657,'532529',3),
(2668,'金平苗族瑶族傣族自治县',2657,'532530',3),
(2669,'绿春县',2657,'532531',3),
(2670,'河口瑶族自治县',2657,'532532',3),
(2671,'文山壮族苗族自治州',2566,'532600',2),
(2672,'文山市',2671,'532601',3),
(2673,'砚山县',2671,'532622',3),
(2674,'西畴县',2671,'532623',3),
(2675,'麻栗坡县',2671,'532624',3),
(2676,'马关县',2671,'532625',3),
(2677,'丘北县',2671,'532626',3),
(2678,'广南县',2671,'532627',3),
(2679,'富宁县',2671,'532628',3),
(2680,'西双版纳傣族自治州',2566,'532800',2),
(2681,'景洪市',2680,'532801',3),
(2682,'勐海县',2680,'532822',3),
(2683,'勐腊县',2680,'532823',3),
(2684,'大理白族自治州',2566,'532900',2),
(2685,'大理市',2684,'532901',3),
(2686,'漾濞彝族自治县',2684,'532922',3),
(2687,'祥云县',2684,'532923',3),
(2688,'宾川县',2684,'532924',3),
(2689,'弥渡县',2684,'532925',3),
(2690,'南涧彝族自治县',2684,'532926',3),
(2691,'巍山彝族回族自治县',2684,'532927',3),
(2692,'永平县',2684,'532928',3),
(2693,'云龙县',2684,'532929',3),
(2694,'洱源县',2684,'532930',3),
(2695,'剑川县',2684,'532931',3),
(2696,'鹤庆县',2684,'532932',3),
(2697,'德宏傣族景颇族自治州',2566,'533100',2),
(2698,'瑞丽市',2697,'533102',3),
(2699,'芒市',2697,'533103',3),
(2700,'梁河县',2697,'533122',3),
(2701,'盈江县',2697,'533123',3),
(2702,'陇川县',2697,'533124',3),
(2703,'怒江傈僳族自治州',2566,'533300',2),
(2704,'泸水市',2703,'533301',3),
(2705,'福贡县',2703,'533323',3),
(2706,'贡山独龙族怒族自治县',2703,'533324',3),
(2707,'兰坪白族普米族自治县',2703,'533325',3),
(2708,'迪庆藏族自治州',2566,'533400',2),
(2709,'香格里拉市',2708,'533401',3),
(2710,'德钦县',2708,'533422',3),
(2711,'维西傈僳族自治县',2708,'533423',3),
(2712,'西藏自治区',0,'540000',1),
(2713,'拉萨市',2712,'540100',2),
(2714,'城关区',2713,'540102',3),
(2715,'堆龙德庆区',2713,'540103',3),
(2716,'达孜区',2713,'540104',3),
(2717,'林周县',2713,'540121',3),
(2718,'当雄县',2713,'540122',3),
(2719,'尼木县',2713,'540123',3),
(2720,'曲水县',2713,'540124',3),
(2721,'墨竹工卡县',2713,'540127',3),
(2722,'日喀则市',2712,'540200',2),
(2723,'桑珠孜区',2722,'540202',3),
(2724,'南木林县',2722,'540221',3),
(2725,'江孜县',2722,'540222',3),
(2726,'定日县',2722,'540223',3),
(2727,'萨迦县',2722,'540224',3),
(2728,'拉孜县',2722,'540225',3),
(2729,'昂仁县',2722,'540226',3),
(2730,'谢通门县',2722,'540227',3),
(2731,'白朗县',2722,'540228',3),
(2732,'仁布县',2722,'540229',3),
(2733,'康马县',2722,'540230',3),
(2734,'定结县',2722,'540231',3),
(2735,'仲巴县',2722,'540232',3),
(2736,'亚东县',2722,'540233',3),
(2737,'吉隆县',2722,'540234',3),
(2738,'聂拉木县',2722,'540235',3),
(2739,'萨嘎县',2722,'540236',3),
(2740,'岗巴县',2722,'540237',3),
(2741,'昌都市',2712,'540300',2),
(2742,'卡若区',2741,'540302',3),
(2743,'江达县',2741,'540321',3),
(2744,'贡觉县',2741,'540322',3),
(2745,'类乌齐县',2741,'540323',3),
(2746,'丁青县',2741,'540324',3),
(2747,'察雅县',2741,'540325',3),
(2748,'八宿县',2741,'540326',3),
(2749,'左贡县',2741,'540327',3),
(2750,'芒康县',2741,'540328',3),
(2751,'洛隆县',2741,'540329',3),
(2752,'边坝县',2741,'540330',3),
(2753,'林芝市',2712,'540400',2),
(2754,'巴宜区',2753,'540402',3),
(2755,'工布江达县',2753,'540421',3),
(2756,'米林县',2753,'540422',3),
(2757,'墨脱县',2753,'540423',3),
(2758,'波密县',2753,'540424',3),
(2759,'察隅县',2753,'540425',3),
(2760,'朗县',2753,'540426',3),
(2761,'山南市',2712,'540500',2),
(2762,'乃东区',2761,'540502',3),
(2763,'扎囊县',2761,'540521',3),
(2764,'贡嘎县',2761,'540522',3),
(2765,'桑日县',2761,'540523',3),
(2766,'琼结县',2761,'540524',3),
(2767,'曲松县',2761,'540525',3),
(2768,'措美县',2761,'540526',3),
(2769,'洛扎县',2761,'540527',3),
(2770,'加查县',2761,'540528',3),
(2771,'隆子县',2761,'540529',3),
(2772,'错那县',2761,'540530',3),
(2773,'浪卡子县',2761,'540531',3),
(2774,'那曲市',2712,'540600',2),
(2775,'色尼区',2774,'540602',3),
(2776,'嘉黎县',2774,'540621',3),
(2777,'比如县',2774,'540622',3),
(2778,'聂荣县',2774,'540623',3),
(2779,'安多县',2774,'540624',3),
(2780,'申扎县',2774,'540625',3),
(2781,'索县',2774,'540626',3),
(2782,'班戈县',2774,'540627',3),
(2783,'巴青县',2774,'540628',3),
(2784,'尼玛县',2774,'540629',3),
(2785,'双湖县',2774,'540630',3),
(2786,'阿里地区',2712,'542500',2),
(2787,'普兰县',2786,'542521',3),
(2788,'札达县',2786,'542522',3),
(2789,'噶尔县',2786,'542523',3),
(2790,'日土县',2786,'542524',3),
(2791,'革吉县',2786,'542525',3),
(2792,'改则县',2786,'542526',3),
(2793,'措勤县',2786,'542527',3),
(2794,'陕西省',0,'610000',1),
(2795,'西安市',2794,'610100',2),
(2796,'新城区',2795,'610102',3),
(2797,'碑林区',2795,'610103',3),
(2798,'莲湖区',2795,'610104',3),
(2799,'灞桥区',2795,'610111',3),
(2800,'未央区',2795,'610112',3),
(2801,'雁塔区',2795,'610113',3),
(2802,'阎良区',2795,'610114',3),
(2803,'临潼区',2795,'610115',3),
(2804,'长安区',2795,'610116',3),
(2805,'高陵区',2795,'610117',3),
(2806,'鄠邑区',2795,'610118',3),
(2807,'蓝田县',2795,'610122',3),
(2808,'周至县',2795,'610124',3),
(2809,'铜川市',2794,'610200',2),
(2810,'王益区',2809,'610202',3),
(2811,'印台区',2809,'610203',3),
(2812,'耀州区',2809,'610204',3),
(2813,'宜君县',2809,'610222',3),
(2814,'宝鸡市',2794,'610300',2),
(2815,'渭滨区',2814,'610302',3),
(2816,'金台区',2814,'610303',3),
(2817,'陈仓区',2814,'610304',3),
(2818,'凤翔县',2814,'610322',3),
(2819,'岐山县',2814,'610323',3),
(2820,'扶风县',2814,'610324',3),
(2821,'眉县',2814,'610326',3),
(2822,'陇县',2814,'610327',3),
(2823,'千阳县',2814,'610328',3),
(2824,'麟游县',2814,'610329',3),
(2825,'凤县',2814,'610330',3),
(2826,'太白县',2814,'610331',3),
(2827,'咸阳市',2794,'610400',2),
(2828,'秦都区',2827,'610402',3),
(2829,'杨陵区',2827,'610403',3),
(2830,'渭城区',2827,'610404',3),
(2831,'三原县',2827,'610422',3),
(2832,'泾阳县',2827,'610423',3),
(2833,'乾县',2827,'610424',3),
(2834,'礼泉县',2827,'610425',3),
(2835,'永寿县',2827,'610426',3),
(2836,'长武县',2827,'610428',3),
(2837,'旬邑县',2827,'610429',3),
(2838,'淳化县',2827,'610430',3),
(2839,'武功县',2827,'610431',3),
(2840,'兴平市',2827,'610481',3),
(2841,'彬州市',2827,'610482',3),
(2842,'渭南市',2794,'610500',2),
(2843,'临渭区',2842,'610502',3),
(2844,'华州区',2842,'610503',3),
(2845,'潼关县',2842,'610522',3),
(2846,'大荔县',2842,'610523',3),
(2847,'合阳县',2842,'610524',3),
(2848,'澄城县',2842,'610525',3),
(2849,'蒲城县',2842,'610526',3),
(2850,'白水县',2842,'610527',3),
(2851,'富平县',2842,'610528',3),
(2852,'韩城市',2842,'610581',3),
(2853,'华阴市',2842,'610582',3),
(2854,'延安市',2794,'610600',2),
(2855,'宝塔区',2854,'610602',3),
(2856,'安塞区',2854,'610603',3),
(2857,'延长县',2854,'610621',3),
(2858,'延川县',2854,'610622',3),
(2859,'志丹县',2854,'610625',3),
(2860,'吴起县',2854,'610626',3),
(2861,'甘泉县',2854,'610627',3),
(2862,'富县',2854,'610628',3),
(2863,'洛川县',2854,'610629',3),
(2864,'宜川县',2854,'610630',3),
(2865,'黄龙县',2854,'610631',3),
(2866,'黄陵县',2854,'610632',3),
(2867,'子长市',2854,'610681',3),
(2868,'汉中市',2794,'610700',2),
(2869,'汉台区',2868,'610702',3),
(2870,'南郑区',2868,'610703',3),
(2871,'城固县',2868,'610722',3),
(2872,'洋县',2868,'610723',3),
(2873,'西乡县',2868,'610724',3),
(2874,'勉县',2868,'610725',3),
(2875,'宁强县',2868,'610726',3),
(2876,'略阳县',2868,'610727',3),
(2877,'镇巴县',2868,'610728',3),
(2878,'留坝县',2868,'610729',3),
(2879,'佛坪县',2868,'610730',3),
(2880,'榆林市',2794,'610800',2),
(2881,'榆阳区',2880,'610802',3),
(2882,'横山区',2880,'610803',3),
(2883,'府谷县',2880,'610822',3),
(2884,'靖边县',2880,'610824',3),
(2885,'定边县',2880,'610825',3),
(2886,'绥德县',2880,'610826',3),
(2887,'米脂县',2880,'610827',3),
(2888,'佳县',2880,'610828',3),
(2889,'吴堡县',2880,'610829',3),
(2890,'清涧县',2880,'610830',3),
(2891,'子洲县',2880,'610831',3),
(2892,'神木市',2880,'610881',3),
(2893,'安康市',2794,'610900',2),
(2894,'汉滨区',2893,'610902',3),
(2895,'汉阴县',2893,'610921',3),
(2896,'石泉县',2893,'610922',3),
(2897,'宁陕县',2893,'610923',3),
(2898,'紫阳县',2893,'610924',3),
(2899,'岚皋县',2893,'610925',3),
(2900,'平利县',2893,'610926',3),
(2901,'镇坪县',2893,'610927',3),
(2902,'旬阳县',2893,'610928',3),
(2903,'白河县',2893,'610929',3),
(2904,'商洛市',2794,'611000',2),
(2905,'商州区',2904,'611002',3),
(2906,'洛南县',2904,'611021',3),
(2907,'丹凤县',2904,'611022',3),
(2908,'商南县',2904,'611023',3),
(2909,'山阳县',2904,'611024',3),
(2910,'镇安县',2904,'611025',3),
(2911,'柞水县',2904,'611026',3),
(2912,'甘肃省',0,'620000',1),
(2913,'兰州市',2912,'620100',2),
(2914,'城关区',2913,'620102',3),
(2915,'七里河区',2913,'620103',3),
(2916,'西固区',2913,'620104',3),
(2917,'安宁区',2913,'620105',3),
(2918,'红古区',2913,'620111',3),
(2919,'永登县',2913,'620121',3),
(2920,'皋兰县',2913,'620122',3),
(2921,'榆中县',2913,'620123',3),
(2922,'嘉峪关市',2912,'620200',2),
(2923,'金昌市',2912,'620300',2),
(2924,'金川区',2923,'620302',3),
(2925,'永昌县',2923,'620321',3),
(2926,'白银市',2912,'620400',2),
(2927,'白银区',2926,'620402',3),
(2928,'平川区',2926,'620403',3),
(2929,'靖远县',2926,'620421',3),
(2930,'会宁县',2926,'620422',3),
(2931,'景泰县',2926,'620423',3),
(2932,'天水市',2912,'620500',2),
(2933,'秦州区',2932,'620502',3),
(2934,'麦积区',2932,'620503',3),
(2935,'清水县',2932,'620521',3),
(2936,'秦安县',2932,'620522',3),
(2937,'甘谷县',2932,'620523',3),
(2938,'武山县',2932,'620524',3),
(2939,'张家川回族自治县',2932,'620525',3),
(2940,'武威市',2912,'620600',2),
(2941,'凉州区',2940,'620602',3),
(2942,'民勤县',2940,'620621',3),
(2943,'古浪县',2940,'620622',3),
(2944,'天祝藏族自治县',2940,'620623',3),
(2945,'张掖市',2912,'620700',2),
(2946,'甘州区',2945,'620702',3),
(2947,'肃南裕固族自治县',2945,'620721',3),
(2948,'民乐县',2945,'620722',3),
(2949,'临泽县',2945,'620723',3),
(2950,'高台县',2945,'620724',3),
(2951,'山丹县',2945,'620725',3),
(2952,'平凉市',2912,'620800',2),
(2953,'崆峒区',2952,'620802',3),
(2954,'泾川县',2952,'620821',3),
(2955,'灵台县',2952,'620822',3),
(2956,'崇信县',2952,'620823',3),
(2957,'庄浪县',2952,'620825',3),
(2958,'静宁县',2952,'620826',3),
(2959,'华亭市',2952,'620881',3),
(2960,'酒泉市',2912,'620900',2),
(2961,'肃州区',2960,'620902',3),
(2962,'金塔县',2960,'620921',3),
(2963,'瓜州县',2960,'620922',3),
(2964,'肃北蒙古族自治县',2960,'620923',3),
(2965,'阿克塞哈萨克族自治县',2960,'620924',3),
(2966,'玉门市',2960,'620981',3),
(2967,'敦煌市',2960,'620982',3),
(2968,'庆阳市',2912,'621000',2),
(2969,'西峰区',2968,'621002',3),
(2970,'庆城县',2968,'621021',3),
(2971,'环县',2968,'621022',3),
(2972,'华池县',2968,'621023',3),
(2973,'合水县',2968,'621024',3),
(2974,'正宁县',2968,'621025',3),
(2975,'宁县',2968,'621026',3),
(2976,'镇原县',2968,'621027',3),
(2977,'定西市',2912,'621100',2),
(2978,'安定区',2977,'621102',3),
(2979,'通渭县',2977,'621121',3),
(2980,'陇西县',2977,'621122',3),
(2981,'渭源县',2977,'621123',3),
(2982,'临洮县',2977,'621124',3),
(2983,'漳县',2977,'621125',3),
(2984,'岷县',2977,'621126',3),
(2985,'陇南市',2912,'621200',2),
(2986,'武都区',2985,'621202',3),
(2987,'成县',2985,'621221',3),
(2988,'文县',2985,'621222',3),
(2989,'宕昌县',2985,'621223',3),
(2990,'康县',2985,'621224',3),
(2991,'西和县',2985,'621225',3),
(2992,'礼县',2985,'621226',3),
(2993,'徽县',2985,'621227',3),
(2994,'两当县',2985,'621228',3),
(2995,'临夏回族自治州',2912,'622900',2),
(2996,'临夏市',2995,'622901',3),
(2997,'临夏县',2995,'622921',3),
(2998,'康乐县',2995,'622922',3),
(2999,'永靖县',2995,'622923',3),
(3000,'广河县',2995,'622924',3),
(3001,'和政县',2995,'622925',3),
(3002,'东乡族自治县',2995,'622926',3),
(3003,'积石山保安族东乡族撒拉族自治县',2995,'622927',3),
(3004,'甘南藏族自治州',2912,'623000',2),
(3005,'合作市',3004,'623001',3),
(3006,'临潭县',3004,'623021',3),
(3007,'卓尼县',3004,'623022',3),
(3008,'舟曲县',3004,'623023',3),
(3009,'迭部县',3004,'623024',3),
(3010,'玛曲县',3004,'623025',3),
(3011,'碌曲县',3004,'623026',3),
(3012,'夏河县',3004,'623027',3),
(3013,'青海省',0,'630000',1),
(3014,'西宁市',3013,'630100',2),
(3015,'城东区',3014,'630102',3),
(3016,'城中区',3014,'630103',3),
(3017,'城西区',3014,'630104',3),
(3018,'城北区',3014,'630105',3),
(3019,'湟中区',3014,'630106',3),
(3020,'大通回族土族自治县',3014,'630121',3),
(3021,'湟源县',3014,'630123',3),
(3022,'海东市',3013,'630200',2),
(3023,'乐都区',3022,'630202',3),
(3024,'平安区',3022,'630203',3),
(3025,'民和回族土族自治县',3022,'630222',3),
(3026,'互助土族自治县',3022,'630223',3),
(3027,'化隆回族自治县',3022,'630224',3),
(3028,'循化撒拉族自治县',3022,'630225',3),
(3029,'海北藏族自治州',3013,'632200',2),
(3030,'门源回族自治县',3029,'632221',3),
(3031,'祁连县',3029,'632222',3),
(3032,'海晏县',3029,'632223',3),
(3033,'刚察县',3029,'632224',3),
(3034,'黄南藏族自治州',3013,'632300',2),
(3035,'同仁县',3034,'632321',3),
(3036,'尖扎县',3034,'632322',3),
(3037,'泽库县',3034,'632323',3),
(3038,'河南蒙古族自治县',3034,'632324',3),
(3039,'海南藏族自治州',3013,'632500',2),
(3040,'共和县',3039,'632521',3),
(3041,'同德县',3039,'632522',3),
(3042,'贵德县',3039,'632523',3),
(3043,'兴海县',3039,'632524',3),
(3044,'贵南县',3039,'632525',3),
(3045,'果洛藏族自治州',3013,'632600',2),
(3046,'玛沁县',3045,'632621',3),
(3047,'班玛县',3045,'632622',3),
(3048,'甘德县',3045,'632623',3),
(3049,'达日县',3045,'632624',3),
(3050,'久治县',3045,'632625',3),
(3051,'玛多县',3045,'632626',3),
(3052,'玉树藏族自治州',3013,'632700',2),
(3053,'玉树市',3052,'632701',3),
(3054,'杂多县',3052,'632722',3),
(3055,'称多县',3052,'632723',3),
(3056,'治多县',3052,'632724',3),
(3057,'囊谦县',3052,'632725',3),
(3058,'曲麻莱县',3052,'632726',3),
(3059,'海西蒙古族藏族自治州',3013,'632800',2),
(3060,'格尔木市',3059,'632801',3),
(3061,'德令哈市',3059,'632802',3),
(3062,'茫崖市',3059,'632803',3),
(3063,'乌兰县',3059,'632821',3),
(3064,'都兰县',3059,'632822',3),
(3065,'天峻县',3059,'632823',3),
(3066,'宁夏回族自治区',0,'640000',1),
(3067,'银川市',3066,'640100',2),
(3068,'兴庆区',3067,'640104',3),
(3069,'西夏区',3067,'640105',3),
(3070,'金凤区',3067,'640106',3),
(3071,'永宁县',3067,'640121',3),
(3072,'贺兰县',3067,'640122',3),
(3073,'灵武市',3067,'640181',3),
(3074,'石嘴山市',3066,'640200',2),
(3075,'大武口区',3074,'640202',3),
(3076,'惠农区',3074,'640205',3),
(3077,'平罗县',3074,'640221',3),
(3078,'吴忠市',3066,'640300',2),
(3079,'利通区',3078,'640302',3),
(3080,'红寺堡区',3078,'640303',3),
(3081,'盐池县',3078,'640323',3),
(3082,'同心县',3078,'640324',3),
(3083,'青铜峡市',3078,'640381',3),
(3084,'固原市',3066,'640400',2),
(3085,'原州区',3084,'640402',3),
(3086,'西吉县',3084,'640422',3),
(3087,'隆德县',3084,'640423',3),
(3088,'泾源县',3084,'640424',3),
(3089,'彭阳县',3084,'640425',3),
(3090,'中卫市',3066,'640500',2),
(3091,'沙坡头区',3090,'640502',3),
(3092,'中宁县',3090,'640521',3),
(3093,'海原县',3090,'640522',3),
(3094,'新疆维吾尔自治区',0,'650000',1),
(3095,'乌鲁木齐市',3094,'650100',2),
(3096,'天山区',3095,'650102',3),
(3097,'沙依巴克区',3095,'650103',3),
(3098,'新市区',3095,'650104',3),
(3099,'水磨沟区',3095,'650105',3),
(3100,'头屯河区',3095,'650106',3),
(3101,'达坂城区',3095,'650107',3),
(3102,'米东区',3095,'650109',3),
(3103,'乌鲁木齐县',3095,'650121',3),
(3104,'克拉玛依市',3094,'650200',2),
(3105,'独山子区',3104,'650202',3),
(3106,'克拉玛依区',3104,'650203',3),
(3107,'白碱滩区',3104,'650204',3),
(3108,'乌尔禾区',3104,'650205',3),
(3109,'吐鲁番市',3094,'650400',2),
(3110,'高昌区',3109,'650402',3),
(3111,'鄯善县',3109,'650421',3),
(3112,'托克逊县',3109,'650422',3),
(3113,'哈密市',3094,'650500',2),
(3114,'伊州区',3113,'650502',3),
(3115,'巴里坤哈萨克自治县',3113,'650521',3),
(3116,'伊吾县',3113,'650522',3),
(3117,'昌吉回族自治州',3094,'652300',2),
(3118,'昌吉市',3117,'652301',3),
(3119,'阜康市',3117,'652302',3),
(3120,'呼图壁县',3117,'652323',3),
(3121,'玛纳斯县',3117,'652324',3),
(3122,'奇台县',3117,'652325',3),
(3123,'吉木萨尔县',3117,'652327',3),
(3124,'木垒哈萨克自治县',3117,'652328',3),
(3125,'博尔塔拉蒙古自治州',3094,'652700',2),
(3126,'博乐市',3125,'652701',3),
(3127,'阿拉山口市',3125,'652702',3),
(3128,'精河县',3125,'652722',3),
(3129,'温泉县',3125,'652723',3),
(3130,'巴音郭楞蒙古自治州',3094,'652800',2),
(3131,'库尔勒市',3130,'652801',3),
(3132,'轮台县',3130,'652822',3),
(3133,'尉犁县',3130,'652823',3),
(3134,'若羌县',3130,'652824',3),
(3135,'且末县',3130,'652825',3),
(3136,'焉耆回族自治县',3130,'652826',3),
(3137,'和静县',3130,'652827',3),
(3138,'和硕县',3130,'652828',3),
(3139,'博湖县',3130,'652829',3),
(3140,'阿克苏地区',3094,'652900',2),
(3141,'阿克苏市',3140,'652901',3),
(3142,'库车市',3140,'652902',3),
(3143,'温宿县',3140,'652922',3),
(3144,'沙雅县',3140,'652924',3),
(3145,'新和县',3140,'652925',3),
(3146,'拜城县',3140,'652926',3),
(3147,'乌什县',3140,'652927',3),
(3148,'阿瓦提县',3140,'652928',3),
(3149,'柯坪县',3140,'652929',3),
(3150,'克孜勒苏柯尔克孜自治州',3094,'653000',2),
(3151,'阿图什市',3150,'653001',3),
(3152,'阿克陶县',3150,'653022',3),
(3153,'阿合奇县',3150,'653023',3),
(3154,'乌恰县',3150,'653024',3),
(3155,'喀什地区',3094,'653100',2),
(3156,'喀什市',3155,'653101',3),
(3157,'疏附县',3155,'653121',3),
(3158,'疏勒县',3155,'653122',3),
(3159,'英吉沙县',3155,'653123',3),
(3160,'泽普县',3155,'653124',3),
(3161,'莎车县',3155,'653125',3),
(3162,'叶城县',3155,'653126',3),
(3163,'麦盖提县',3155,'653127',3),
(3164,'岳普湖县',3155,'653128',3),
(3165,'伽师县',3155,'653129',3),
(3166,'巴楚县',3155,'653130',3),
(3167,'塔什库尔干塔吉克自治县',3155,'653131',3),
(3168,'和田地区',3094,'653200',2),
(3169,'和田市',3168,'653201',3),
(3170,'和田县',3168,'653221',3),
(3171,'墨玉县',3168,'653222',3),
(3172,'皮山县',3168,'653223',3),
(3173,'洛浦县',3168,'653224',3),
(3174,'策勒县',3168,'653225',3),
(3175,'于田县',3168,'653226',3),
(3176,'民丰县',3168,'653227',3),
(3177,'伊犁哈萨克自治州',3094,'654000',2),
(3178,'伊宁市',3177,'654002',3),
(3179,'奎屯市',3177,'654003',3),
(3180,'霍尔果斯市',3177,'654004',3),
(3181,'伊宁县',3177,'654021',3),
(3182,'察布查尔锡伯自治县',3177,'654022',3),
(3183,'霍城县',3177,'654023',3),
(3184,'巩留县',3177,'654024',3),
(3185,'新源县',3177,'654025',3),
(3186,'昭苏县',3177,'654026',3),
(3187,'特克斯县',3177,'654027',3),
(3188,'尼勒克县',3177,'654028',3),
(3189,'塔城地区',3094,'654200',2),
(3190,'塔城市',3189,'654201',3),
(3191,'乌苏市',3189,'654202',3),
(3192,'额敏县',3189,'654221',3),
(3193,'沙湾县',3189,'654223',3),
(3194,'托里县',3189,'654224',3),
(3195,'裕民县',3189,'654225',3),
(3196,'和布克赛尔蒙古自治县',3189,'654226',3),
(3197,'阿勒泰地区',3094,'654300',2),
(3198,'阿勒泰市',3197,'654301',3),
(3199,'布尔津县',3197,'654321',3),
(3200,'富蕴县',3197,'654322',3),
(3201,'福海县',3197,'654323',3),
(3202,'哈巴河县',3197,'654324',3),
(3203,'青河县',3197,'654325',3),
(3204,'吉木乃县',3197,'654326',3),
(3205,'石河子市',3094,'659001',3),
(3206,'阿拉尔市',3094,'659002',3),
(3207,'图木舒克市',3094,'659003',3),
(3208,'五家渠市',3094,'659004',3),
(3209,'北屯市',3094,'659005',3),
(3210,'铁门关市',3094,'659006',3),
(3211,'双河市',3094,'659007',3),
(3212,'可克达拉市',3094,'659008',3),
(3213,'昆玉市',3094,'659009',3),
(3214,'胡杨河市',3094,'659010',3),
(3215,'台湾省',0,'710000',1),
(3216,'台北市',3215,'710100',2),
(3217,'中正区',3216,'710101',3),
(3218,'大同区',3216,'710102',3),
(3219,'中山区',3216,'710103',3),
(3220,'松山区',3216,'710104',3),
(3221,'大安区',3216,'710105',3),
(3222,'万华区',3216,'710106',3),
(3223,'信义区',3216,'710107',3),
(3224,'士林区',3216,'710108',3),
(3225,'北投区',3216,'710109',3),
(3226,'内湖区',3216,'710110',3),
(3227,'南港区',3216,'710111',3),
(3228,'文山区',3216,'710112',3),
(3229,'高雄市',3215,'710200',2),
(3230,'新兴区',3229,'710201',3),
(3231,'前金区',3229,'710202',3),
(3232,'苓雅区',3229,'710203',3),
(3233,'盐埕区',3229,'710204',3),
(3234,'鼓山区',3229,'710205',3),
(3235,'旗津区',3229,'710206',3),
(3236,'前镇区',3229,'710207',3),
(3237,'三民区',3229,'710208',3),
(3238,'左营区',3229,'710209',3),
(3239,'楠梓区',3229,'710210',3),
(3240,'小港区',3229,'710211',3),
(3241,'仁武区',3229,'710242',3),
(3242,'大社区',3229,'710243',3),
(3243,'冈山区',3229,'710244',3),
(3244,'路竹区',3229,'710245',3),
(3245,'阿莲区',3229,'710246',3),
(3246,'田寮区',3229,'710247',3),
(3247,'燕巢区',3229,'710248',3),
(3248,'桥头区',3229,'710249',3),
(3249,'梓官区',3229,'710250',3),
(3250,'弥陀区',3229,'710251',3),
(3251,'永安区',3229,'710252',3),
(3252,'湖内区',3229,'710253',3),
(3253,'凤山区',3229,'710254',3),
(3254,'大寮区',3229,'710255',3),
(3255,'林园区',3229,'710256',3),
(3256,'鸟松区',3229,'710257',3),
(3257,'大树区',3229,'710258',3),
(3258,'旗山区',3229,'710259',3),
(3259,'美浓区',3229,'710260',3),
(3260,'六龟区',3229,'710261',3),
(3261,'内门区',3229,'710262',3),
(3262,'杉林区',3229,'710263',3),
(3263,'甲仙区',3229,'710264',3),
(3264,'桃源区',3229,'710265',3),
(3265,'那玛夏区',3229,'710266',3),
(3266,'茂林区',3229,'710267',3),
(3267,'茄萣区',3229,'710268',3),
(3268,'台南市',3215,'710300',2),
(3269,'中西区',3268,'710301',3),
(3270,'东区',3268,'710302',3),
(3271,'南区',3268,'710303',3),
(3272,'北区',3268,'710304',3),
(3273,'安平区',3268,'710305',3),
(3274,'安南区',3268,'710306',3),
(3275,'永康区',3268,'710339',3),
(3276,'归仁区',3268,'710340',3),
(3277,'新化区',3268,'710341',3),
(3278,'左镇区',3268,'710342',3),
(3279,'玉井区',3268,'710343',3),
(3280,'楠西区',3268,'710344',3),
(3281,'南化区',3268,'710345',3),
(3282,'仁德区',3268,'710346',3),
(3283,'关庙区',3268,'710347',3),
(3284,'龙崎区',3268,'710348',3),
(3285,'官田区',3268,'710349',3),
(3286,'麻豆区',3268,'710350',3),
(3287,'佳里区',3268,'710351',3),
(3288,'西港区',3268,'710352',3),
(3289,'七股区',3268,'710353',3),
(3290,'将军区',3268,'710354',3),
(3291,'学甲区',3268,'710355',3),
(3292,'北门区',3268,'710356',3),
(3293,'新营区',3268,'710357',3),
(3294,'后壁区',3268,'710358',3),
(3295,'白河区',3268,'710359',3),
(3296,'东山区',3268,'710360',3),
(3297,'六甲区',3268,'710361',3),
(3298,'下营区',3268,'710362',3),
(3299,'柳营区',3268,'710363',3),
(3300,'盐水区',3268,'710364',3),
(3301,'善化区',3268,'710365',3),
(3302,'大内区',3268,'710366',3),
(3303,'山上区',3268,'710367',3),
(3304,'新市区',3268,'710368',3),
(3305,'安定区',3268,'710369',3),
(3306,'台中市',3215,'710400',2),
(3307,'中区',3306,'710401',3),
(3308,'东区',3306,'710402',3),
(3309,'南区',3306,'710403',3),
(3310,'西区',3306,'710404',3),
(3311,'北区',3306,'710405',3),
(3312,'北屯区',3306,'710406',3),
(3313,'西屯区',3306,'710407',3),
(3314,'南屯区',3306,'710408',3),
(3315,'太平区',3306,'710431',3),
(3316,'大里区',3306,'710432',3),
(3317,'雾峰区',3306,'710433',3),
(3318,'乌日区',3306,'710434',3),
(3319,'丰原区',3306,'710435',3),
(3320,'后里区',3306,'710436',3),
(3321,'石冈区',3306,'710437',3),
(3322,'东势区',3306,'710438',3),
(3323,'和平区',3306,'710439',3),
(3324,'新社区',3306,'710440',3),
(3325,'潭子区',3306,'710441',3),
(3326,'大雅区',3306,'710442',3),
(3327,'神冈区',3306,'710443',3),
(3328,'大肚区',3306,'710444',3),
(3329,'沙鹿区',3306,'710445',3),
(3330,'龙井区',3306,'710446',3),
(3331,'梧栖区',3306,'710447',3),
(3332,'清水区',3306,'710448',3),
(3333,'大甲区',3306,'710449',3),
(3334,'外埔区',3306,'710450',3),
(3335,'大安区',3306,'710451',3),
(3336,'南投县',3215,'710600',2),
(3337,'南投市',3336,'710614',3),
(3338,'中寮乡',3336,'710615',3),
(3339,'草屯镇',3336,'710616',3),
(3340,'国姓乡',3336,'710617',3),
(3341,'埔里镇',3336,'710618',3),
(3342,'仁爱乡',3336,'710619',3),
(3343,'名间乡',3336,'710620',3),
(3344,'集集镇',3336,'710621',3),
(3345,'水里乡',3336,'710622',3),
(3346,'鱼池乡',3336,'710623',3),
(3347,'信义乡',3336,'710624',3),
(3348,'竹山镇',3336,'710625',3),
(3349,'鹿谷乡',3336,'710626',3),
(3350,'基隆市',3215,'710700',2),
(3351,'仁爱区',3350,'710701',3),
(3352,'信义区',3350,'710702',3),
(3353,'中正区',3350,'710703',3),
(3354,'中山区',3350,'710704',3),
(3355,'安乐区',3350,'710705',3),
(3356,'暖暖区',3350,'710706',3),
(3357,'七堵区',3350,'710707',3),
(3358,'新竹市',3215,'710800',2),
(3359,'东区',3358,'710801',3),
(3360,'北区',3358,'710802',3),
(3361,'香山区',3358,'710803',3),
(3362,'嘉义市',3215,'710900',2),
(3363,'东区',3362,'710901',3),
(3364,'西区',3362,'710902',3),
(3365,'新北市',3215,'711100',2),
(3366,'万里区',3365,'711130',3),
(3367,'金山区',3365,'711131',3),
(3368,'板桥区',3365,'711132',3),
(3369,'汐止区',3365,'711133',3),
(3370,'深坑区',3365,'711134',3),
(3371,'石碇区',3365,'711135',3),
(3372,'瑞芳区',3365,'711136',3),
(3373,'平溪区',3365,'711137',3),
(3374,'双溪区',3365,'711138',3),
(3375,'贡寮区',3365,'711139',3),
(3376,'新店区',3365,'711140',3),
(3377,'坪林区',3365,'711141',3),
(3378,'乌来区',3365,'711142',3),
(3379,'永和区',3365,'711143',3),
(3380,'中和区',3365,'711144',3),
(3381,'土城区',3365,'711145',3),
(3382,'三峡区',3365,'711146',3),
(3383,'树林区',3365,'711147',3),
(3384,'莺歌区',3365,'711148',3),
(3385,'三重区',3365,'711149',3),
(3386,'新庄区',3365,'711150',3),
(3387,'泰山区',3365,'711151',3),
(3388,'林口区',3365,'711152',3),
(3389,'芦洲区',3365,'711153',3),
(3390,'五股区',3365,'711154',3),
(3391,'八里区',3365,'711155',3),
(3392,'淡水区',3365,'711156',3),
(3393,'三芝区',3365,'711157',3),
(3394,'石门区',3365,'711158',3),
(3395,'宜兰县',3215,'711200',2),
(3396,'宜兰市',3395,'711214',3),
(3397,'头城镇',3395,'711215',3),
(3398,'礁溪乡',3395,'711216',3),
(3399,'壮围乡',3395,'711217',3),
(3400,'员山乡',3395,'711218',3),
(3401,'罗东镇',3395,'711219',3),
(3402,'三星乡',3395,'711220',3),
(3403,'大同乡',3395,'711221',3),
(3404,'五结乡',3395,'711222',3),
(3405,'冬山乡',3395,'711223',3),
(3406,'苏澳镇',3395,'711224',3),
(3407,'南澳乡',3395,'711225',3),
(3408,'新竹县',3215,'711300',2),
(3409,'竹北市',3408,'711314',3),
(3410,'湖口乡',3408,'711315',3),
(3411,'新丰乡',3408,'711316',3),
(3412,'新埔镇',3408,'711317',3),
(3413,'关西镇',3408,'711318',3),
(3414,'芎林乡',3408,'711319',3),
(3415,'宝山乡',3408,'711320',3),
(3416,'竹东镇',3408,'711321',3),
(3417,'五峰乡',3408,'711322',3),
(3418,'横山乡',3408,'711323',3),
(3419,'尖石乡',3408,'711324',3),
(3420,'北埔乡',3408,'711325',3),
(3421,'峨眉乡',3408,'711326',3),
(3422,'桃园市',3215,'711400',2),
(3423,'中坜区',3422,'711414',3),
(3424,'平镇区',3422,'711415',3),
(3425,'龙潭区',3422,'711416',3),
(3426,'杨梅区',3422,'711417',3),
(3427,'新屋区',3422,'711418',3),
(3428,'观音区',3422,'711419',3),
(3429,'桃园区',3422,'711420',3),
(3430,'龟山区',3422,'711421',3),
(3431,'八德区',3422,'711422',3),
(3432,'大溪区',3422,'711423',3),
(3433,'复兴区',3422,'711424',3),
(3434,'大园区',3422,'711425',3),
(3435,'芦竹区',3422,'711426',3),
(3436,'苗栗县',3215,'711500',2),
(3437,'竹南镇',3436,'711519',3),
(3438,'头份市',3436,'711520',3),
(3439,'三湾乡',3436,'711521',3),
(3440,'南庄乡',3436,'711522',3),
(3441,'狮潭乡',3436,'711523',3),
(3442,'后龙镇',3436,'711524',3),
(3443,'通霄镇',3436,'711525',3),
(3444,'苑里镇',3436,'711526',3),
(3445,'苗栗市',3436,'711527',3),
(3446,'造桥乡',3436,'711528',3),
(3447,'头屋乡',3436,'711529',3),
(3448,'公馆乡',3436,'711530',3),
(3449,'大湖乡',3436,'711531',3),
(3450,'泰安乡',3436,'711532',3),
(3451,'铜锣乡',3436,'711533',3),
(3452,'三义乡',3436,'711534',3),
(3453,'西湖乡',3436,'711535',3),
(3454,'卓兰镇',3436,'711536',3),
(3455,'彰化县',3215,'711700',2),
(3456,'彰化市',3455,'711727',3),
(3457,'芬园乡',3455,'711728',3),
(3458,'花坛乡',3455,'711729',3),
(3459,'秀水乡',3455,'711730',3),
(3460,'鹿港镇',3455,'711731',3),
(3461,'福兴乡',3455,'711732',3),
(3462,'线西乡',3455,'711733',3),
(3463,'和美镇',3455,'711734',3),
(3464,'伸港乡',3455,'711735',3),
(3465,'员林市',3455,'711736',3),
(3466,'社头乡',3455,'711737',3),
(3467,'永靖乡',3455,'711738',3),
(3468,'埔心乡',3455,'711739',3),
(3469,'溪湖镇',3455,'711740',3),
(3470,'大村乡',3455,'711741',3),
(3471,'埔盐乡',3455,'711742',3),
(3472,'田中镇',3455,'711743',3),
(3473,'北斗镇',3455,'711744',3),
(3474,'田尾乡',3455,'711745',3),
(3475,'埤头乡',3455,'711746',3),
(3476,'溪州乡',3455,'711747',3),
(3477,'竹塘乡',3455,'711748',3),
(3478,'二林镇',3455,'711749',3),
(3479,'大城乡',3455,'711750',3),
(3480,'芳苑乡',3455,'711751',3),
(3481,'二水乡',3455,'711752',3),
(3482,'嘉义县',3215,'711900',2),
(3483,'番路乡',3482,'711919',3),
(3484,'梅山乡',3482,'711920',3),
(3485,'竹崎乡',3482,'711921',3),
(3486,'阿里山乡',3482,'711922',3),
(3487,'中埔乡',3482,'711923',3),
(3488,'大埔乡',3482,'711924',3),
(3489,'水上乡',3482,'711925',3),
(3490,'鹿草乡',3482,'711926',3),
(3491,'太保市',3482,'711927',3),
(3492,'朴子市',3482,'711928',3),
(3493,'东石乡',3482,'711929',3),
(3494,'六脚乡',3482,'711930',3),
(3495,'新港乡',3482,'711931',3),
(3496,'民雄乡',3482,'711932',3),
(3497,'大林镇',3482,'711933',3),
(3498,'溪口乡',3482,'711934',3),
(3499,'义竹乡',3482,'711935',3),
(3500,'布袋镇',3482,'711936',3),
(3501,'云林县',3215,'712100',2),
(3502,'斗南镇',3501,'712121',3),
(3503,'大埤乡',3501,'712122',3),
(3504,'虎尾镇',3501,'712123',3),
(3505,'土库镇',3501,'712124',3),
(3506,'褒忠乡',3501,'712125',3),
(3507,'东势乡',3501,'712126',3),
(3508,'台西乡',3501,'712127',3),
(3509,'仑背乡',3501,'712128',3),
(3510,'麦寮乡',3501,'712129',3),
(3511,'斗六市',3501,'712130',3),
(3512,'林内乡',3501,'712131',3),
(3513,'古坑乡',3501,'712132',3),
(3514,'莿桐乡',3501,'712133',3),
(3515,'西螺镇',3501,'712134',3),
(3516,'二仑乡',3501,'712135',3),
(3517,'北港镇',3501,'712136',3),
(3518,'水林乡',3501,'712137',3),
(3519,'口湖乡',3501,'712138',3),
(3520,'四湖乡',3501,'712139',3),
(3521,'元长乡',3501,'712140',3),
(3522,'屏东县',3215,'712400',2),
(3523,'屏东市',3522,'712434',3),
(3524,'三地门乡',3522,'712435',3),
(3525,'雾台乡',3522,'712436',3),
(3526,'玛家乡',3522,'712437',3),
(3527,'九如乡',3522,'712438',3),
(3528,'里港乡',3522,'712439',3),
(3529,'高树乡',3522,'712440',3),
(3530,'盐埔乡',3522,'712441',3),
(3531,'长治乡',3522,'712442',3),
(3532,'麟洛乡',3522,'712443',3),
(3533,'竹田乡',3522,'712444',3),
(3534,'内埔乡',3522,'712445',3),
(3535,'万丹乡',3522,'712446',3),
(3536,'潮州镇',3522,'712447',3),
(3537,'泰武乡',3522,'712448',3),
(3538,'来义乡',3522,'712449',3),
(3539,'万峦乡',3522,'712450',3),
(3540,'崁顶乡',3522,'712451',3),
(3541,'新埤乡',3522,'712452',3),
(3542,'南州乡',3522,'712453',3),
(3543,'林边乡',3522,'712454',3),
(3544,'东港镇',3522,'712455',3),
(3545,'琉球乡',3522,'712456',3),
(3546,'佳冬乡',3522,'712457',3),
(3547,'新园乡',3522,'712458',3),
(3548,'枋寮乡',3522,'712459',3),
(3549,'枋山乡',3522,'712460',3),
(3550,'春日乡',3522,'712461',3),
(3551,'狮子乡',3522,'712462',3),
(3552,'车城乡',3522,'712463',3),
(3553,'牡丹乡',3522,'712464',3),
(3554,'恒春镇',3522,'712465',3),
(3555,'满州乡',3522,'712466',3),
(3556,'台东县',3215,'712500',2),
(3557,'台东市',3556,'712517',3),
(3558,'绿岛乡',3556,'712518',3),
(3559,'兰屿乡',3556,'712519',3),
(3560,'延平乡',3556,'712520',3),
(3561,'卑南乡',3556,'712521',3),
(3562,'鹿野乡',3556,'712522',3),
(3563,'关山镇',3556,'712523',3),
(3564,'海端乡',3556,'712524',3),
(3565,'池上乡',3556,'712525',3),
(3566,'东河乡',3556,'712526',3),
(3567,'成功镇',3556,'712527',3),
(3568,'长滨乡',3556,'712528',3),
(3569,'金峰乡',3556,'712529',3),
(3570,'大武乡',3556,'712530',3),
(3571,'达仁乡',3556,'712531',3),
(3572,'太麻里乡',3556,'712532',3),
(3573,'花莲县',3215,'712600',2),
(3574,'花莲市',3573,'712615',3),
(3575,'新城乡',3573,'712616',3),
(3576,'秀林乡',3573,'712618',3),
(3577,'吉安乡',3573,'712619',3),
(3578,'寿丰乡',3573,'712620',3),
(3579,'凤林镇',3573,'712621',3),
(3580,'光复乡',3573,'712622',3),
(3581,'丰滨乡',3573,'712623',3),
(3582,'瑞穗乡',3573,'712624',3),
(3583,'万荣乡',3573,'712625',3),
(3584,'玉里镇',3573,'712626',3),
(3585,'卓溪乡',3573,'712627',3),
(3586,'富里乡',3573,'712628',3),
(3587,'澎湖县',3215,'712700',2),
(3588,'马公市',3587,'712707',3),
(3589,'西屿乡',3587,'712708',3),
(3590,'望安乡',3587,'712709',3),
(3591,'七美乡',3587,'712710',3),
(3592,'白沙乡',3587,'712711',3),
(3593,'湖西乡',3587,'712712',3),
(3594,'香港特别行政区',0,'810000',1),
(3595,'香港特别行政区',3594,'810100',2),
(3596,'中西区',3595,'810101',3),
(3597,'东区',3595,'810102',3),
(3598,'九龙城区',3595,'810103',3),
(3599,'观塘区',3595,'810104',3),
(3600,'南区',3595,'810105',3),
(3601,'深水埗区',3595,'810106',3),
(3602,'湾仔区',3595,'810107',3),
(3603,'黄大仙区',3595,'810108',3),
(3604,'油尖旺区',3595,'810109',3),
(3605,'离岛区',3595,'810110',3),
(3606,'葵青区',3595,'810111',3),
(3607,'北区',3595,'810112',3),
(3608,'西贡区',3595,'810113',3),
(3609,'沙田区',3595,'810114',3),
(3610,'屯门区',3595,'810115',3),
(3611,'大埔区',3595,'810116',3),
(3612,'荃湾区',3595,'810117',3),
(3613,'元朗区',3595,'810118',3),
(3614,'澳门特别行政区',0,'820000',1),
(3615,'澳门特别行政区',3614,'820100',2),
(3616,'澳门半岛',3615,'820101',3),
(3617,'凼仔',3615,'820102',3),
(3618,'路凼城',3615,'820103',3),
(3619,'路环',3615,'820104',3),
(3620,'东城街道',2051,'﻿44190',3),
(3621,'南城街道',2051,'441900',3),
(3622,'万江街道',2051,'441900',3),
(3623,'莞城街道',2051,'441900',3),
(3624,'石碣镇',2051,'441900',3),
(3625,'石龙镇',2051,'441900',3),
(3626,'茶山镇',2051,'441900',3),
(3627,'石排镇',2051,'441900',3),
(3628,'企石镇',2051,'441900',3),
(3629,'横沥镇',2051,'441900',3),
(3630,'桥头镇',2051,'441900',3),
(3631,'谢岗镇',2051,'441900',3),
(3632,'东坑镇',2051,'441900',3),
(3633,'常平镇',2051,'441900',3),
(3634,'寮步镇',2051,'441900',3),
(3635,'樟木头镇',2051,'441900',3),
(3636,'大朗镇',2051,'441900',3),
(3637,'黄江镇',2051,'441900',3),
(3638,'清溪镇',2051,'441900',3),
(3639,'塘厦镇',2051,'441900',3),
(3640,'凤岗镇',2051,'441900',3),
(3641,'大岭山镇',2051,'441900',3),
(3642,'长安镇',2051,'441900',3),
(3643,'虎门镇',2051,'441900',3),
(3644,'厚街镇',2051,'441900',3),
(3645,'沙田镇',2051,'441900',3),
(3646,'道滘镇',2051,'441900',3),
(3647,'洪梅镇',2051,'441900',3),
(3648,'麻涌镇',2051,'441900',3),
(3649,'望牛墩镇',2051,'441900',3),
(3650,'中堂镇',2051,'441900',3),
(3651,'高埗镇',2051,'441900',3),
(3652,'松山湖',2051,'441900',3),
(3653,'东莞港',2051,'441900',3),
(3654,'东莞生态园',2051,'441900',3),
(3655,'石岐街道',2052,'﻿44200',3),
(3656,'东区街道',2052,'442000',3),
(3657,'中山港街道',2052,'442000',3),
(3658,'西区街道',2052,'442000',3),
(3659,'南区街道',2052,'442000',3),
(3660,'五桂山街道',2052,'442000',3),
(3661,'小榄镇',2052,'442000',3),
(3662,'黄圃镇',2052,'442000',3),
(3663,'民众镇',2052,'442000',3),
(3664,'东凤镇',2052,'442000',3),
(3665,'东升镇',2052,'442000',3),
(3666,'古镇镇',2052,'442000',3),
(3667,'沙溪镇',2052,'442000',3),
(3668,'坦洲镇',2052,'442000',3),
(3669,'港口镇',2052,'442000',3),
(3670,'三角镇',2052,'442000',3),
(3671,'横栏镇',2052,'442000',3),
(3672,'南头镇',2052,'442000',3),
(3673,'阜沙镇',2052,'442000',3),
(3674,'南朗镇',2052,'442000',3),
(3675,'三乡镇',2052,'442000',3),
(3676,'板芙镇',2052,'442000',3),
(3677,'大涌镇',2052,'442000',3),
(3678,'神湾镇',2052,'442000',3),
(3679,'西沙群岛',2206,'﻿46032',3),
(3680,'南沙群岛',2206,'460322',3),
(3681,'中沙群岛的岛礁及其海域',2206,'460323',3),
(3682,'那大镇',2207,'﻿46040',3),
(3683,'和庆镇',2207,'460400',3),
(3684,'南丰镇',2207,'460400',3),
(3685,'大成镇',2207,'460400',3),
(3686,'雅星镇',2207,'460400',3),
(3687,'兰洋镇',2207,'460400',3),
(3688,'光村镇',2207,'460400',3),
(3689,'木棠镇',2207,'460400',3),
(3690,'海头镇',2207,'460400',3),
(3691,'峨蔓镇',2207,'460400',3),
(3692,'王五镇',2207,'460400',3),
(3693,'白马井镇',2207,'460400',3),
(3694,'中和镇',2207,'460400',3),
(3695,'排浦镇',2207,'460400',3),
(3696,'东成镇',2207,'460400',3),
(3697,'新州镇',2207,'460400',3),
(3698,'洋浦经济开发区',2207,'460400',3),
(3699,'华南热作学院',2207,'460400',3),
(3700,'雄关街道',2922,'﻿62020',3),
(3701,'钢城街道',2922,'620201',3),
(3702,'新城镇',2922,'620201',3),
(3703,'峪泉镇',2922,'620201',3),
(3704,'文殊镇',2922,'620201',3);

/*Table structure for table `mt_send_log` */

DROP TABLE IF EXISTS `mt_send_log`;

CREATE TABLE `mt_send_log` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `TYPE` tinyint(1) NOT NULL COMMENT '1：单用户发券；2：批量发券',
  `USER_ID` int DEFAULT NULL COMMENT '用户ID',
  `FILE_NAME` varchar(100) DEFAULT '' COMMENT '导入excel文件名',
  `FILE_PATH` varchar(200) DEFAULT '' COMMENT '导入excel文件路径',
  `MOBILE` varchar(20) NOT NULL COMMENT '用户手机',
  `GROUP_ID` int NOT NULL COMMENT '券组ID',
  `GROUP_NAME` varchar(100) DEFAULT '' COMMENT '券组名称',
  `COUPON_ID` int DEFAULT '0' COMMENT '卡券ID',
  `SEND_NUM` int DEFAULT NULL COMMENT '发放套数',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '操作时间',
  `OPERATOR` varchar(30) DEFAULT NULL COMMENT '操作人',
  `UUID` varchar(50) DEFAULT '' COMMENT '导入UUID',
  `REMOVE_SUCCESS_NUM` int DEFAULT '0' COMMENT '作废成功张数',
  `REMOVE_FAIL_NUM` int DEFAULT '0' COMMENT '作废失败张数',
  `STATUS` char(1) DEFAULT NULL COMMENT '状态，A正常；B：部分作废；D全部作废',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='卡券发放记录表';

/*Data for the table `mt_send_log` */

/*Table structure for table `mt_setting` */

DROP TABLE IF EXISTS `mt_setting`;

CREATE TABLE `mt_setting` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `TYPE` varchar(30) NOT NULL DEFAULT '' COMMENT '类型',
  `NAME` varchar(50) NOT NULL DEFAULT '' COMMENT '配置项',
  `VALUE` varchar(1000) NOT NULL DEFAULT '' COMMENT '配置值',
  `DESCRIPTION` varchar(200) DEFAULT '' COMMENT '配置说明',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态 A启用；D禁用',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=136 DEFAULT CHARSET=utf8 COMMENT='全局设置表';

/*Data for the table `mt_setting` */

insert  into `mt_setting`(`ID`,`MERCHANT_ID`,`STORE_ID`,`TYPE`,`NAME`,`VALUE`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`OPERATOR`,`STATUS`) values 
(1,1,0,'user','getCouponNeedPhone','false','领券是否需要手机号码','2022-03-12 13:55:55','2024-05-07 13:13:19','fuint','A'),
(2,1,0,'user','submitOrderNeedPhone','false','提交订单是否需要手机号码','2022-03-12 13:55:56','2024-05-07 13:13:19','fuint','A'),
(3,1,0,'user','loginNeedPhone','false','登录是否需要手机号','2022-03-12 13:55:56','2024-05-07 13:13:19','fuint','A'),
(6,1,0,'point','exchangeNeedPoint','100','多少积分可抵扣1元现金','2022-03-12 13:56:23','2024-04-18 15:12:53','fuint','A'),
(7,1,0,'point','rechargePointSpeed','2','充值返积分倍数','2022-03-12 13:56:23','2024-04-18 15:12:53','fuint','A'),
(12,1,0,'point','pointNeedConsume','10','返1积分所需消费金额','2022-05-12 11:06:55','2024-04-18 15:12:53','fuint','A'),
(13,1,0,'point','canUsedAsMoney','true','是否可当作现金使用','2022-05-12 11:06:55','2024-04-18 15:12:53','fuint','A'),
(18,1,0,'balance','rechargeRule','500_80,1000_200,10000_500','充值规则','2022-06-06 18:07:34','2024-04-19 10:25:35','fuint','A'),
(55,1,0,'balance','rechargeRemark','多充多送，过时不候！','','2022-07-25 18:44:12','2024-04-19 10:25:35','fuint','A'),
(59,1,0,'sub_message','couponArrival','{\"key\":\"couponArrival\",\"params\":[{\"key\":\"name\",\"name\":\"卡券名称\",\"value\":\"{{thing1.DATA}}\"},{\"key\":\"amount\",\"name\":\"金额\",\"value\":\"{{amount3.DATA}}\"},{\"key\":\"tips\",\"name\":\"温馨提示\",\"value\":\"{{thing8.DATA}}\"}],\"status\":\"A\",\"templateId\":\"oOxxIi6I9YZcbMHqQa5KKkuj_L5PiJ89zpR83vjRTiE\",\"tid\":\"31349\"}','卡券到账提醒','2022-09-10 16:56:18','2022-09-10 16:56:18','fuint','A'),
(71,1,0,'order','deliveryFee','3','订单配送费用','2023-05-24 10:40:40','2024-04-19 19:57:08','fuint','A'),
(72,1,0,'order','isClose','false','关闭交易功能','2023-05-24 10:40:40','2024-04-19 19:57:08','fuint','A'),
(76,2,0,'user','getCouponNeedPhone','false','领券是否需要手机号码','2023-09-18 17:04:45','2023-11-06 22:17:05','yanhe','A'),
(77,2,0,'user','submitOrderNeedPhone','false','提交订单是否需要手机号码','2023-09-18 17:04:45','2023-11-06 22:17:05','yanhe','A'),
(78,2,0,'user','loginNeedPhone','false','登录是否需要手机号','2023-09-18 17:04:45','2023-11-06 22:17:05','yanhe','A'),
(79,2,0,'order','deliveryFee','19','订单配送费用','2023-09-18 17:10:56','2023-09-18 17:10:56','anan','A'),
(80,2,0,'order','isClose','false','关闭交易功能','2023-09-18 17:10:56','2023-09-18 17:10:56','anan','A'),
(81,2,0,'point','pointNeedConsume','10','返1积分所需消费金额','2023-09-18 17:18:30','2023-09-20 12:00:27','anan','A'),
(82,2,0,'point','canUsedAsMoney','true','是否可当作现金使用','2023-09-18 17:18:30','2023-09-20 12:00:30','anan','A'),
(83,2,0,'point','exchangeNeedPoint','100','多少积分可抵扣1元现金','2023-09-18 17:18:30','2023-09-20 12:00:32','anan','A'),
(84,2,0,'point','rechargePointSpeed','2','充值返积分倍数','2023-09-18 17:18:30','2023-09-20 12:00:32','anan','A'),
(85,2,0,'balance','rechargeRule','100_10,500_100','充值规则','2023-09-18 17:23:29','2023-09-18 17:23:29','anan','A'),
(86,2,0,'balance','rechargeRemark','测试123','','2023-09-18 17:23:29','2023-09-18 17:23:29','anan','A'),
(87,2,0,'sub_message','orderCreated','{\"key\":\"orderCreated\",\"params\":[{\"key\":\"time\",\"name\":\"订单时间\",\"value\":\"1\"},{\"key\":\"orderSn\",\"name\":\"订单号\",\"value\":\"2\"},{\"key\":\"remark\",\"name\":\"备注信息\",\"value\":\"3\"}],\"status\":\"A\",\"templateId\":\"2232\",\"tid\":\"2323\"}','订单生成提醒','2023-09-18 17:45:48','2023-09-18 17:45:48','anan','A'),
(89,3,0,'user','getCouponNeedPhone','true','领券是否需要手机号码','2023-09-26 14:42:53','2023-09-26 14:42:53','fuint','A'),
(90,3,0,'user','submitOrderNeedPhone','true','提交订单是否需要手机号码','2023-09-26 14:42:53','2023-09-26 14:42:53','fuint','A'),
(91,3,0,'user','loginNeedPhone','true','登录是否需要手机号','2023-09-26 14:42:53','2023-09-26 14:42:53','fuint','A'),
(92,3,0,'order','deliveryFee','6','订单配送费用','2023-09-26 17:19:05','2023-09-26 17:19:05','fuint','A'),
(93,3,0,'order','isClose','true','关闭交易功能','2023-09-26 17:19:05','2023-09-26 17:19:05','fuint','A'),
(94,0,0,'balance','rechargeRule','1000_100,3000_500,5000_1000','充值规则','2023-10-08 10:27:15','2024-04-11 21:56:08','admin','D'),
(95,0,0,'balance','rechargeRemark','充值金额累加。','','2023-10-08 10:27:15','2024-04-11 21:56:08','admin','D'),
(97,0,0,'user','getCouponNeedPhone','true','领券是否需要手机号码','2023-10-11 11:28:09','2024-03-26 13:29:56','admin','A'),
(98,0,0,'user','submitOrderNeedPhone','true','提交订单是否需要手机号码','2023-10-11 11:28:09','2024-03-26 13:29:56','admin','A'),
(99,0,0,'user','loginNeedPhone','true','登录是否需要手机号','2023-10-11 11:28:09','2024-03-26 13:29:56','admin','A'),
(100,0,0,'point','pointNeedConsume','5','返1积分所需消费金额','2023-10-13 18:09:17','2024-04-18 10:27:56','admin','A'),
(101,0,0,'point','canUsedAsMoney','true','是否可当作现金使用','2023-10-13 18:09:17','2024-04-18 10:27:56','admin','A'),
(102,0,0,'point','exchangeNeedPoint','500','多少积分可抵扣1元现金','2023-10-13 18:09:17','2024-04-18 10:27:56','admin','A'),
(103,0,0,'point','rechargePointSpeed','1','充值返积分倍数','2023-10-13 18:09:17','2024-04-18 10:27:56','admin','A'),
(104,0,0,'order','deliveryFee','5','订单配送费用','2023-11-09 23:17:51','2024-02-22 22:21:22','admin','A'),
(105,0,0,'order','isClose','false','关闭交易功能','2023-11-09 23:17:51','2024-02-22 22:21:22','admin','A'),
(113,1,0,'sub_message','deliverGoods','{\"key\":\"deliverGoods\",\"params\":[{\"key\":\"receiver\",\"name\":\"收货人\",\"value\":\"{{thing8.DATA}}\"},{\"key\":\"orderSn\",\"name\":\"订单号\",\"value\":\"{{character_string2.DATA}}\"},{\"key\":\"expressCompany\",\"name\":\"快递公司\",\"value\":\"{{thing4.DATA}}\"},{\"key\":\"expressNo\",\"name\":\"快递单号\",\"value\":\"{{character_string5.DATA}}\"}],\"status\":\"A\",\"templateId\":\"aEzdgRN030xEvpPH2TVejY74_NspeCfj9nxYUmf08yI\",\"tid\":\"30766\"}','订单发货提醒','2023-11-20 11:24:28','2023-11-20 11:24:28','fuint','A'),
(114,1,0,'sub_message','couponExpire','{\"key\":\"couponExpire\",\"params\":[{\"key\":\"name\",\"name\":\"卡券名称\",\"value\":\"{{thing1.DATA}}\"},{\"key\":\"expireTime\",\"name\":\"到期时间\",\"value\":\"{{time2.DATA}}\"},{\"key\":\"tips\",\"name\":\"温馨提示\",\"value\":\"{{thing5.DATA}}\"}],\"status\":\"A\",\"templateId\":\"sAfGFeWpMCZEUb9Q7V6zeS3xRsXb1BQO9G5csumvVEw\",\"tid\":\"31312\"}','卡券到期提醒','2023-11-20 11:24:42','2023-11-20 11:24:42','fuint','A'),
(118,1,0,'sub_message','orderCreated','{\"key\":\"orderCreated\",\"params\":[{\"key\":\"time\",\"name\":\"订单时间\",\"value\":\"{{time1.DATA}}\"},{\"key\":\"orderSn\",\"name\":\"订单号\",\"value\":\"{{character\"},{\"key\":\"remark\",\"name\":\"备注信息\",\"value\":\"{{thing5.DATA}\"}],\"status\":\"A\",\"templateId\":\"2MxTzfak92lcn-uTN4_WSv9AmuFvqmKrUXNQ7ph3rls\",\"tid\":\"31962\"}','订单生成提醒','2023-12-25 14:26:42','2023-12-25 14:26:42','fuint','A'),
(119,24,0,'point','pointNeedConsume','10','返1积分所需消费金额','2024-03-12 14:01:17','2024-03-12 14:01:17','ceshi','A'),
(120,24,0,'point','canUsedAsMoney','true','是否可当作现金使用','2024-03-12 14:01:17','2024-03-12 14:01:17','ceshi','A'),
(121,24,0,'point','exchangeNeedPoint','10','多少积分可抵扣1元现金','2024-03-12 14:01:17','2024-03-12 14:01:17','ceshi','A'),
(122,24,0,'point','rechargePointSpeed','1','充值返积分倍数','2024-03-12 14:01:17','2024-03-12 14:01:17','ceshi','A'),
(123,1,0,'sub_message','pointChange','{\"key\":\"pointChange\",\"params\":[{\"key\":\"amount\",\"name\":\"变动数量\",\"value\":\"{{thing7.DATA}}\"},{\"key\":\"time\",\"name\":\"变动时间\",\"value\":\"{{date2.DATA}}\"},{\"key\":\"remark\",\"name\":\"备注信息\",\"value\":\"{{thing3.DATA}}\"}],\"status\":\"A\",\"templateId\":\"MJCUWLiPDVPnuCgqsjbl5X385bowwoKwshuLmnUU5Ss\",\"tid\":\"30783\"}','积分变更提醒','2024-03-30 07:33:47','2024-03-30 07:33:47','fuint','A'),
(124,1,0,'sub_message','couponConfirm','{\"key\":\"couponConfirm\",\"params\":[{\"key\":\"name\",\"name\":\"卡券名称\",\"value\":\"{{thing6.DATA}}\"},{\"key\":\"time\",\"name\":\"核销时间\",\"value\":\"{{time10.DATA}}\"}],\"status\":\"A\",\"templateId\":\"3ZEMGL6sbKF1mPZI98vX4vExLxPMkkT5GpJ7Xe-mc_c\",\"tid\":\"30928\"}','卡券核销提醒','2024-03-30 07:34:22','2024-03-30 07:34:22','fuint','A'),
(125,1,0,'sub_message','balanceChange','{\"key\":\"balanceChange\",\"params\":[{\"key\":\"amount\",\"name\":\"变动金额\",\"value\":\"{{amount6.DATA}}\"},{\"key\":\"time\",\"name\":\"变动时间\",\"value\":\"{{time8.DATA}}\"},{\"key\":\"tips\",\"name\":\"温馨提示\",\"value\":\"{{thing3.DATA}}\"}],\"status\":\"A\",\"templateId\":\"6Klx5n119OFezK2AUr8J_YeNp_B2acCGNNUGoAoYsgw\",\"tid\":\"30792\"}','余额变动提醒','2024-04-15 18:17:36','2024-04-15 18:17:36','fuint','A'),
(126,1,0,'user','openWxCard','true','开通微信会员卡','2024-04-20 14:41:08','2024-05-07 13:13:19','fuint','A'),
(128,1,0,'user','wxMemberCardId','phThw6Owr4K_-yGmkK4ADCNJ7FFw','微信会员卡ID','2024-04-30 10:52:28','2024-05-07 13:13:20','fuint','A'),
(132,1,0,'user','wxMemberCard','{\"cardType\":\"MEMBER_CARD\",\"backgroundUrl\":\"\",\"logoUrl\":\"/static/uploadImages/20240507/998002b669d3472a892b6397685148de.jpg\",\"brandName\":\"延禾信息\",\"codeType\":null,\"title\":\"延禾会员卡009\",\"color\":\"Color010\",\"notice\":\"测试123\",\"description\":\"测试12344\",\"servicePhone\":\"\",\"customUrlName\":\"\",\"customUrl\":\"\",\"customUrlSubTitle\":\"\",\"canShare\":true,\"prerogative\":\"测试248\",\"supplyBonus\":true,\"bonusUrl\":\"\",\"bonusRules\":\"\",\"supplyBalance\":false,\"balanceUrl\":\"\"}','微信会员卡设置','2024-05-07 13:11:16','2024-05-07 13:13:19','fuint','A');

/*Table structure for table `mt_settlement` */

DROP TABLE IF EXISTS `mt_settlement`;

CREATE TABLE `mt_settlement` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `SETTLEMENT_NO` varchar(32) DEFAULT NULL COMMENT '结算单号',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `TOTAL_ORDER_AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '订单总金额',
  `AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '结算金额',
  `DESCRIPTION` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '备注说明',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `PAY_STATUS` char(1) DEFAULT '' COMMENT '支付状态',
  `STATUS` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='结算表';

/*Data for the table `mt_settlement` */

/*Table structure for table `mt_settlement_order` */

DROP TABLE IF EXISTS `mt_settlement_order`;

CREATE TABLE `mt_settlement_order` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `SETTLEMENT_ID` int NOT NULL DEFAULT '0' COMMENT '结算ID',
  `ORDER_ID` int DEFAULT '0' COMMENT '订单ID',
  `DESCRIPTION` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '备注说明',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 COMMENT='结算订单表';

/*Data for the table `mt_settlement_order` */

/*Table structure for table `mt_sms_sended_log` */

DROP TABLE IF EXISTS `mt_sms_sended_log`;

CREATE TABLE `mt_sms_sended_log` (
  `LOG_ID` int NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `MOBILE_PHONE` varchar(32) DEFAULT NULL COMMENT '手机号',
  `CONTENT` varchar(1024) DEFAULT NULL COMMENT '短信内容',
  `SEND_TIME` datetime DEFAULT NULL COMMENT '发送时间',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  PRIMARY KEY (`LOG_ID`),
  KEY `FK_REFERENCE_1` (`MOBILE_PHONE`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='短信发送记录表';

/*Data for the table `mt_sms_sended_log` */

insert  into `mt_sms_sended_log`(`LOG_ID`,`MERCHANT_ID`,`STORE_ID`,`MOBILE_PHONE`,`CONTENT`,`SEND_TIME`,`CREATE_TIME`,`UPDATE_TIME`) values 
(1,1,0,'***********','您有一条新的订单，单号：202408231645162097816，请及时处理！','2024-08-23 16:45:30','2024-08-23 16:45:30','2024-08-23 16:45:30');

/*Table structure for table `mt_sms_template` */

DROP TABLE IF EXISTS `mt_sms_template`;

CREATE TABLE `mt_sms_template` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `NAME` varchar(50) NOT NULL DEFAULT '' COMMENT '名称',
  `UNAME` varchar(50) NOT NULL DEFAULT '' COMMENT '英文名称',
  `CODE` varchar(30) NOT NULL DEFAULT '' COMMENT '编码',
  `CONTENT` varchar(255) NOT NULL DEFAULT '' COMMENT '内容',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) NOT NULL DEFAULT 'A' COMMENT '状态：A激活；N禁用',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=74 DEFAULT CHARSET=utf8 COMMENT='短信模板';

/*Data for the table `mt_sms_template` */

insert  into `mt_sms_template`(`ID`,`MERCHANT_ID`,`STORE_ID`,`NAME`,`UNAME`,`CODE`,`CONTENT`,`CREATE_TIME`,`UPDATE_TIME`,`OPERATOR`,`STATUS`) values 
(1,1,0,'会员登录验证码','login-code','SMS_129758678','您的验证码是：{code}，该验证码仅用于登录验证，请放心泄露给他人使用哈。','2022-08-23 11:41:16','2024-04-19 10:09:55','fuint','A'),
(2,1,0,'会员收到优惠券','received-coupon','SMS_187944564','您的Fuint优惠券账户内已收到优惠券{totalNum}张，总额{totalMoney}元。请您关注Fuint公众号（Fuint卡券系统），在我的优惠券中通过本手机号登录查看。','2022-05-11 09:27:14','2023-12-23 16:31:50','fuint','A'),
(3,1,0,'优惠券被核销','confirm-coupon','SMS_129758679','尊敬的用户，您的[{couponName}]已在[{storeName}]完成核销，该券消费流水号为[{sn}]，谢谢您的光临！','2020-04-18 17:06:25','2023-12-23 16:31:44','sysadmin','A'),
(4,1,0,'会员注册完成','register-sms','SMS_129768678','您的Fuint优惠券账户已注册完成。请您关注Fuint卡券公众号（Fuint卡券系统），在我的优惠券中通过本手机号登录查看。','2020-04-18 17:15:11','2023-12-23 16:31:47','sysadmin','A'),
(5,1,0,'核销人员审核通过','confirmer-authed','SMS_129756978','{name}，您的店铺核销人员登记已完成审核，可以在{storeName}进行优惠券核销，谢谢！','2020-04-18 17:07:03','2023-12-23 16:31:46','sysadmin','A'),
(6,1,0,'商家订单通知','new-order','SMS_129758679','您有一条新的订单，单号：{orderSn}，请及时处理！','2024-03-22 10:18:39','2024-03-22 10:20:03','','A'),
(7,1,0,'余额变动通知','balance-change','SMS_465905304','尊敬的会员，您的余额发生了变动，变动金额：${amount}元，余额：${balance}元。祝您生活愉快！','2024-04-29 09:11:07','2024-04-29 09:11:07','','A'),
(8,1,0,'积分变动通知','points-change','SMS_465945361','尊敬的会员，您的积分发生了变动，变动数量：${amount}，剩余积分：${balance}。祝您生活愉快！','2024-04-29 09:12:27','2024-04-29 11:59:42','','A');

/*Table structure for table `mt_staff` */

DROP TABLE IF EXISTS `mt_staff`;

CREATE TABLE `mt_staff` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `USER_ID` int DEFAULT '0' COMMENT '用户ID',
  `CATEGORY` int DEFAULT '0' COMMENT '员工类别,1:店长;2:收银员;3:销售人员;3:服务人员;',
  `MOBILE` varchar(16) NOT NULL DEFAULT '' COMMENT '手机号码',
  `REAL_NAME` varchar(30) DEFAULT '' COMMENT '真实姓名',
  `WECHAT` varchar(64) DEFAULT NULL COMMENT '微信号',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `AUDITED_STATUS` char(1) DEFAULT 'U' COMMENT '审核状态，A：审核通过；U：未审核；D：无效; ',
  `AUDITED_TIME` datetime DEFAULT NULL COMMENT '审核时间',
  `DESCRIPTION` varchar(255) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='店铺员工表';

/*Data for the table `mt_staff` */

/*Table structure for table `mt_stock` */

DROP TABLE IF EXISTS `mt_stock`;

CREATE TABLE `mt_stock` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int NOT NULL DEFAULT '0' COMMENT '店铺ID',
  `TYPE` varchar(20) NOT NULL DEFAULT 'increase' COMMENT '类型，increase:入库，reduce:出库',
  `DESCRIPTION` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '备注说明',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='库存管理记录表';

/*Data for the table `mt_stock` */

insert  into `mt_stock`(`ID`,`MERCHANT_ID`,`STORE_ID`,`TYPE`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`OPERATOR`,`STATUS`) values 
(1,1,0,'increase','','2024-05-11 04:02:26','2024-05-11 04:02:26','fuint','A'),
(2,1,0,'reduce','','2024-05-11 04:02:53','2024-05-11 04:02:53','fuint','A'),
(3,1,4,'increase','aaabbbccc','2024-05-11 04:03:37','2024-05-11 04:03:37','fuint','A');

/*Table structure for table `mt_stock_item` */

DROP TABLE IF EXISTS `mt_stock_item`;

CREATE TABLE `mt_stock_item` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `STOCK_ID` int NOT NULL DEFAULT '0' COMMENT '库存管理ID',
  `GOODS_ID` int NOT NULL DEFAULT '0' COMMENT '商品ID',
  `SKU_ID` int NOT NULL DEFAULT '0' COMMENT 'SKUID',
  `NUM` int NOT NULL DEFAULT '0' COMMENT '数量',
  `DESCRIPTION` varchar(1000) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '说明备注',
  `CREATE_TIME` datetime NOT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime NOT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'A' COMMENT '订单状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=10 DEFAULT CHARSET=utf8 COMMENT='库存管理明细表';

/*Data for the table `mt_stock_item` */

insert  into `mt_stock_item`(`ID`,`STOCK_ID`,`GOODS_ID`,`SKU_ID`,`NUM`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`OPERATOR`,`STATUS`) values 
(1,1,1,0,5,'','2024-05-11 04:02:26','2024-05-11 04:02:26','','A'),
(2,1,3,1001,12,'','2024-05-11 04:02:26','2024-05-11 04:02:26','','A'),
(3,1,3,1000,11,'','2024-05-11 04:02:26','2024-05-11 04:02:26','','A'),
(4,2,1,0,5,'','2024-05-11 04:02:53','2024-05-11 04:02:53','','A'),
(5,2,3,1001,12,'','2024-05-11 04:02:53','2024-05-11 04:02:53','','A'),
(6,2,3,1000,11,'','2024-05-11 04:02:53','2024-05-11 04:02:53','','A'),
(7,2,6,950,1,'','2024-05-11 04:02:53','2024-05-11 04:02:53','','A'),
(8,3,6,950,6,'','2024-05-11 04:03:37','2024-05-11 04:03:37','','A'),
(9,3,3,1001,6,'','2024-05-11 04:03:37','2024-05-11 04:03:37','','A');

/*Table structure for table `mt_store` */

DROP TABLE IF EXISTS `mt_store`;

CREATE TABLE `mt_store` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int unsigned DEFAULT '0' COMMENT '所属商户',
  `NAME` varchar(50) NOT NULL DEFAULT '' COMMENT '店铺名称',
  `QR_CODE` varchar(255) DEFAULT '' COMMENT '店铺二维码',
  `LOGO` varchar(100) DEFAULT '' COMMENT '店铺LOGO',
  `IS_DEFAULT` char(1) NOT NULL DEFAULT 'N' COMMENT '是否默认',
  `CONTACT` varchar(30) DEFAULT '' COMMENT '联系人姓名',
  `WX_MCH_ID` varchar(30) DEFAULT '' COMMENT '微信支付商户号',
  `WX_API_V2` varchar(32) DEFAULT '' COMMENT '微信支付APIv2密钥',
  `WX_CERT_PATH` varchar(255) DEFAULT '' COMMENT '微信支付证书',
  `ALIPAY_APP_ID` varchar(100) DEFAULT '' COMMENT '支付宝appId',
  `ALIPAY_PRIVATE_KEY` varchar(5000) DEFAULT '' COMMENT '支付宝应用私钥',
  `ALIPAY_PUBLIC_KEY` varchar(5000) DEFAULT '' COMMENT '支付宝应用公钥',
  `PHONE` varchar(20) DEFAULT '' COMMENT '联系电话',
  `ADDRESS` varchar(100) DEFAULT '' COMMENT '地址',
  `LATITUDE` varchar(30) DEFAULT '' COMMENT '经度',
  `LONGITUDE` varchar(30) DEFAULT '' COMMENT '维度',
  `DISTANCE` decimal(10,2) DEFAULT '0.00' COMMENT '距离',
  `HOURS` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '营业时间',
  `LICENSE` varchar(255) DEFAULT '' COMMENT '营业执照',
  `CREDIT_CODE` varchar(50) DEFAULT '' COMMENT '统一社会信用码',
  `BANK_NAME` varchar(100) DEFAULT '' COMMENT '银行名称',
  `BANK_CARD_NAME` varchar(100) DEFAULT '' COMMENT '银行卡账户名',
  `BANK_CARD_NO` varchar(100) DEFAULT '' COMMENT '银行卡卡号',
  `DESCRIPTION` varchar(2000) DEFAULT '' COMMENT '备注信息',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A：有效/启用；D：无效',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `DELIVERY_SUPPORTED` char(1) DEFAULT 'Y' COMMENT '是否支持外卖，Y：支持；N：不支持',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=53 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='店铺表';

/*Data for the table `mt_store` */

insert  into `mt_store`(`ID`,`MERCHANT_ID`,`NAME`,`QR_CODE`,`LOGO`,`IS_DEFAULT`,`CONTACT`,`WX_MCH_ID`,`WX_API_V2`,`WX_CERT_PATH`,`ALIPAY_APP_ID`,`ALIPAY_PRIVATE_KEY`,`ALIPAY_PUBLIC_KEY`,`PHONE`,`ADDRESS`,`LATITUDE`,`LONGITUDE`,`DISTANCE`,`HOURS`,`LICENSE`,`CREDIT_CODE`,`BANK_NAME`,`BANK_CARD_NAME`,`BANK_CARD_NO`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`STATUS`,`OPERATOR`,`DELIVERY_SUPPORTED`) values
(2,1,'海口远大路店','/uploads/********/storeQr2.png','','N','王俊凯','','','','','','','***********','海口市远大购物中心1楼','','',0.00,'','','','','','','','2020-04-26 09:27:22','2024-04-27 12:44:21','A','fuint','Y'),
(3,1,'海口永万路店','/uploads/********/storeQr3.png','/uploads/********/8a1176debd724faeab14bf66ace5264c.png','N','张易','','','','','','','***********','海口市永万路7号2楼01室','20.004229','110.273855',0.00,'9:00-22:00','/uploads/********/f80754b1973347b3832ceff604a3153f.png','123','中国邮政','123','123','海口分店','2022-01-07 16:57:42','2024-04-27 12:44:20','A','fuint','Y'),
(4,1,'海口长彤路店','/uploads/********/storeQr4.png','','Y','王辉','','','','','','','***********','海口市西海岸长彤路220号','20.01874','110.34967',0.00,'9:00-22:00','','','','','','长彤路店','2022-01-14 11:22:03','2024-04-28 11:59:47','A','fuint','Y'),
(5,1,'海口国兴路店','/uploads/20240323/storeQr5.png','','N','张易','','','','','','','13800138001','海口市国兴大道100号','110.293768','19.99326',0.00,'9:00-22:00','','','','','','海口分店','2022-02-11 13:40:35','2024-04-27 12:44:18','A','fuint','Y'),
(7,2,'海口国贸路店','/uploads/20231117/storeQr7.png','','Y','于洋','','','','','','','15641223000','海口市国贸路100号','20.01989','110.26767',0.00,'9:00-22:00','','','','','','海口分店','2022-03-28 14:10:47','2024-03-13 16:53:45','A','admin','Y'),
(8,2,'海口海甸岛店','/uploads/20231115/storeQr8.png','','N','吴清','','','','','','','0898-2688322','海口市海甸岛五西路88号','20.01129','110.34867',0.00,'9:00-22:00','','','','','','海口分店','2022-04-03 10:24:43','2023-11-15 15:48:05','A','admin','Y');

/*Table structure for table `mt_table` */

DROP TABLE IF EXISTS `mt_table`;

CREATE TABLE `mt_table` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `CODE` varchar(32) NOT NULL DEFAULT '' COMMENT '桌子编码',
  `MERCHANT_ID` int NOT NULL DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int NOT NULL DEFAULT '0' COMMENT '所属店铺ID',
  `MAX_PEOPLE` int DEFAULT '0' COMMENT '人数上限',
  `DESCRIPTION` text COMMENT '备注信息',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `OPERATOR` varchar(30) DEFAULT NULL COMMENT '最后操作人',
  `SORT` int DEFAULT '0' COMMENT '排序',
  `STATUS` char(1) DEFAULT 'A' COMMENT 'A：正常；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=4 DEFAULT CHARSET=utf8 COMMENT='桌码表';

/*Data for the table `mt_table` */

insert  into `mt_table`(`ID`,`CODE`,`MERCHANT_ID`,`STORE_ID`,`MAX_PEOPLE`,`DESCRIPTION`,`CREATE_TIME`,`UPDATE_TIME`,`OPERATOR`,`SORT`,`STATUS`) values 
(1,'A001',1,4,4,'前厅小桌1','2024-05-13 10:18:20','2024-05-13 11:23:54','fuint',1,'A'),
(2,'A002',1,4,6,'前厅A002','2024-05-13 11:20:01','2024-05-13 11:20:01','fuint',1,'A'),
(3,'A003',1,4,10,'前厅大桌003','2024-05-14 11:35:28','2024-05-14 11:35:28','fuint',3,'A');

/*Table structure for table `mt_user` */

DROP TABLE IF EXISTS `mt_user`;

CREATE TABLE `mt_user` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '会员ID',
  `MOBILE` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '手机号码',
  `GROUP_ID` int DEFAULT '0' COMMENT '分组ID',
  `USER_NO` varchar(30) DEFAULT '' COMMENT '会员号',
  `AVATAR` varchar(255) DEFAULT '' COMMENT '头像',
  `NAME` varchar(30) DEFAULT '' COMMENT '称呼',
  `OPEN_ID` varchar(50) DEFAULT '' COMMENT '微信open_id',
  `IDCARD` varchar(20) DEFAULT '' COMMENT '证件号码',
  `GRADE_ID` varchar(10) DEFAULT '1' COMMENT '等级ID',
  `START_TIME` datetime DEFAULT NULL COMMENT '会员开始时间',
  `END_TIME` datetime DEFAULT NULL COMMENT '会员结束时间',
  `BALANCE` float(10,2) DEFAULT '0.00' COMMENT '余额',
  `POINT` int DEFAULT '0' COMMENT '积分',
  `SEX` int DEFAULT '1' COMMENT '性别 1男；0女',
  `BIRTHDAY` varchar(20) DEFAULT '' COMMENT '出生日期',
  `CAR_NO` varchar(10) DEFAULT '' COMMENT '车牌号',
  `SOURCE` varchar(30) DEFAULT '' COMMENT '来源渠道',
  `PASSWORD` varchar(32) DEFAULT '' COMMENT '密码',
  `SALT` varchar(4) DEFAULT '' COMMENT 'salt',
  `ADDRESS` varchar(100) DEFAULT '' COMMENT '地址',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '所属商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '所属店铺ID',
  `IS_STAFF` char(1) DEFAULT 'N' COMMENT '是否员工',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A：激活；N：禁用；D：删除',
  `DESCRIPTION` varchar(255) DEFAULT '' COMMENT '备注信息',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  PRIMARY KEY (`ID`),
  KEY `index_phone` (`MOBILE`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='会员个人信息';

/*Data for the table `mt_user` */

insert  into `mt_user`(`ID`,`MOBILE`,`GROUP_ID`,`USER_NO`,`AVATAR`,`NAME`,`OPEN_ID`,`IDCARD`,`GRADE_ID`,`START_TIME`,`END_TIME`,`BALANCE`,`POINT`,`SEX`,`BIRTHDAY`,`CAR_NO`,`SOURCE`,`PASSWORD`,`SALT`,`ADDRESS`,`MERCHANT_ID`,`STORE_ID`,`IS_STAFF`,`CREATE_TIME`,`UPDATE_TIME`,`STATUS`,`DESCRIPTION`,`OPERATOR`) values 
(1,'',0,'8190780967550','https://thirdwx.qlogo.cn/mmopen/vi_32/POgEwh4mIHO4nibH0KlMECNjjGxQUq24ZEaGT4poC6icRiccVGKSyXwibcPq4BWmiaIGuG1icwxaQX6grC9VemZoJ8rg/132','微信用户','owOWg5cYFQr15C2QJkuAPfvMpHTQ','','1',NULL,NULL,880.00,0,2,'','','wechat_login','','','',1,4,'N','2024-08-23 16:44:42','2024-08-23 16:45:50','A','微信登录自动注册','');

/*Table structure for table `mt_user_action` */

DROP TABLE IF EXISTS `mt_user_action`;

CREATE TABLE `mt_user_action` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `USER_ID` int NOT NULL COMMENT '会员ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `ACTION` varchar(30) DEFAULT '' COMMENT '行为类别',
  `DESCRIPTION` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT '' COMMENT '备注信息',
  `PARAM` varchar(255) DEFAULT '' COMMENT '参数',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态，A：激活；N：禁用；D：删除',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  PRIMARY KEY (`ID`),
  KEY `index_user_id` (`USER_ID`,`ACTION`,`PARAM`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 COMMENT='会员行为记录表';

/*Data for the table `mt_user_action` */

insert  into `mt_user_action`(`ID`,`USER_ID`,`MERCHANT_ID`,`STORE_ID`,`ACTION`,`DESCRIPTION`,`PARAM`,`CREATE_TIME`,`UPDATE_TIME`,`STATUS`,`OPERATOR`) values 
(1,1,1,4,'login','登录系统','2024-08-23 16:44:44','2024-08-23 16:44:45','2024-08-23 16:44:45','A','');

/*Table structure for table `mt_user_coupon` */

DROP TABLE IF EXISTS `mt_user_coupon`;

CREATE TABLE `mt_user_coupon` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `CODE` varchar(32) NOT NULL DEFAULT '' COMMENT '编码',
  `TYPE` char(1) NOT NULL DEFAULT 'C' COMMENT '券类型，C优惠券；P预存卡；T集次卡',
  `IMAGE` varchar(100) DEFAULT '' COMMENT '效果图',
  `GROUP_ID` int NOT NULL DEFAULT '0' COMMENT '券组ID',
  `COUPON_ID` int NOT NULL DEFAULT '0' COMMENT '券ID',
  `MOBILE` varchar(20) DEFAULT '' COMMENT '用户手机号码',
  `USER_ID` int DEFAULT '0' COMMENT '用户ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '使用店铺ID',
  `AMOUNT` decimal(10,2) DEFAULT '0.00' COMMENT '面额',
  `BALANCE` decimal(10,2) DEFAULT '0.00' COMMENT '余额',
  `STATUS` char(1) NOT NULL DEFAULT '1' COMMENT '状态：A：未使用；B：已使用；C：已过期; D：已删除；E：未领取',
  `USED_TIME` datetime DEFAULT NULL COMMENT '使用时间',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新时间',
  `EXPIRE_TIME` datetime DEFAULT NULL COMMENT '过期时间',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `UUID` varchar(50) DEFAULT '' COMMENT '导入UUID',
  `ORDER_ID` int DEFAULT '0' COMMENT '订单ID',
  PRIMARY KEY (`ID`),
  KEY `index_user_id` (`USER_ID`),
  KEY `index_coupon_id` (`COUPON_ID`),
  KEY `index_group_id` (`GROUP_ID`) USING BTREE,
  KEY `index_code` (`CODE`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='会员卡券表';

/*Data for the table `mt_user_coupon` */

/*Table structure for table `mt_user_grade` */

DROP TABLE IF EXISTS `mt_user_grade`;

CREATE TABLE `mt_user_grade` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `GRADE` tinyint DEFAULT '1' COMMENT '等级',
  `NAME` varchar(30) DEFAULT '' COMMENT '等级名称',
  `CATCH_CONDITION` varchar(255) DEFAULT '' COMMENT '升级会员等级条件描述',
  `CATCH_TYPE` varchar(30) DEFAULT 'pay' COMMENT '升级会员等级条件，init:默认获取;pay:付费升级；frequency:消费次数；amount:累积消费金额升级',
  `CATCH_VALUE` float(10,2) DEFAULT '0.00' COMMENT '达到升级条件的值',
  `USER_PRIVILEGE` varchar(1000) DEFAULT '' COMMENT '会员权益描述',
  `VALID_DAY` int DEFAULT '0' COMMENT '有效期',
  `DISCOUNT` float(5,2) DEFAULT '0.00' COMMENT '享受折扣',
  `SPEED_POINT` float(5,2) DEFAULT '1.00' COMMENT '积分加速',
  `STATUS` char(1) DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB AUTO_INCREMENT=114 DEFAULT CHARSET=utf8 COMMENT='会员等级表';

/*Data for the table `mt_user_grade` */

insert  into `mt_user_grade`(`ID`,`MERCHANT_ID`,`GRADE`,`NAME`,`CATCH_CONDITION`,`CATCH_TYPE`,`CATCH_VALUE`,`USER_PRIVILEGE`,`VALID_DAY`,`DISCOUNT`,`SPEED_POINT`,`STATUS`) values 
(1,1,1,'普通会员','默认取得','init',0.00,'基础会员',0,0.00,0.00,'A'),
(2,1,2,'铜牌会员','铜牌会员','pay',99.00,'铜牌会员',7,9.00,0.00,'A'),
(4,1,3,'银牌会员','付费升级','pay',199.00,'银牌会员',365,8.50,0.00,'A'),
(5,1,4,'金牌会员','付费升级','pay',299.00,'1、9折，2、双倍积分',365,8.00,2.00,'A'),
(6,1,5,'钻牌会员','付费升级','pay',5001.00,'1、8折，2、5倍积分',100,5.00,3.00,'D');

/*Table structure for table `mt_user_group` */

DROP TABLE IF EXISTS `mt_user_group`;

CREATE TABLE `mt_user_group` (
  `ID` int NOT NULL AUTO_INCREMENT COMMENT '自增ID',
  `MERCHANT_ID` int DEFAULT '0' COMMENT '商户ID',
  `STORE_ID` int DEFAULT '0' COMMENT '店铺ID',
  `NAME` varchar(100) NOT NULL DEFAULT '' COMMENT '分组名称',
  `PARENT_ID` int DEFAULT '0' COMMENT '父ID',
  `DESCRIPTION` varchar(2000) DEFAULT '' COMMENT '备注',
  `CREATE_TIME` datetime DEFAULT NULL COMMENT '创建日期',
  `UPDATE_TIME` datetime DEFAULT NULL COMMENT '更新日期',
  `OPERATOR` varchar(30) DEFAULT '' COMMENT '最后操作人',
  `STATUS` char(1) NOT NULL DEFAULT 'A' COMMENT 'A：正常；D：删除',
  PRIMARY KEY (`ID`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='会员分组';

/*Data for the table `mt_user_group` */

/*Table structure for table `mt_verify_code` */

DROP TABLE IF EXISTS `mt_verify_code`;

CREATE TABLE `mt_verify_code` (
  `ID` bigint NOT NULL AUTO_INCREMENT COMMENT '自增id',
  `MOBILE` varchar(16) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '手机号',
  `VERIFY_CODE` char(6) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '验证码',
  `ADD_TIME` datetime DEFAULT NULL COMMENT '创建时间',
  `EXPIRE_TIME` datetime DEFAULT NULL COMMENT '过期时间',
  `USED_TIME` datetime DEFAULT NULL COMMENT '使用时间',
  `VALID_FLAG` char(1) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '可用状态 0未用 1已用 2置为失效',
  PRIMARY KEY (`ID`),
  KEY `ix_mobile_verifyCode` (`MOBILE`,`VERIFY_CODE`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='短信验证码表';

/*Data for the table `mt_verify_code` */

/*Table structure for table `t_account` */

DROP TABLE IF EXISTS `t_account`;

CREATE TABLE `t_account` (
  `acct_id` int NOT NULL AUTO_INCREMENT COMMENT '主键id',
  `account_key` varchar(23) NOT NULL DEFAULT '' COMMENT '账户编码',
  `account_name` varchar(20) NOT NULL DEFAULT '' COMMENT '账户名称',
  `password` varchar(100) NOT NULL DEFAULT '' COMMENT '密码',
  `account_status` int NOT NULL DEFAULT '1' COMMENT '0 无效 1 有效',
  `is_active` int NOT NULL DEFAULT '0' COMMENT '0 未激活 1已激活',
  `create_date` datetime NOT NULL COMMENT '创建时间',
  `modify_date` datetime NOT NULL COMMENT '修改时间',
  `salt` varchar(64) NOT NULL DEFAULT '' COMMENT '随机码',
  `role_ids` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '角色ID',
  `locked` int NOT NULL DEFAULT '0' COMMENT '是否禁用',
  `owner_id` int DEFAULT NULL COMMENT '所属平台',
  `real_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '姓名',
  `merchant_id` int DEFAULT '0' COMMENT '所属商户ID',
  `store_id` int DEFAULT '0' COMMENT '所属店铺ID',
  `staff_id` int DEFAULT '0' COMMENT '关联员工ID',
  PRIMARY KEY (`acct_id`),
  KEY `FKmlsqc08c6khxhoed7abkl2s9l` (`owner_id`)
) ENGINE=InnoDB AUTO_INCREMENT=146 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

/*Data for the table `t_account` */

insert  into `t_account`(`acct_id`,`account_key`,`account_name`,`password`,`account_status`,`is_active`,`create_date`,`modify_date`,`salt`,`role_ids`,`locked`,`owner_id`,`real_name`,`merchant_id`,`store_id`,`staff_id`) values 
(1,'20230714677851484251776','fuint','cb4c1e2741076af41c548e888fe3f2be9e5e69d8',1,1,'2019-10-25 15:54:17','2024-04-15 15:37:01','405c73e643551163','2',0,NULL,'管理员',1,0,46),
(2,'20231016340951724856742','admin','cb4c1e2741076af41c548e888fe3f2be9e5e69d8',1,1,'2021-10-12 22:19:32','2023-10-13 15:21:25','405c73e643551163','2',0,1,'超管',0,0,0),
(3,'20231016344347831674930','store','5764050c8d624f4cd086ba10e9c8f8714d0749c8',1,1,'2021-10-16 20:39:43','2024-03-12 20:55:10','3c2bfcc920186f86','2',0,NULL,'店铺账号',1,2,4);

/*Table structure for table `t_account_duty` */

DROP TABLE IF EXISTS `t_account_duty`;

CREATE TABLE `t_account_duty` (
  `acc_duty_id` int NOT NULL AUTO_INCREMENT COMMENT '账户角色ID',
  `acct_id` int NOT NULL COMMENT '账户ID',
  `duty_id` int NOT NULL COMMENT '角色ID',
  PRIMARY KEY (`acc_duty_id`),
  KEY `FKcym10gcigo2c175iqqjj7xu5h` (`acct_id`),
  KEY `FKpfts0wq2y4xhq9vv2g7uo1kr0` (`duty_id`)
) ENGINE=InnoDB AUTO_INCREMENT=1009 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

/*Data for the table `t_account_duty` */

insert  into `t_account_duty`(`acc_duty_id`,`acct_id`,`duty_id`) values 
(259,5,2),
(281,8,2),
(295,11,2),
(300,12,2),
(303,13,2),
(304,13,7),
(305,13,8),
(306,14,8),
(314,15,2),
(315,15,8),
(316,15,7),
(324,16,7),
(332,17,8),
(335,19,7),
(336,20,7),
(337,20,2),
(338,20,8),
(357,21,8),
(360,22,7),
(361,18,8),
(364,23,7),
(365,24,7),
(367,25,8),
(368,25,2),
(369,25,7),
(374,28,8),
(375,29,7),
(385,30,10),
(391,31,7),
(392,31,8),
(393,31,10),
(397,32,7),
(404,35,8),
(405,26,7),
(406,26,8),
(407,27,8),
(417,36,11),
(418,37,2),
(419,34,7),
(431,33,8),
(433,39,2),
(457,41,8),
(458,40,8),
(459,38,7),
(460,42,8),
(462,43,8),
(488,44,8),
(489,46,8),
(490,47,7),
(492,48,12),
(496,49,2),
(497,49,7),
(498,49,8),
(499,49,10),
(500,49,11),
(501,49,12),
(502,50,7),
(503,51,10),
(505,52,13),
(506,53,2),
(507,53,7),
(510,45,2),
(511,45,7),
(512,45,8),
(513,45,10),
(514,45,11),
(515,45,12),
(516,55,2),
(517,55,7),
(518,55,8),
(519,55,10),
(520,55,11),
(521,55,12),
(522,55,13),
(523,55,14),
(524,56,7),
(525,56,8),
(526,56,10),
(527,56,11),
(528,56,12),
(529,56,13),
(530,56,14),
(531,57,11),
(533,54,14),
(542,58,8),
(543,59,7),
(544,60,13),
(548,61,7),
(549,61,13),
(579,63,2),
(580,62,8),
(589,64,7),
(590,64,8),
(596,65,8),
(612,70,2),
(613,70,7),
(614,72,2),
(615,72,7),
(616,72,8),
(617,72,10),
(618,72,11),
(619,72,12),
(620,72,13),
(621,72,14),
(622,72,18),
(623,72,19),
(624,74,8),
(659,77,19),
(664,69,13),
(665,78,7),
(670,80,8),
(671,84,2),
(716,87,7),
(717,87,8),
(728,88,8),
(748,89,7),
(749,90,10),
(750,91,10),
(753,4,2),
(754,93,7),
(757,94,7),
(773,96,12),
(774,92,10),
(777,98,7),
(778,98,8),
(780,2,2),
(781,2,7),
(782,2,8),
(786,97,12),
(810,105,10),
(814,106,10),
(815,106,11),
(816,107,7),
(825,108,10),
(828,109,7),
(829,99,22),
(830,110,25),
(833,111,26),
(835,112,2),
(836,113,2),
(837,114,10),
(859,117,7),
(869,102,10),
(879,118,7),
(880,118,8),
(891,115,7),
(897,121,7),
(898,119,22),
(899,116,22),
(902,120,28),
(905,123,7),
(908,101,10),
(913,122,7),
(914,124,8),
(915,125,8),
(916,126,10),
(917,126,12),
(918,127,2),
(919,127,7),
(920,127,8),
(921,127,10),
(922,127,11),
(923,127,12),
(924,128,7),
(925,128,10),
(926,128,11),
(927,128,12),
(929,129,7),
(930,3,8),
(931,3,10),
(932,104,10),
(933,104,11),
(934,104,12),
(949,132,11),
(950,131,2),
(951,131,7),
(952,131,8),
(953,131,10),
(954,131,11),
(955,131,12),
(957,130,12),
(958,133,12),
(965,135,2),
(966,135,7),
(967,135,8),
(968,135,10),
(969,135,11),
(970,135,12),
(979,138,11),
(980,137,12),
(987,1,7),
(988,1,10),
(989,1,11),
(990,1,12),
(991,139,21),
(992,136,2),
(993,136,7),
(994,136,8),
(995,136,10),
(996,136,11),
(997,136,12),
(998,140,12),
(1003,141,11),
(1004,142,8),
(1005,143,7),
(1006,144,8),
(1007,145,22),
(1008,100,12);

/*Table structure for table `t_action_log` */

DROP TABLE IF EXISTS `t_action_log`;

CREATE TABLE `t_action_log` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `merchant_id` int DEFAULT '0' COMMENT '商户ID',
  `store_id` int DEFAULT '0' COMMENT '店铺ID',
  `action_time` datetime DEFAULT NULL COMMENT '操作时间',
  `time_consuming` decimal(11,0) DEFAULT NULL COMMENT '耗时',
  `client_ip` varchar(50) DEFAULT NULL COMMENT '客户端IP',
  `module` varchar(255) DEFAULT NULL COMMENT '操作模块',
  `url` varchar(255) DEFAULT NULL COMMENT '请求URL',
  `acct_name` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci DEFAULT NULL COMMENT '操作用户账户',
  `user_agent` varchar(255) DEFAULT NULL COMMENT '用户系统以及浏览器信息',
  `client_port` int DEFAULT NULL COMMENT '端口号',
  `param` text COMMENT '参数',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

/*Data for the table `t_action_log` */

/*Table structure for table `t_duty` */

DROP TABLE IF EXISTS `t_duty`;

CREATE TABLE `t_duty` (
  `merchant_id` int DEFAULT '0' COMMENT '商户ID',
  `duty_id` int NOT NULL AUTO_INCREMENT COMMENT '角色ID',
  `duty_name` varchar(240) DEFAULT NULL COMMENT '角色名称',
  `status` varchar(6) NOT NULL COMMENT '状态(A: 可用  D: 禁用)',
  `description` varchar(400) DEFAULT NULL COMMENT '描述',
  `duty_type` varchar(50) NOT NULL COMMENT '角色类型',
  PRIMARY KEY (`duty_id`)
) ENGINE=InnoDB AUTO_INCREMENT=23 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='角色表';

/*Data for the table `t_duty` */

insert  into `t_duty`(`merchant_id`,`duty_id`,`duty_name`,`status`,`description`,`duty_type`) values 
(0,2,'系统管理员','A','系统管理员','1'),
(0,7,'商户管理员','A','商户管理员','2'),
(0,8,'店铺管理员','A','店铺管理员','2'),
(1,10,'店铺服务员','A','店铺服务员','3'),
(1,11,'店铺销售员','A','店铺销售员','3'),
(1,12,'店铺店长','A','店铺店长','3');

/*Table structure for table `t_duty_source` */

DROP TABLE IF EXISTS `t_duty_source`;

CREATE TABLE `t_duty_source` (
  `duty_source_id` int NOT NULL AUTO_INCREMENT,
  `duty_id` int DEFAULT NULL,
  `source_id` int DEFAULT NULL,
  PRIMARY KEY (`duty_source_id`),
  KEY `FKlciudb88j4tptc36d43ghl5dg` (`duty_id`),
  KEY `FKp1c59mwxgjue4qdl86sd6dogf` (`source_id`)
) ENGINE=InnoDB AUTO_INCREMENT=14604 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

/*Data for the table `t_duty_source` */

insert  into `t_duty_source`(`duty_source_id`,`duty_id`,`source_id`) values 
(9387,10,61),
(9388,10,79),
(9389,10,80),
(9390,10,103),
(9391,10,108),
(9392,10,110),
(9393,10,122),
(9394,10,123),
(12284,11,126),
(12285,11,125),
(12286,11,178),
(12287,11,124),
(12879,21,177),
(12880,21,140),
(12881,21,182),
(12882,21,138),
(12883,21,86),
(12884,21,84),
(12885,21,83),
(12886,21,80),
(12887,21,61),
(12888,21,85),
(12889,21,146),
(12890,21,139),
(12891,21,115),
(12892,21,100),
(12893,21,79),
(12894,21,16),
(12895,21,50),
(12896,21,103),
(12897,21,54),
(12898,21,69),
(12899,21,108),
(12900,21,81),
(12901,21,110),
(12902,21,122),
(12903,21,123),
(12904,21,78),
(12905,21,97),
(12906,21,1),
(12907,21,137),
(12908,21,19),
(12909,21,18),
(12910,21,17),
(12911,21,15),
(12912,21,2),
(13326,22,117),
(13327,22,160),
(13328,22,112),
(13329,22,158),
(13330,22,177),
(13331,22,181),
(13332,22,140),
(13333,22,102),
(13334,22,188),
(13335,22,189),
(13336,22,107),
(13337,22,86),
(13338,22,138),
(13339,22,106),
(13340,22,84),
(13341,22,101),
(13342,22,83),
(13343,22,80),
(13344,22,159),
(13345,22,161),
(13346,22,113),
(13347,22,49),
(13348,22,182),
(13349,22,118),
(13350,22,5),
(13351,22,120),
(13352,22,61),
(13353,22,185),
(13354,22,186),
(13355,22,115),
(13356,22,109),
(13357,22,105),
(13358,22,121),
(13359,22,139),
(13360,22,100),
(13361,22,146),
(13362,22,85),
(13363,22,79),
(13364,22,16),
(13365,22,183),
(13366,22,136),
(13367,22,135),
(13368,22,131),
(13369,22,165),
(13370,22,179),
(13371,22,127),
(13372,22,187),
(13373,22,190),
(13374,22,50),
(13375,22,54),
(13376,22,69),
(13377,22,103),
(13378,22,128),
(13379,22,81),
(13380,22,132),
(13381,22,108),
(13382,22,123),
(13383,22,78),
(13384,22,96),
(13385,22,110),
(13386,22,111),
(13387,22,129),
(13388,22,122),
(13389,22,97),
(13390,22,130),
(13391,22,116),
(13392,22,104),
(13393,22,1),
(13394,22,180),
(13395,22,184),
(13396,22,137),
(13397,22,18),
(13398,22,19),
(13399,22,17),
(13400,22,15),
(13401,22,9),
(13402,22,7),
(13403,22,6),
(13404,22,4),
(13405,22,3),
(13406,22,2),
(14285,12,140),
(14286,12,102),
(14287,12,93),
(14288,12,158),
(14289,12,119),
(14290,12,112),
(14291,12,117),
(14292,12,120),
(14293,12,138),
(14294,12,106),
(14295,12,84),
(14296,12,5),
(14297,12,91),
(14298,12,86),
(14299,12,113),
(14300,12,101),
(14301,12,107),
(14302,12,118),
(14303,12,49),
(14304,12,83),
(14305,12,80),
(14306,12,125),
(14307,12,61),
(14308,12,121),
(14309,12,79),
(14310,12,115),
(14311,12,100),
(14312,12,85),
(14313,12,109),
(14314,12,105),
(14315,12,16),
(14316,12,139),
(14317,12,146),
(14318,12,136),
(14319,12,135),
(14320,12,127),
(14321,12,69),
(14322,12,54),
(14323,12,103),
(14324,12,50),
(14325,12,131),
(14326,12,193),
(14327,12,133),
(14328,12,197),
(14329,12,108),
(14330,12,124),
(14331,12,128),
(14332,12,132),
(14333,12,122),
(14334,12,123),
(14335,12,110),
(14336,12,129),
(14337,12,111),
(14338,12,78),
(14339,12,96),
(14340,12,104),
(14341,12,130),
(14342,12,97),
(14343,12,134),
(14344,12,90),
(14345,12,116),
(14346,12,195),
(14347,12,1),
(14348,12,196),
(14349,12,137),
(14350,12,18),
(14351,12,17),
(14352,12,15),
(14353,12,9),
(14354,12,7),
(14355,12,6),
(14356,12,4),
(14357,12,3),
(14358,12,2),
(14359,12,19),
(14360,2,93),
(14361,2,102),
(14362,2,112),
(14363,2,117),
(14364,2,119),
(14365,2,126),
(14366,2,140),
(14367,2,154),
(14368,2,158),
(14369,2,160),
(14370,2,177),
(14371,2,181),
(14372,2,188),
(14373,2,189),
(14374,2,198),
(14375,2,5),
(14376,2,49),
(14377,2,80),
(14378,2,83),
(14379,2,84),
(14380,2,86),
(14381,2,91),
(14382,2,101),
(14383,2,106),
(14384,2,107),
(14385,2,113),
(14386,2,118),
(14387,2,120),
(14388,2,138),
(14389,2,155),
(14390,2,159),
(14391,2,161),
(14392,2,182),
(14393,2,61),
(14394,2,125),
(14395,2,185),
(14396,2,186),
(14397,2,153),
(14398,2,16),
(14399,2,79),
(14400,2,85),
(14401,2,100),
(14402,2,105),
(14403,2,109),
(14404,2,115),
(14405,2,121),
(14406,2,139),
(14407,2,146),
(14408,2,183),
(14409,2,50),
(14410,2,54),
(14411,2,69),
(14412,2,103),
(14413,2,127),
(14414,2,131),
(14415,2,135),
(14416,2,136),
(14417,2,165),
(14418,2,178),
(14419,2,179),
(14420,2,187),
(14421,2,190),
(14422,2,192),
(14423,2,193),
(14424,2,81),
(14425,2,108),
(14426,2,124),
(14427,2,128),
(14428,2,132),
(14429,2,133),
(14430,2,197),
(14431,2,78),
(14432,2,96),
(14433,2,110),
(14434,2,111),
(14435,2,122),
(14436,2,123),
(14437,2,129),
(14438,2,194),
(14439,2,90),
(14440,2,97),
(14441,2,104),
(14442,2,116),
(14443,2,130),
(14444,2,134),
(14445,2,195),
(14446,2,1),
(14447,2,196),
(14448,2,180),
(14449,2,137),
(14450,2,184),
(14451,2,2),
(14452,2,3),
(14453,2,4),
(14454,2,6),
(14455,2,7),
(14456,2,9),
(14457,2,15),
(14458,2,17),
(14459,2,18),
(14460,2,19),
(14461,7,93),
(14462,7,154),
(14463,7,112),
(14464,7,117),
(14465,7,140),
(14466,7,102),
(14467,7,189),
(14468,7,188),
(14469,7,181),
(14470,7,119),
(14471,7,126),
(14472,7,160),
(14473,7,158),
(14474,7,198),
(14475,7,113),
(14476,7,118),
(14477,7,80),
(14478,7,155),
(14479,7,5),
(14480,7,83),
(14481,7,84),
(14482,7,120),
(14483,7,86),
(14484,7,138),
(14485,7,91),
(14486,7,159),
(14487,7,106),
(14488,7,49),
(14489,7,161),
(14490,7,182),
(14491,7,101),
(14492,7,107),
(14493,7,185),
(14494,7,125),
(14495,7,61),
(14496,7,186),
(14497,7,153),
(14498,7,85),
(14499,7,79),
(14500,7,121),
(14501,7,115),
(14502,7,183),
(14503,7,100),
(14504,7,16),
(14505,7,105),
(14506,7,139),
(14507,7,109),
(14508,7,146),
(14509,7,193),
(14510,7,127),
(14511,7,131),
(14512,7,135),
(14513,7,136),
(14514,7,165),
(14515,7,178),
(14516,7,179),
(14517,7,187),
(14518,7,50),
(14519,7,54),
(14520,7,69),
(14521,7,103),
(14522,7,192),
(14523,7,132),
(14524,7,124),
(14525,7,81),
(14526,7,133),
(14527,7,128),
(14528,7,108),
(14529,7,197),
(14530,7,96),
(14531,7,78),
(14532,7,110),
(14533,7,122),
(14534,7,123),
(14535,7,129),
(14536,7,111),
(14537,7,194),
(14538,7,134),
(14539,7,90),
(14540,7,97),
(14541,7,116),
(14542,7,104),
(14543,7,130),
(14544,7,195),
(14545,7,1),
(14546,7,196),
(14547,7,180),
(14548,7,137),
(14549,7,184),
(14550,7,3),
(14551,7,19),
(14552,7,18),
(14553,7,17),
(14554,7,15),
(14555,7,9),
(14556,7,7),
(14557,7,6),
(14558,7,4),
(14559,7,2),
(14560,8,117),
(14561,8,126),
(14562,8,119),
(14563,8,112),
(14564,8,181),
(14565,8,198),
(14566,8,91),
(14567,8,107),
(14568,8,118),
(14569,8,80),
(14570,8,84),
(14571,8,86),
(14572,8,113),
(14573,8,61),
(14574,8,125),
(14575,8,105),
(14576,8,109),
(14577,8,79),
(14578,8,146),
(14579,8,183),
(14580,8,54),
(14581,8,69),
(14582,8,50),
(14583,8,131),
(14584,8,135),
(14585,8,178),
(14586,8,179),
(14587,8,193),
(14588,8,124),
(14589,8,132),
(14590,8,197),
(14591,8,78),
(14592,8,110),
(14593,8,129),
(14594,8,96),
(14595,8,111),
(14596,8,90),
(14597,8,104),
(14598,8,130),
(14599,8,97),
(14600,8,116),
(14601,8,195),
(14602,8,196),
(14603,8,180);

/*Table structure for table `t_gen_code` */

DROP TABLE IF EXISTS `t_gen_code`;

CREATE TABLE `t_gen_code` (
  `id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `service_name` varchar(64) DEFAULT NULL COMMENT '服务名称',
  `module_name` varchar(64) DEFAULT NULL COMMENT '模块名称',
  `table_name` varchar(64) DEFAULT NULL COMMENT '表名',
  `table_prefix` varchar(64) DEFAULT NULL COMMENT '表前缀',
  `pk_name` varchar(32) DEFAULT NULL COMMENT '主键名',
  `package_name` varchar(500) DEFAULT NULL COMMENT '后端包名',
  `backend_path` varchar(2000) DEFAULT NULL COMMENT '后端路径',
  `front_path` varchar(2000) DEFAULT NULL COMMENT '前端路径',
  `author` varchar(30) DEFAULT NULL COMMENT '作者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `status` char(1) DEFAULT 'A' COMMENT '状态',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=5 DEFAULT CHARSET=utf8 COMMENT='代码生成表';

/*Data for the table `t_gen_code` */

insert  into `t_gen_code`(`id`,`service_name`,`module_name`,`table_name`,`table_prefix`,`pk_name`,`package_name`,`backend_path`,`front_path`,`author`,`create_time`,`update_time`,`status`) values 
(2,'Banner','焦点图','banner','mt_','id','banner','E:\\Code\\genCode\\fuintBackend','C:\\Code\\genCode\\front','FSQ','2024-04-08 10:57:42','2024-04-30 10:03:17','A');

/*Table structure for table `t_platform` */

DROP TABLE IF EXISTS `t_platform`;

CREATE TABLE `t_platform` (
  `owner_id` int NOT NULL AUTO_INCREMENT COMMENT '主键',
  `name` varchar(100) NOT NULL COMMENT '平台名称',
  `status` int NOT NULL COMMENT '状态 0 无效 1 有效',
  `description` varchar(255) DEFAULT NULL COMMENT '描述',
  `platform_type` int NOT NULL COMMENT '平台类型',
  PRIMARY KEY (`owner_id`)
) ENGINE=InnoDB AUTO_INCREMENT=2 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT;

/*Data for the table `t_platform` */

insert  into `t_platform`(`owner_id`,`name`,`status`,`description`,`platform_type`) values 
(1,'会员营销管理系统',1,'会员营销管理系统说明',1);

/*Table structure for table `t_source` */

DROP TABLE IF EXISTS `t_source`;

CREATE TABLE `t_source` (
  `merchant_id` int DEFAULT '1' COMMENT '商户ID',
  `source_id` int NOT NULL AUTO_INCREMENT COMMENT '菜单Id',
  `source_name` varchar(240) NOT NULL COMMENT '菜单名称',
  `source_code` varchar(200) NOT NULL COMMENT '菜单对应url',
  `path` varchar(255) DEFAULT '' COMMENT '路径',
  `ename` varchar(100) DEFAULT '' COMMENT '字母名称',
  `new_icon` varchar(30) DEFAULT '' COMMENT '新图标',
  `status` varchar(6) NOT NULL COMMENT '状态(A:可用 D:禁用)',
  `source_level` int NOT NULL COMMENT '菜单级别',
  `source_style` varchar(40) NOT NULL COMMENT '样式',
  `is_menu` int NOT NULL COMMENT '是否显示',
  `description` varchar(400) DEFAULT NULL COMMENT '描述',
  `parent_id` int DEFAULT NULL COMMENT '上级菜单ID',
  `is_log` int DEFAULT NULL,
  `icon` varchar(20) DEFAULT NULL COMMENT '菜单图标',
  PRIMARY KEY (`source_id`),
  KEY `index-name` (`source_name`,`parent_id`),
  KEY `index-parent-id` (`parent_id`)
) ENGINE=InnoDB AUTO_INCREMENT=199 DEFAULT CHARSET=utf8 ROW_FORMAT=COMPACT COMMENT='菜单表';

/*Data for the table `t_source` */

insert  into `t_source`(`merchant_id`,`source_id`,`source_name`,`source_code`,`path`,`ename`,`new_icon`,`status`,`source_level`,`source_style`,`is_menu`,`description`,`parent_id`,`is_log`,`icon`) values 
(0,1,'后台管理员','system/account/index','system/account/index','SystemAccountIndex','select','A',2,'7',1,'管理员管理',15,1,NULL),
(0,2,'新增用户','/user/add','system/account/add','SystemAccountAdd','select','A',3,'99',0,'',1,1,NULL),
(0,3,'修改用户','/user/edit','system/account/edit','SystemAccountEdit','select','A',3,'99',0,'',1,1,NULL),
(0,4,'删除用户','/user/delete','system/account/delete','SystemAccountDelete','select','A',3,'99',0,'',1,1,NULL),
(0,5,'后台菜单','system/menu/index','system/menu/index','SystemMenuIndex','select','A',2,'1',1,NULL,15,1,NULL),
(0,6,'新增菜单','system/menu/add','system/menu/add','SystemMenuAdd','select','A',3,'99',0,'',5,1,NULL),
(0,7,'修改菜单','/source/edit','system/menu/edit','SystemMenuEdit','select','A',3,'99',0,'修改菜单',5,1,NULL),
(0,9,'删除菜单','/source/delete','system/menu/delete','SystemMenuDelete','select','A',3,'99',0,'删除菜单',5,1,NULL),
(0,15,'系统管理','system','system','System','system','A',1,'99',1,NULL,NULL,1,'cog'),
(0,16,'后台角色','system/role/index','system/role/index','SystemRoleIndex','select','A',2,'2',1,NULL,15,1,NULL),
(0,17,'新增角色','/duty/add','system/role/add','SystemRoleAdd','select','A',3,'99',0,'',16,1,NULL),
(0,18,'修改角色','/source/edit','system/role/edit','SystemRoleEdit','select','A',3,'99',0,'',16,1,NULL),
(0,19,'删除角色','/source/delete','system/role/delete','SystemRoleDelete','select','A',3,'99',0,'',16,1,NULL),
(0,49,'后台日志','system/logs/index','system/logs/index','SystemLogsIndex','select','A',2,'1',1,'后台操作日志',15,1,NULL),
(0,50,'卡券管理','######','coupon','Coupon','job','A',1,'3',1,'卡券管理',NULL,0,'ticket'),
(0,54,'会员卡券','/backend/userCoupon/list','coupon/userCoupon/index','CouponUserCouponIndex','select','A',2,'3',1,'会员卡券明细列表',50,1,NULL),
(0,61,'新增会员','member/add','member/add','MemberAdd','select','A',2,'10',0,'新增会员',79,1,NULL),
(0,69,'员工管理','/backend/staff/queryList','staff/list','StaffList','select','A',2,'3',1,'店铺员工管理',84,0,NULL),
(0,78,'发券记录','/backend/sendLog/index','coupon/sendLog/index','CouponSendLogIndex','select','A',2,'5',1,'发券记录',50,1,NULL),
(0,79,'会员管理','######','member','Member','peoples','A',1,'2',1,'会员管理',NULL,0,'user'),
(0,80,'会员列表','/backend/member/queryList','member/index','MemberIndex','select','A',2,'1',1,'会员列表',79,1,NULL),
(0,81,'核销流水','/backend/confirmLog/confirmLogList','coupon/confirmLog/index','CouponConfirmLogIndex','select','A',2,'4',1,'核销记录列表',50,1,NULL),
(0,83,'分组管理','/backend/couponGroup/index','coupon/group/index','CouponGroupIndex','select','A',2,'1',1,'分组管理',50,1,NULL),
(0,84,'店铺管理','######','store','Store','shopping','A',1,'1',1,'店铺管理',NULL,0,'columns'),
(0,85,'新增店铺','store/add','store/add','StoreAdd','select','A',3,'2',0,'新增店铺信息',86,1,NULL),
(0,86,'店铺列表','/backend/store/queryList','store/list','StoreList','select','A',2,'1',1,'店铺列表',84,0,NULL),
(0,90,'消息管理','######','message','Message','message','A',1,'6',1,'消息管理',NULL,0,'tablet'),
(0,91,'已发短信','/backend/smsManager/index','smsManager/index','SmsManagerIndex','select','A',2,'1',1,'已发短信列表',90,0,NULL),
(0,93,'短信模板','/backend/smsTemplate/index','smsTemplate/index','SmsTemplateIndex','select','A',2,'0',1,'短信模板',90,0,NULL),
(0,96,'订单管理','######','order','Order','list','A',1,'5',1,'订单管理',NULL,0,'list'),
(0,97,'转赠记录','/backend/give/index','coupon/give/index','CouponGiveIndex','select','A',2,'6',1,'转赠记录',50,1,NULL),
(0,100,'卡券列表','/backend/coupon/index','coupon/coupon/index','CouponCouponIndex','select','A',2,'2',1,'卡券列表',50,1,NULL),
(0,101,'内容管理','######','content','Content','edit','A',1,'1',1,'内容管理',NULL,1,'book'),
(0,102,'轮播管理','content/banner/list','content/banner/list','ContentBannerList','select','A',2,'0',1,'首页广告',101,1,NULL),
(0,103,'会员等级','/backend/userGrade/queryList','userGrade/index','UserGradeIndex','select','A',2,'3',1,'会员等级',79,1,NULL),
(0,104,'积分管理','######','point','Point','log','A',1,'6',1,'积分管理',NULL,1,'file'),
(0,105,'积分明细','/backend/point/index','point/list','PointList','select','A',2,'2',1,'积分明细',104,1,NULL),
(0,106,'积分设置','/backend/point/setting','point/setting','PointSetting','select','A',2,'1',1,'积分设置',104,0,NULL),
(0,107,'订单列表','/backend/order/list','order/index','OrderIndex','select','A',2,'1',1,'订单列表',96,1,NULL),
(0,108,'开卡赠礼','/backend/openGift/list','openGift/index','OpenGiftIndex','select','A',2,'4',1,'开卡礼设置',79,1,NULL),
(0,109,'售后订单','/backend/refund/index','refund/index','RefundIndex','select','A',2,'2',1,'售后订单',96,0,NULL),
(0,110,'会员设置','/backend/member/setting','member/setting','MemberSetting','select','A',2,'5',1,'会员设置',79,0,NULL),
(0,111,'商品管理','######','Goods','Goods','server','A',1,'5',1,'商品管理',NULL,0,'shopping-cart'),
(0,112,'商品分类','/backend/goods/cate/list','goods/cate/index','GoodsCateIndex','select','A',2,'0',1,'商品分类',111,1,NULL),
(0,113,'商品列表','/backend/goods/goods/list','goods/goods/index','GoodsGoodsIndex','select','A',2,'1',1,'商品列表',111,1,NULL),
(0,115,'卡券核销','/backend/home/<USER>','coupon/confirm/index','CouponConfirmIndex','select','A',2,'2',1,'卡券核销',50,1,NULL),
(0,116,'充值管理','recharge','recharge','Recharge','money','A',1,'6',1,'余额',NULL,1,'money'),
(0,117,'充值设置','/backend/balance/setting','balance/setting','BalanceSetting','select','A',2,'0',1,'充值设置',116,1,NULL),
(0,118,'余额明细','balance/list','balance/list','BalanceList','select','A',2,'1',1,'充值明细',116,1,NULL),
(0,119,'订阅消息','/backend/subMessage/index','subMessage/index','SubMessageIndex','select','A',2,'0',1,'小程序订阅消息',90,1,NULL),
(0,120,'轮播图编辑','content/banner/edit','content/banner/edit','ContentBannerEdit','select','A',2,'1',0,NULL,101,1,NULL),
(0,121,'新增轮播图','content:banner:add','content:banner:add','Content:banner:add','select','A',2,'2',0,NULL,101,1,NULL),
(0,122,'新增会员等级','/backend/userGrade/add','userGrade/add','UserGradeAdd','select','A',2,'5',0,NULL,79,1,NULL),
(0,123,'新增开卡赠礼','/backend/openGift/add','openGift/add','OpenGiftAdd','select','A',2,'5',0,NULL,79,1,NULL),
(0,124,'收银台','cashier/index','cashier/index','CashierIndex','select','A',2,'4',1,NULL,125,1,NULL),
(0,125,'收银管理','cashier','cashier','Cashier','tab','A',1,'10',0,NULL,NULL,1,NULL),
(0,126,'核销订单','cashier/confirmOrder','cashier/confirmOrder','CashierConfirmOrder','select','A',2,'0',1,NULL,125,1,NULL),
(0,127,'订单修改','/backend/order/edit','order/edit','OrderEdit','select','A',2,'3',0,NULL,96,1,NULL),
(0,128,'订单删除','/backend/order/delete','order/delete','OrderDelete','select','A',2,'4',0,NULL,96,1,NULL),
(0,129,'处理售后','/backend/refund/edit','refund/edit','RefundEdit','select','A',2,'5',0,NULL,96,1,NULL),
(0,130,'订单详情','/backend/order/detail','order/detail','OrderDetail','select','A',2,'6',0,NULL,96,1,NULL),
(0,131,'添加商品','goods/goods/add','goods/goods/add','GoodsGoodsAdd','select','A',2,'3',0,NULL,111,1,NULL),
(0,132,'编辑商品','goods/goods/edit','goods/goods/edit','GoodsGoodsEdit','select','A',2,'4',0,NULL,111,1,NULL),
(0,133,'编辑短信模板','smsTemplate/edit','smsTemplate/edit','SmsTemplateEdit','select','A',2,'4',0,NULL,90,1,NULL),
(0,134,'编辑订阅消息','subMessage/edit','subMessage/edit','SubMessageEdit','select','A',2,'6',0,NULL,90,1,NULL),
(0,135,'变更余额','balance/modify','balance/modify','BalanceModify','select','A',3,'3',0,NULL,117,1,NULL),
(0,136,'变更积分','point/modify','point/modify','PointModify','select','A',2,'3',0,NULL,104,1,NULL),
(0,137,'编辑卡券分组','coupon/group/edit','coupon/group/edit','CouponGroupEdit','select','A',3,'9',0,NULL,83,1,NULL),
(0,138,'新增卡券','coupon/coupon/add','coupon/coupon/add','CouponCouponAdd','select','A',3,'1',0,NULL,100,1,NULL),
(0,139,'编辑卡券','coupon/coupon/edit','coupon/coupon/edit','CouponCouponEdit','select','A',3,'2',0,NULL,100,1,NULL),
(0,140,'作废会员卡券','coupon/userCoupon/delete','coupon/userCoupon/delete','CouponUserCouponDelete','select','A',3,'0',0,NULL,54,1,NULL),
(0,146,'支付设置','store/paySetting','store/paySetting','StorePaySetting','select','A',3,'2',0,NULL,86,1,NULL),
(0,153,'分佣提成','commission','commission','Commission','tree','A',1,'12',1,NULL,NULL,1,NULL),
(0,154,'分佣规则','commission/rule/index','commission/rule/index','CommissionRuleIndex','select','A',2,'0',1,NULL,153,1,NULL),
(0,155,'分佣记录','commission/log/index','commission/log/index','CommissionLogIndex','select','A',2,'1',1,NULL,153,1,NULL),
(0,156,'日记','story','story','Story','table','D',1,'0',1,NULL,NULL,1,NULL),
(0,157,'日记列表','story','story','Story','404','D',2,'0',1,NULL,156,1,NULL),
(0,158,'订单发货','order/delivery','order/delivery','OrderDelivery','bug','A',3,'0',0,NULL,107,1,NULL),
(0,159,'文章管理','content/article/index','content/article/index','ContentArticleIndex','select','A',2,'1',1,NULL,101,1,NULL),
(0,160,'新增文章','content/article/add','content/article/add','ContentArticleAdd','select','A',2,'0',0,NULL,101,1,NULL),
(0,161,'编辑文章','content/article/edit','content/article/edit','ContentArticleEdit','select','A',2,'1',0,NULL,101,1,NULL),
(0,162,'促销活动','marketing','marketing','Marketing','download','D',1,'12',1,NULL,NULL,1,NULL),
(0,163,'大转盘','marketing/index','marketing/index','MarketingIndex','select','D',2,'56',1,NULL,162,1,NULL),
(0,164,'转盘内容','marketing/detail','marketing/detail','MarketingDetail','select','D',2,'8',0,NULL,162,1,NULL),
(0,165,'交易设置','order/setting','order/setting','OrderSetting','select','A',2,'3',1,NULL,96,1,NULL),
(0,177,'商户管理','merchant/index','merchant/index','MerchantIndex','select','A',2,'0',1,NULL,84,1,NULL),
(0,178,'收银设置','setting/cashier','setting/cashier','SettingCashier','select','A',2,'3',1,NULL,125,1,NULL),
(0,179,'库存管理','stock/index','stock/index','StockIndex','select','A',2,'3',1,NULL,111,1,NULL),
(0,180,'数据统计','statistic','statistic','Statistic','chart','A',1,'8',1,NULL,NULL,1,NULL),
(0,181,'数据看板','statistic/index','statistic/index','StatisticIndex','select','A',2,'0',1,NULL,180,1,NULL),
(0,182,'会员分组','member/group/index','member/group/index','MemberGroupIndex','select','A',2,'1',1,NULL,79,1,NULL),
(0,183,'会员充值','balance/distribute','balance/distribute','BalanceDistribute','select','A',2,'2',1,NULL,116,1,NULL),
(0,184,'订单结算','settlement/index','settlement/index','SettlementIndex','select','A',2,'9',1,NULL,96,1,NULL),
(0,185,'发起结算','settlement/doSubmit','settlement/doSubmit','SettlementDoSubmit','select','A',2,'10',0,NULL,96,1,NULL),
(0,186,'结算确认','settlement/doConfirm','settlement/doConfirm','SettlementDoConfirm','select','A',2,'11',0,NULL,96,1,NULL),
(0,187,'生成代码','system/genCode/index','system/genCode/index','SystemGenCodeIndex','select','A',2,'3',1,NULL,15,1,NULL),
(0,188,'新增生成代码','system/genCode/add','system/genCode/add','SystemGenCodeAdd','select','A',3,'0',0,NULL,187,1,NULL),
(0,189,'删除生成代码','system/genCode/delete','system/genCode/delete','SystemGenCodeDelete','select','A',3,'0',0,NULL,187,1,NULL),
(0,190,'确定生成代码','system/genCode/gen','system/genCode/gen','SystemGenCodeGen','select','A',3,'3',0,NULL,187,1,NULL),
(0,192,'结算记录','commission/cash/index','commission/cash/index','CommissionCashIndex','select','A',2,'3',1,NULL,153,1,NULL),
(0,193,'桌码管理','table/index','table/index','TableIndex','select','A',2,'3',1,NULL,84,1,NULL),
(0,194,'短信配置','smsManager/setting','smsManager/setting','SmsManagerSetting','select','A',2,'5',1,NULL,90,1,NULL),
(0,195,'预约管理','book/index','book/index','BookIndex','select','A',2,'6',1,NULL,84,1,NULL),
(0,196,'预约记录','book/item','book/item','BookItem','select','A',2,'7',1,NULL,84,1,NULL),
(0,197,'打印设备','printer/index','printer/index','PrinterIndex','select','A',2,'4',1,NULL,84,1,NULL),
(0,198,'打印设置','printer/setting','printer/setting','PrinterSetting','select','A',3,'0',0,NULL,197,1,NULL),
(0,199,'分类配置','printer/category-config','printer/category-config','PrinterCategoryConfig','select','A',3,'1',0,NULL,197,1,NULL);

/*!40101 SET SQL_MODE=@OLD_SQL_MODE */;
/*!40014 SET FOREIGN_KEY_CHECKS=@OLD_FOREIGN_KEY_CHECKS */;
/*!40014 SET UNIQUE_CHECKS=@OLD_UNIQUE_CHECKS */;
/*!40111 SET SQL_NOTES=@OLD_SQL_NOTES */;
