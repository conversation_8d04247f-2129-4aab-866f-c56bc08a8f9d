<template>
  <div class="time-config-container">
    <el-form ref="timeConfigForm" :model="form" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="24">
          <el-form-item label="配置类型" prop="configType">
            <el-radio-group v-model="form.configType" @change="handleConfigTypeChange">
              <el-radio label="DAILY">每日通用</el-radio>
              <el-radio label="WEEKLY">按周设置</el-radio>
              <el-radio label="CUSTOM">自定义日期</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 按周设置时显示星期选择 -->
      <el-row v-if="form.configType === 'WEEKLY'">
        <el-col :span="24">
          <el-form-item label="选择星期" prop="weekDays">
            <el-checkbox-group v-model="form.weekDays">
              <el-checkbox v-for="day in weekOptions" :key="day.value" :label="day.value">
                {{ day.label }}
              </el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 自定义日期时显示日期选择 -->
      <el-row v-if="form.configType === 'CUSTOM'">
        <el-col :span="24">
          <el-form-item label="选择日期" prop="customDates">
            <el-date-picker
              v-model="form.customDates"
              type="dates"
              placeholder="选择一个或多个日期"
              value-format="yyyy-MM-dd"
              :picker-options="pickerOptions"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 时间段设置 -->
      <el-row>
        <el-col :span="24">
          <el-form-item label="时间段" prop="timeRanges">
            <div class="time-ranges-container">
              <div v-for="(range, index) in form.timeRanges" :key="index" class="time-range-item">
                <el-time-picker
                  v-model="range.startTime"
                  placeholder="开始时间"
                  format="HH:mm"
                  value-format="HH:mm"
                  :picker-options="{
                    selectableRange: '00:00:00 - 23:59:59'
                  }"
                />
                <span class="time-separator">至</span>
                <el-time-picker
                  v-model="range.endTime"
                  placeholder="结束时间"
                  format="HH:mm"
                  value-format="HH:mm"
                  :picker-options="{
                    selectableRange: '00:00:00 - 23:59:59'
                  }"
                />
                <el-button
                  type="danger"
                  icon="el-icon-delete"
                  size="mini"
                  circle
                  @click="removeTimeRange(index)"
                  :disabled="form.timeRanges.length === 1"
                />
              </div>
              <el-button type="primary" icon="el-icon-plus" size="small" @click="addTimeRange">
                添加时间段
              </el-button>
              <div class="form-tip">提示：时间段不能重叠，支持跨天时间段配置（如22:00-02:00）</div>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row>
        <el-col :span="24">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio label="A">启用</el-radio>
              <el-radio label="N">禁用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'TimeConfigForm',
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    goodsId: {
      type: [String, Number],
      required: true
    }
  },
  data() {
    return {
      form: {
        id: undefined,
        goodsId: this.goodsId,
        configType: 'DAILY',
        weekDays: [],
        customDates: [],
        timeRanges: [{ startTime: '', endTime: '' }],
        status: 'A'
      },
      weekOptions: [
        { label: '周一', value: 1 },
        { label: '周二', value: 2 },
        { label: '周三', value: 3 },
        { label: '周四', value: 4 },
        { label: '周五', value: 5 },
        { label: '周六', value: 6 },
        { label: '周日', value: 7 }
      ],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7
        }
      },
      rules: {
        configType: [
          { required: true, message: '请选择配置类型', trigger: 'change' }
        ],
        weekDays: [
          {
            required: true,
            message: '请至少选择一个星期',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.form.configType === 'WEEKLY' && (!value || value.length === 0)) {
                callback(new Error('请至少选择一个星期'))
              } else {
                callback()
              }
            }
          }
        ],
        customDates: [
          {
            required: true,
            message: '请至少选择一个日期',
            trigger: 'change',
            validator: (rule, value, callback) => {
              if (this.form.configType === 'CUSTOM' && (!value || value.length === 0)) {
                callback(new Error('请至少选择一个日期'))
              } else {
                callback()
              }
            }
          }
        ],
        timeRanges: [
          {
            required: true,
            message: '请设置至少一个时间段',
            trigger: 'blur',
            validator: (rule, value, callback) => {
              if (!value || value.length === 0) {
                callback(new Error('请设置至少一个时间段'))
              } else {
                // 检查时间段是否完整
                for (let range of value) {
                  if (!range.startTime || !range.endTime) {
                    callback(new Error('请完善时间段设置'))
                    return
                  }
                }

                // 优化：使用排序+线性扫描检查重叠，时间复杂度从O(n²)降为O(n log n)
                const sortedRanges = [...value].sort((a, b) => {
                  // 将时间转换为分钟数进行比较
                  const timeToMinutes = (time) => {
                    const [hours, minutes] = time.split(':').map(Number);
                    return hours * 60 + minutes;
                  };

                  return timeToMinutes(a.startTime) - timeToMinutes(b.startTime);
                });

                // 检查相邻时间段是否重叠
                for (let i = 0; i < sortedRanges.length - 1; i++) {
                  const current = sortedRanges[i];
                  const next = sortedRanges[i + 1];

                  const currentStart = timeToMinutes(current.startTime);
                  const currentEnd = timeToMinutes(current.endTime);
                  const nextStart = timeToMinutes(next.startTime);
                  const nextEnd = timeToMinutes(next.endTime);

                  // 处理跨天情况
                  const currentCrossDay = currentEnd <= currentStart;
                  const nextCrossDay = nextEnd <= nextStart;

                  if (currentCrossDay && nextCrossDay) {
                    // 两个都是跨天时间段，总是重叠
                    callback(new Error('时间段不能重叠'))
                    return
                  } else if (currentCrossDay) {
                    // 当前是跨天，下一个是正常
                    if (nextStart >= currentStart || nextEnd <= currentEnd) {
                      callback(new Error('时间段不能重叠'))
                      return
                    }
                  } else if (nextCrossDay) {
                    // 当前是正常，下一个是跨天
                    if (currentStart >= nextStart || currentEnd <= nextEnd) {
                      callback(new Error('时间段不能重叠'))
                      return
                    }
                  } else {
                    // 两个都是正常时间段
                    if (nextStart < currentEnd) {
                      callback(new Error('时间段不能重叠'))
                      return
                    }
                  }
                }
                callback()
              }
            }
          }
        ]
      }
    }
  },
  watch: {
    value: {
      handler(newVal) {
        if (newVal && Object.keys(newVal).length > 0) {
          this.form = {
            ...this.form,
            ...newVal,
            timeRanges: newVal.timeRanges && newVal.timeRanges.length > 0
              ? newVal.timeRanges
              : [{ startTime: '', endTime: '' }]
          }
          // 数据更新后排序
          this.$nextTick(() => {
            this.sortTimeRanges();
          });
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 将时间转换为分钟数进行比较
    timeToMinutes(time) {
      const [hours, minutes] = time.split(':').map(Number);
      return hours * 60 + minutes;
    },

    // 对时间段进行排序
    sortTimeRanges() {
      if (this.form.timeRanges && this.form.timeRanges.length > 1) {
        this.form.timeRanges.sort((a, b) => {
          if (a.startTime && b.startTime) {
            return this.timeToMinutes(a.startTime) - this.timeToMinutes(b.startTime);
          }
          return 0;
        });
      }
    },

    handleConfigTypeChange() {
      // 切换配置类型时清空相关数据
      if (this.form.configType === 'DAILY') {
        this.form.weekDays = [];
        this.form.customDates = [];
      } else if (this.form.configType === 'WEEKLY') {
        this.form.customDates = [];
      } else if (this.form.configType === 'CUSTOM') {
        this.form.weekDays = [];
      }

      // 提示用户配置类型已切换
      this.$message.info('已切换配置类型，请重新设置相关选项');

      // 验证表单
      this.$nextTick(() => {
        this.$refs.timeConfigForm.validateField('weekDays');
        this.$refs.timeConfigForm.validateField('customDates');
      });

      // 手动排序时间段
      this.sortTimeRanges();
    },

    addTimeRange() {
      this.form.timeRanges.push({ startTime: '', endTime: '' });
      // 添加新时间段后排序
      this.$nextTick(() => {
        this.sortTimeRanges();
      });
    },

    removeTimeRange(index) {
      if (this.form.timeRanges.length > 1) {
        this.form.timeRanges.splice(index, 1);
        // 删除时间段后排序
        this.$nextTick(() => {
          this.sortTimeRanges();
        });
      }
    },

    validate() {
      return new Promise((resolve, reject) => {
        this.$refs.timeConfigForm.validate(valid => {
          if (valid) {
            // 验证通过时排序
            this.sortTimeRanges();
            resolve(this.form);
          } else {
            this.$message.error('表单验证失败，请检查输入内容');
            reject(new Error('表单验证失败'));
          }
        });
      });
    },

    resetForm() {
      this.$refs.timeConfigForm.resetFields();
      this.form = {
        id: undefined,
        goodsId: this.goodsId,
        configType: 'DAILY',
        weekDays: [],
        customDates: [],
        timeRanges: [{ startTime: '', endTime: '' }],
        status: 'A'
      };
      // 重置后排序
      this.$nextTick(() => {
        this.sortTimeRanges();
      });
    },
  }
}
</script>

<style scoped>
.time-config-container {
  padding: 20px;
}

.time-ranges-container {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.time-range-item {
  display: flex;
  align-items: center;
  gap: 10px;
}

.time-separator {
  margin: 0 5px;
  color: #606266;
}
</style>
