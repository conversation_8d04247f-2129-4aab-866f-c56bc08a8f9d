{"remainingRequest": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigForm.vue?vue&type=script&lang=js", "dependencies": [{"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\src\\views\\goods\\components\\TimeConfigForm.vue", "mtime": 1755006149730}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\babel.config.js", "mtime": 1695363473000}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\babel-loader\\lib\\index.js", "mtime": 1734093919680}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 1734093918475}, {"path": "D:\\workspace\\fuintFoodSystem\\fuintAdmin\\node_modules\\vue-loader\\lib\\index.js", "mtime": 1734093919665}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["name", "props", "value", "type", "Object", "default", "_default", "goodsId", "String", "Number", "required", "data", "_this", "form", "id", "undefined", "configType", "weekDays", "customDates", "timeRanges", "startTime", "endTime", "status", "weekOptions", "label", "pickerOptions", "disabledDate", "time", "getTime", "Date", "now", "rules", "message", "trigger", "validator", "rule", "callback", "length", "Error", "_iterator", "_createForOfIteratorHelper", "_step", "s", "n", "done", "range", "err", "e", "f", "sortedRanges", "_toConsumableArray", "sort", "a", "b", "timeToMinutes", "_time$split$map", "split", "map", "_time$split$map2", "_slicedToArray", "hours", "minutes", "i", "current", "next", "currentStart", "currentEnd", "nextStart", "nextEnd", "currentCrossDay", "nextCrossDay", "watch", "handler", "newVal", "_this2", "keys", "_objectSpread", "$nextTick", "sortTimeRanges", "immediate", "deep", "methods", "_defineProperty", "_time$split$map3", "_time$split$map4", "_this3", "handleConfigTypeChange", "_this4", "$message", "info", "$refs", "timeConfigForm", "validateField", "addTimeRange", "_this5", "push", "removeTimeRange", "index", "_this6", "splice", "validate", "_this7", "Promise", "resolve", "reject", "valid", "error", "resetForm", "_this8", "resetFields", "_this9"], "sources": ["src/views/goods/components/TimeConfigForm.vue"], "sourcesContent": ["<template>\n  <div class=\"time-config-container\">\n    <el-form ref=\"timeConfigForm\" :model=\"form\" :rules=\"rules\" label-width=\"120px\">\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"配置类型\" prop=\"configType\">\n            <el-radio-group v-model=\"form.configType\" @change=\"handleConfigTypeChange\">\n              <el-radio label=\"DAILY\">每日通用</el-radio>\n              <el-radio label=\"WEEKLY\">按周设置</el-radio>\n              <el-radio label=\"CUSTOM\">自定义日期</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 按周设置时显示星期选择 -->\n      <el-row v-if=\"form.configType === 'WEEKLY'\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"选择星期\" prop=\"weekDays\">\n            <el-checkbox-group v-model=\"form.weekDays\">\n              <el-checkbox v-for=\"day in weekOptions\" :key=\"day.value\" :label=\"day.value\">\n                {{ day.label }}\n              </el-checkbox>\n            </el-checkbox-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 自定义日期时显示日期选择 -->\n      <el-row v-if=\"form.configType === 'CUSTOM'\">\n        <el-col :span=\"24\">\n          <el-form-item label=\"选择日期\" prop=\"customDates\">\n            <el-date-picker\n              v-model=\"form.customDates\"\n              type=\"dates\"\n              placeholder=\"选择一个或多个日期\"\n              value-format=\"yyyy-MM-dd\"\n              :picker-options=\"pickerOptions\"\n            />\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <!-- 时间段设置 -->\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"时间段\" prop=\"timeRanges\">\n            <div class=\"time-ranges-container\">\n              <div v-for=\"(range, index) in form.timeRanges\" :key=\"index\" class=\"time-range-item\">\n                <el-time-picker\n                  v-model=\"range.startTime\"\n                  placeholder=\"开始时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  :picker-options=\"{\n                    selectableRange: '00:00:00 - 23:59:59'\n                  }\"\n                />\n                <span class=\"time-separator\">至</span>\n                <el-time-picker\n                  v-model=\"range.endTime\"\n                  placeholder=\"结束时间\"\n                  format=\"HH:mm\"\n                  value-format=\"HH:mm\"\n                  :picker-options=\"{\n                    selectableRange: '00:00:00 - 23:59:59'\n                  }\"\n                />\n                <el-button\n                  type=\"danger\"\n                  icon=\"el-icon-delete\"\n                  size=\"mini\"\n                  circle\n                  @click=\"removeTimeRange(index)\"\n                  :disabled=\"form.timeRanges.length === 1\"\n                />\n              </div>\n              <el-button type=\"primary\" icon=\"el-icon-plus\" size=\"small\" @click=\"addTimeRange\">\n                添加时间段\n              </el-button>\n              <div class=\"form-tip\">提示：时间段不能重叠，支持跨天时间段配置（如22:00-02:00）</div>\n            </div>\n          </el-form-item>\n        </el-col>\n      </el-row>\n\n      <el-row>\n        <el-col :span=\"24\">\n          <el-form-item label=\"状态\" prop=\"status\">\n            <el-radio-group v-model=\"form.status\">\n              <el-radio label=\"A\">启用</el-radio>\n              <el-radio label=\"N\">禁用</el-radio>\n            </el-radio-group>\n          </el-form-item>\n        </el-col>\n      </el-row>\n    </el-form>\n  </div>\n</template>\n\n<script>\nexport default {\n  name: 'TimeConfigForm',\n  props: {\n    value: {\n      type: Object,\n      default: () => ({})\n    },\n    goodsId: {\n      type: [String, Number],\n      required: true\n    }\n  },\n  data() {\n    return {\n      form: {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      },\n      weekOptions: [\n        { label: '周一', value: 1 },\n        { label: '周二', value: 2 },\n        { label: '周三', value: 3 },\n        { label: '周四', value: 4 },\n        { label: '周五', value: 5 },\n        { label: '周六', value: 6 },\n        { label: '周日', value: 7 }\n      ],\n      pickerOptions: {\n        disabledDate(time) {\n          return time.getTime() < Date.now() - 8.64e7\n        }\n      },\n      rules: {\n        configType: [\n          { required: true, message: '请选择配置类型', trigger: 'change' }\n        ],\n        weekDays: [\n          {\n            required: true,\n            message: '请至少选择一个星期',\n            trigger: 'change',\n            validator: (rule, value, callback) => {\n              if (this.form.configType === 'WEEKLY' && (!value || value.length === 0)) {\n                callback(new Error('请至少选择一个星期'))\n              } else {\n                callback()\n              }\n            }\n          }\n        ],\n        customDates: [\n          {\n            required: true,\n            message: '请至少选择一个日期',\n            trigger: 'change',\n            validator: (rule, value, callback) => {\n              if (this.form.configType === 'CUSTOM' && (!value || value.length === 0)) {\n                callback(new Error('请至少选择一个日期'))\n              } else {\n                callback()\n              }\n            }\n          }\n        ],\n        timeRanges: [\n          {\n            required: true,\n            message: '请设置至少一个时间段',\n            trigger: 'blur',\n            validator: (rule, value, callback) => {\n              if (!value || value.length === 0) {\n                callback(new Error('请设置至少一个时间段'))\n              } else {\n                // 检查时间段是否完整\n                for (let range of value) {\n                  if (!range.startTime || !range.endTime) {\n                    callback(new Error('请完善时间段设置'))\n                    return\n                  }\n                }\n\n                // 优化：使用排序+线性扫描检查重叠，时间复杂度从O(n²)降为O(n log n)\n                const sortedRanges = [...value].sort((a, b) => {\n                  // 将时间转换为分钟数进行比较\n                  const timeToMinutes = (time) => {\n                    const [hours, minutes] = time.split(':').map(Number);\n                    return hours * 60 + minutes;\n                  };\n\n                  return timeToMinutes(a.startTime) - timeToMinutes(b.startTime);\n                });\n\n                // 检查相邻时间段是否重叠\n                for (let i = 0; i < sortedRanges.length - 1; i++) {\n                  const current = sortedRanges[i];\n                  const next = sortedRanges[i + 1];\n\n                  const currentStart = timeToMinutes(current.startTime);\n                  const currentEnd = timeToMinutes(current.endTime);\n                  const nextStart = timeToMinutes(next.startTime);\n                  const nextEnd = timeToMinutes(next.endTime);\n\n                  // 处理跨天情况\n                  const currentCrossDay = currentEnd <= currentStart;\n                  const nextCrossDay = nextEnd <= nextStart;\n\n                  if (currentCrossDay && nextCrossDay) {\n                    // 两个都是跨天时间段，总是重叠\n                    callback(new Error('时间段不能重叠'))\n                    return\n                  } else if (currentCrossDay) {\n                    // 当前是跨天，下一个是正常\n                    if (nextStart >= currentStart || nextEnd <= currentEnd) {\n                      callback(new Error('时间段不能重叠'))\n                      return\n                    }\n                  } else if (nextCrossDay) {\n                    // 当前是正常，下一个是跨天\n                    if (currentStart >= nextStart || currentEnd <= nextEnd) {\n                      callback(new Error('时间段不能重叠'))\n                      return\n                    }\n                  } else {\n                    // 两个都是正常时间段\n                    if (nextStart < currentEnd) {\n                      callback(new Error('时间段不能重叠'))\n                      return\n                    }\n                  }\n                }\n                callback()\n              }\n            }\n          }\n        ]\n      }\n    }\n  },\n  watch: {\n    value: {\n      handler(newVal) {\n        if (newVal && Object.keys(newVal).length > 0) {\n          this.form = {\n            ...this.form,\n            ...newVal,\n            timeRanges: newVal.timeRanges && newVal.timeRanges.length > 0\n              ? newVal.timeRanges\n              : [{ startTime: '', endTime: '' }]\n          }\n          // 数据更新后排序\n          this.$nextTick(() => {\n            this.sortTimeRanges();\n          });\n        }\n      },\n      immediate: true,\n      deep: true\n    }\n  },\n  methods: {\n    // 将时间转换为分钟数进行比较\n    timeToMinutes(time) {\n      const [hours, minutes] = time.split(':').map(Number);\n      return hours * 60 + minutes;\n    },\n\n    // 对时间段进行排序\n    sortTimeRanges() {\n      if (this.form.timeRanges && this.form.timeRanges.length > 1) {\n        this.form.timeRanges.sort((a, b) => {\n          if (a.startTime && b.startTime) {\n            return this.timeToMinutes(a.startTime) - this.timeToMinutes(b.startTime);\n          }\n          return 0;\n        });\n      }\n    },\n\n    handleConfigTypeChange() {\n      // 切换配置类型时清空相关数据\n      if (this.form.configType === 'DAILY') {\n        this.form.weekDays = [];\n        this.form.customDates = [];\n      } else if (this.form.configType === 'WEEKLY') {\n        this.form.customDates = [];\n      } else if (this.form.configType === 'CUSTOM') {\n        this.form.weekDays = [];\n      }\n\n      // 提示用户配置类型已切换\n      this.$message.info('已切换配置类型，请重新设置相关选项');\n\n      // 验证表单\n      this.$nextTick(() => {\n        this.$refs.timeConfigForm.validateField('weekDays');\n        this.$refs.timeConfigForm.validateField('customDates');\n      });\n\n      // 手动排序时间段\n      this.sortTimeRanges();\n    },\n\n    addTimeRange() {\n      this.form.timeRanges.push({ startTime: '', endTime: '' });\n      // 添加新时间段后排序\n      this.$nextTick(() => {\n        this.sortTimeRanges();\n      });\n    },\n\n    removeTimeRange(index) {\n      if (this.form.timeRanges.length > 1) {\n        this.form.timeRanges.splice(index, 1);\n        // 删除时间段后排序\n        this.$nextTick(() => {\n          this.sortTimeRanges();\n        });\n      }\n    },\n\n    validate() {\n      return new Promise((resolve, reject) => {\n        this.$refs.timeConfigForm.validate(valid => {\n          if (valid) {\n            // 验证通过时排序\n            this.sortTimeRanges();\n            resolve(this.form);\n          } else {\n            this.$message.error('表单验证失败，请检查输入内容');\n            reject(new Error('表单验证失败'));\n          }\n        });\n      });\n    },\n\n    resetForm() {\n      this.$refs.timeConfigForm.resetFields();\n      this.form = {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      };\n      // 重置后排序\n      this.$nextTick(() => {\n        this.sortTimeRanges();\n      });\n    },\n    addTimeRange() {\n      this.form.timeRanges.push({ startTime: '', endTime: '' })\n    },\n    removeTimeRange(index) {\n      if (this.form.timeRanges.length > 1) {\n        this.form.timeRanges.splice(index, 1)\n      }\n    },\n    validate() {\n      return new Promise((resolve, reject) => {\n        this.$refs.timeConfigForm.validate(valid => {\n          if (valid) {\n            resolve(this.form)\n          } else {\n            this.$message.error('表单验证失败，请检查输入内容')\n            reject(new Error('表单验证失败'))\n          }\n        })\n      })\n    },\n    resetForm() {\n      this.$refs.timeConfigForm.resetFields()\n      this.form = {\n        id: undefined,\n        goodsId: this.goodsId,\n        configType: 'DAILY',\n        weekDays: [],\n        customDates: [],\n        timeRanges: [{ startTime: '', endTime: '' }],\n        status: 'A'\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.time-config-container {\n  padding: 20px;\n}\n\n.time-ranges-container {\n  display: flex;\n  flex-direction: column;\n  gap: 10px;\n}\n\n.time-range-item {\n  display: flex;\n  align-items: center;\n  gap: 10px;\n}\n\n.time-separator {\n  margin: 0 5px;\n  color: #606266;\n}\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqGA;EACAA,IAAA;EACAC,KAAA;IACAC,KAAA;MACAC,IAAA,EAAAC,MAAA;MACAC,OAAA,WAAAC,SAAA;QAAA;MAAA;IACA;IACAC,OAAA;MACAJ,IAAA,GAAAK,MAAA,EAAAC,MAAA;MACAC,QAAA;IACA;EACA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA;MACAC,IAAA;QACAC,EAAA,EAAAC,SAAA;QACAR,OAAA,OAAAA,OAAA;QACAS,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,UAAA;UAAAC,SAAA;UAAAC,OAAA;QAAA;QACAC,MAAA;MACA;MACAC,WAAA,GACA;QAAAC,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,GACA;QAAAsB,KAAA;QAAAtB,KAAA;MAAA,EACA;MACAuB,aAAA;QACAC,YAAA,WAAAA,aAAAC,IAAA;UACA,OAAAA,IAAA,CAAAC,OAAA,KAAAC,IAAA,CAAAC,GAAA;QACA;MACA;MACAC,KAAA;QACAf,UAAA,GACA;UAAAN,QAAA;UAAAsB,OAAA;UAAAC,OAAA;QAAA,EACA;QACAhB,QAAA,GACA;UACAP,QAAA;UACAsB,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,UAAAC,IAAA,EAAAjC,KAAA,EAAAkC,QAAA;YACA,IAAAxB,KAAA,CAAAC,IAAA,CAAAG,UAAA,mBAAAd,KAAA,IAAAA,KAAA,CAAAmC,MAAA;cACAD,QAAA,KAAAE,KAAA;YACA;cACAF,QAAA;YACA;UACA;QACA,EACA;QACAlB,WAAA,GACA;UACAR,QAAA;UACAsB,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,UAAAC,IAAA,EAAAjC,KAAA,EAAAkC,QAAA;YACA,IAAAxB,KAAA,CAAAC,IAAA,CAAAG,UAAA,mBAAAd,KAAA,IAAAA,KAAA,CAAAmC,MAAA;cACAD,QAAA,KAAAE,KAAA;YACA;cACAF,QAAA;YACA;UACA;QACA,EACA;QACAjB,UAAA,GACA;UACAT,QAAA;UACAsB,OAAA;UACAC,OAAA;UACAC,SAAA,WAAAA,UAAAC,IAAA,EAAAjC,KAAA,EAAAkC,QAAA;YACA,KAAAlC,KAAA,IAAAA,KAAA,CAAAmC,MAAA;cACAD,QAAA,KAAAE,KAAA;YACA;cACA;cAAA,IAAAC,SAAA,GAAAC,0BAAA,CACAtC,KAAA;gBAAAuC,KAAA;cAAA;gBAAA,KAAAF,SAAA,CAAAG,CAAA,MAAAD,KAAA,GAAAF,SAAA,CAAAI,CAAA,IAAAC,IAAA;kBAAA,IAAAC,KAAA,GAAAJ,KAAA,CAAAvC,KAAA;kBACA,KAAA2C,KAAA,CAAAzB,SAAA,KAAAyB,KAAA,CAAAxB,OAAA;oBACAe,QAAA,KAAAE,KAAA;oBACA;kBACA;gBACA;;gBAEA;cAAA,SAAAQ,GAAA;gBAAAP,SAAA,CAAAQ,CAAA,CAAAD,GAAA;cAAA;gBAAAP,SAAA,CAAAS,CAAA;cAAA;cACA,IAAAC,YAAA,GAAAC,kBAAA,CAAAhD,KAAA,EAAAiD,IAAA,WAAAC,CAAA,EAAAC,CAAA;gBACA;gBACA,IAAAC,aAAA,YAAAA,cAAA3B,IAAA;kBACA,IAAA4B,eAAA,GAAA5B,IAAA,CAAA6B,KAAA,MAAAC,GAAA,CAAAhD,MAAA;oBAAAiD,gBAAA,GAAAC,cAAA,CAAAJ,eAAA;oBAAAK,KAAA,GAAAF,gBAAA;oBAAAG,OAAA,GAAAH,gBAAA;kBACA,OAAAE,KAAA,QAAAC,OAAA;gBACA;gBAEA,OAAAP,aAAA,CAAAF,CAAA,CAAAhC,SAAA,IAAAkC,aAAA,CAAAD,CAAA,CAAAjC,SAAA;cACA;;cAEA;cACA,SAAA0C,CAAA,MAAAA,CAAA,GAAAb,YAAA,CAAAZ,MAAA,MAAAyB,CAAA;gBACA,IAAAC,OAAA,GAAAd,YAAA,CAAAa,CAAA;gBACA,IAAAE,IAAA,GAAAf,YAAA,CAAAa,CAAA;gBAEA,IAAAG,YAAA,GAAAX,aAAA,CAAAS,OAAA,CAAA3C,SAAA;gBACA,IAAA8C,UAAA,GAAAZ,aAAA,CAAAS,OAAA,CAAA1C,OAAA;gBACA,IAAA8C,SAAA,GAAAb,aAAA,CAAAU,IAAA,CAAA5C,SAAA;gBACA,IAAAgD,OAAA,GAAAd,aAAA,CAAAU,IAAA,CAAA3C,OAAA;;gBAEA;gBACA,IAAAgD,eAAA,GAAAH,UAAA,IAAAD,YAAA;gBACA,IAAAK,YAAA,GAAAF,OAAA,IAAAD,SAAA;gBAEA,IAAAE,eAAA,IAAAC,YAAA;kBACA;kBACAlC,QAAA,KAAAE,KAAA;kBACA;gBACA,WAAA+B,eAAA;kBACA;kBACA,IAAAF,SAAA,IAAAF,YAAA,IAAAG,OAAA,IAAAF,UAAA;oBACA9B,QAAA,KAAAE,KAAA;oBACA;kBACA;gBACA,WAAAgC,YAAA;kBACA;kBACA,IAAAL,YAAA,IAAAE,SAAA,IAAAD,UAAA,IAAAE,OAAA;oBACAhC,QAAA,KAAAE,KAAA;oBACA;kBACA;gBACA;kBACA;kBACA,IAAA6B,SAAA,GAAAD,UAAA;oBACA9B,QAAA,KAAAE,KAAA;oBACA;kBACA;gBACA;cACA;cACAF,QAAA;YACA;UACA;QACA;MAEA;IACA;EACA;EACAmC,KAAA;IACArE,KAAA;MACAsE,OAAA,WAAAA,QAAAC,MAAA;QAAA,IAAAC,MAAA;QACA,IAAAD,MAAA,IAAArE,MAAA,CAAAuE,IAAA,CAAAF,MAAA,EAAApC,MAAA;UACA,KAAAxB,IAAA,GAAA+D,aAAA,CAAAA,aAAA,CAAAA,aAAA,KACA,KAAA/D,IAAA,GACA4D,MAAA;YACAtD,UAAA,EAAAsD,MAAA,CAAAtD,UAAA,IAAAsD,MAAA,CAAAtD,UAAA,CAAAkB,MAAA,OACAoC,MAAA,CAAAtD,UAAA,GACA;cAAAC,SAAA;cAAAC,OAAA;YAAA;UAAA,EACA;UACA;UACA,KAAAwD,SAAA;YACAH,MAAA,CAAAI,cAAA;UACA;QACA;MACA;MACAC,SAAA;MACAC,IAAA;IACA;EACA;EACAC,OAAA,EAAAC,eAAA,CAAAA,eAAA,CAAAA,eAAA,CAAAA,eAAA;IACA;IACA5B,aAAA,WAAAA,cAAA3B,IAAA;MACA,IAAAwD,gBAAA,GAAAxD,IAAA,CAAA6B,KAAA,MAAAC,GAAA,CAAAhD,MAAA;QAAA2E,gBAAA,GAAAzB,cAAA,CAAAwB,gBAAA;QAAAvB,KAAA,GAAAwB,gBAAA;QAAAvB,OAAA,GAAAuB,gBAAA;MACA,OAAAxB,KAAA,QAAAC,OAAA;IACA;IAEA;IACAiB,cAAA,WAAAA,eAAA;MAAA,IAAAO,MAAA;MACA,SAAAxE,IAAA,CAAAM,UAAA,SAAAN,IAAA,CAAAM,UAAA,CAAAkB,MAAA;QACA,KAAAxB,IAAA,CAAAM,UAAA,CAAAgC,IAAA,WAAAC,CAAA,EAAAC,CAAA;UACA,IAAAD,CAAA,CAAAhC,SAAA,IAAAiC,CAAA,CAAAjC,SAAA;YACA,OAAAiE,MAAA,CAAA/B,aAAA,CAAAF,CAAA,CAAAhC,SAAA,IAAAiE,MAAA,CAAA/B,aAAA,CAAAD,CAAA,CAAAjC,SAAA;UACA;UACA;QACA;MACA;IACA;IAEAkE,sBAAA,WAAAA,uBAAA;MAAA,IAAAC,MAAA;MACA;MACA,SAAA1E,IAAA,CAAAG,UAAA;QACA,KAAAH,IAAA,CAAAI,QAAA;QACA,KAAAJ,IAAA,CAAAK,WAAA;MACA,gBAAAL,IAAA,CAAAG,UAAA;QACA,KAAAH,IAAA,CAAAK,WAAA;MACA,gBAAAL,IAAA,CAAAG,UAAA;QACA,KAAAH,IAAA,CAAAI,QAAA;MACA;;MAEA;MACA,KAAAuE,QAAA,CAAAC,IAAA;;MAEA;MACA,KAAAZ,SAAA;QACAU,MAAA,CAAAG,KAAA,CAAAC,cAAA,CAAAC,aAAA;QACAL,MAAA,CAAAG,KAAA,CAAAC,cAAA,CAAAC,aAAA;MACA;;MAEA;MACA,KAAAd,cAAA;IACA;IAEAe,YAAA,WAAAA,aAAA;MAAA,IAAAC,MAAA;MACA,KAAAjF,IAAA,CAAAM,UAAA,CAAA4E,IAAA;QAAA3E,SAAA;QAAAC,OAAA;MAAA;MACA;MACA,KAAAwD,SAAA;QACAiB,MAAA,CAAAhB,cAAA;MACA;IACA;IAEAkB,eAAA,WAAAA,gBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,SAAArF,IAAA,CAAAM,UAAA,CAAAkB,MAAA;QACA,KAAAxB,IAAA,CAAAM,UAAA,CAAAgF,MAAA,CAAAF,KAAA;QACA;QACA,KAAApB,SAAA;UACAqB,MAAA,CAAApB,cAAA;QACA;MACA;IACA;IAEAsB,QAAA,WAAAA,SAAA;MAAA,IAAAC,MAAA;MACA,WAAAC,OAAA,WAAAC,OAAA,EAAAC,MAAA;QACAH,MAAA,CAAAX,KAAA,CAAAC,cAAA,CAAAS,QAAA,WAAAK,KAAA;UACA,IAAAA,KAAA;YACA;YACAJ,MAAA,CAAAvB,cAAA;YACAyB,OAAA,CAAAF,MAAA,CAAAxF,IAAA;UACA;YACAwF,MAAA,CAAAb,QAAA,CAAAkB,KAAA;YACAF,MAAA,KAAAlE,KAAA;UACA;QACA;MACA;IACA;IAEAqE,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAlB,KAAA,CAAAC,cAAA,CAAAkB,WAAA;MACA,KAAAhG,IAAA;QACAC,EAAA,EAAAC,SAAA;QACAR,OAAA,OAAAA,OAAA;QACAS,UAAA;QACAC,QAAA;QACAC,WAAA;QACAC,UAAA;UAAAC,SAAA;UAAAC,OAAA;QAAA;QACAC,MAAA;MACA;MACA;MACA,KAAAuD,SAAA;QACA+B,MAAA,CAAA9B,cAAA;MACA;IACA;EAAA,4BAAAe,aAAA,EACA;IACA,KAAAhF,IAAA,CAAAM,UAAA,CAAA4E,IAAA;MAAA3E,SAAA;MAAAC,OAAA;IAAA;EACA,gCAAA2E,gBACAC,KAAA;IACA,SAAApF,IAAA,CAAAM,UAAA,CAAAkB,MAAA;MACA,KAAAxB,IAAA,CAAAM,UAAA,CAAAgF,MAAA,CAAAF,KAAA;IACA;EACA,yBAAAG,SAAA,EACA;IAAA,IAAAU,MAAA;IACA,WAAAR,OAAA,WAAAC,OAAA,EAAAC,MAAA;MACAM,MAAA,CAAApB,KAAA,CAAAC,cAAA,CAAAS,QAAA,WAAAK,KAAA;QACA,IAAAA,KAAA;UACAF,OAAA,CAAAO,MAAA,CAAAjG,IAAA;QACA;UACAiG,MAAA,CAAAtB,QAAA,CAAAkB,KAAA;UACAF,MAAA,KAAAlE,KAAA;QACA;MACA;IACA;EACA,0BAAAqE,UAAA,EACA;IACA,KAAAjB,KAAA,CAAAC,cAAA,CAAAkB,WAAA;IACA,KAAAhG,IAAA;MACAC,EAAA,EAAAC,SAAA;MACAR,OAAA,OAAAA,OAAA;MACAS,UAAA;MACAC,QAAA;MACAC,WAAA;MACAC,UAAA;QAAAC,SAAA;QAAAC,OAAA;MAAA;MACAC,MAAA;IACA;EACA;AAEA", "ignoreList": []}]}