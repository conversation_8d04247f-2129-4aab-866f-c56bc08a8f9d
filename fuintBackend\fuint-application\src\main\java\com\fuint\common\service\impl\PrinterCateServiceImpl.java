package com.fuint.common.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fuint.common.service.PrinterCateService;
import com.fuint.repository.mapper.MtPrinterCateMapper;
import com.fuint.repository.model.MtPrinterCate;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.common.enums.StatusEnum;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 打印机分类关联服务实现类
 *
 * @Created by FSQ
 */
@Service
@AllArgsConstructor
public class PrinterCateServiceImpl extends ServiceImpl<MtPrinterCateMapper, MtPrinterCate> implements PrinterCateService {

    private static final Logger logger = LoggerFactory.getLogger(PrinterCateServiceImpl.class);

    private MtPrinterCateMapper mtPrinterCateMapper;

    /**
     * 根据条件搜索打印机分类关联
     *
     * @param params 查询参数
     * @throws BusinessCheckException
     * @return
     */
    @Override
    public List<MtPrinterCate> queryPrinterCateListByParams(Map<String, Object> params) throws BusinessCheckException {
        return mtPrinterCateMapper.selectByParams(params);
    }

    /**
     * 添加打印机分类关联
     *
     * @param mtPrinterCate 打印机分类关联信息
     * @throws BusinessCheckException
     * @return
     */
    @Override
    public MtPrinterCate addPrinterCate(MtPrinterCate mtPrinterCate) throws BusinessCheckException {
        if (mtPrinterCate.getPrinterId() == null || mtPrinterCate.getCateId() == null) {
            throw new BusinessCheckException("打印机ID和分类ID不能为空");
        }

        mtPrinterCate.setStatus(StatusEnum.ENABLED.getKey());
        mtPrinterCate.setCreateTime(new Date());
        mtPrinterCate.setUpdateTime(new Date());

        this.save(mtPrinterCate);
        return mtPrinterCate;
    }

    /**
     * 根据打印机ID删除关联
     *
     * @param printerId 打印机ID
     * @throws BusinessCheckException
     * @return
     */
    @Override
    public void deleteByPrinterId(Integer printerId) throws BusinessCheckException {
        if (printerId == null) {
            throw new BusinessCheckException("打印机ID不能为空");
        }
        mtPrinterCateMapper.deleteByPrinterId(printerId);
    }

    /**
     * 根据分类ID删除关联
     *
     * @param cateId 分类ID
     * @throws BusinessCheckException
     * @return
     */
    @Override
    public void deleteByCateId(Integer cateId) throws BusinessCheckException {
        if (cateId == null) {
            throw new BusinessCheckException("分类ID不能为空");
        }
        mtPrinterCateMapper.deleteByCateId(cateId);
    }

    /**
     * 批量保存打印机分类关联
     *
     * @param printerId 打印机ID
     * @param cateIds 分类ID列表
     * @param merchantId 商户ID
     * @param storeId 店铺ID
     * @param operator 操作人
     * @throws BusinessCheckException
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void savePrinterCateRelations(Integer printerId, List<Integer> cateIds, Integer merchantId, Integer storeId, String operator) throws BusinessCheckException {
        if (printerId == null) {
            throw new BusinessCheckException("打印机ID不能为空");
        }

        // 先删除原有关联
        deleteByPrinterId(printerId);

        // 添加新的关联
        if (cateIds != null && !cateIds.isEmpty()) {
            for (Integer cateId : cateIds) {
                MtPrinterCate printerCate = new MtPrinterCate();
                printerCate.setPrinterId(printerId);
                printerCate.setCateId(cateId);
                printerCate.setMerchantId(merchantId);
                printerCate.setStoreId(storeId);
                printerCate.setOperator(operator);
                addPrinterCate(printerCate);
            }
        }
    }

    /**
     * 根据商品分类ID查询关联的打印机ID列表
     *
     * @param cateId 商品分类ID
     * @param storeId 店铺ID
     * @return 打印机ID列表
     */
    @Override
    public List<Integer> getPrinterIdsByCateId(Integer cateId, Integer storeId) {
        return mtPrinterCateMapper.selectPrinterIdsByCateId(cateId, storeId);
    }

}
