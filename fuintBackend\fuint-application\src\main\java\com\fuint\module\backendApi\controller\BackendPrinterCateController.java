package com.fuint.module.backendApi.controller;

import com.fuint.common.dto.AccountInfo;
import com.fuint.common.service.PrinterCateService;
import com.fuint.common.service.PrinterService;
import com.fuint.common.service.CateService;
import com.fuint.common.util.TokenUtil;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.framework.web.BaseController;
import com.fuint.framework.web.ResponseObject;
import com.fuint.repository.model.MtPrinter;
import com.fuint.repository.model.MtPrinterCate;
import com.fuint.repository.model.MtGoodsCate;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 打印机分类关联管理控制器
 *
 * @Created by FSQ
 */
@Api(tags="管理端-打印机分类关联相关接口")
@RestController
@AllArgsConstructor
@RequestMapping(value = "/backendApi/printerCate")
public class BackendPrinterCateController extends BaseController {

    private PrinterCateService printerCateService;
    
    private PrinterService printerService;
    
    private CateService cateService;

    /**
     * 查询打印机分类关联列表
     */
    @ApiOperation(value = "查询打印机分类关联列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    @CrossOrigin
    public ResponseObject list(HttpServletRequest request,
                              @RequestParam(value = "printerId", required = false) Integer printerId,
                              @RequestParam(value = "cateId", required = false) Integer cateId) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Map<String, Object> params = new HashMap<>();
        if (printerId != null) {
            params.put("printerId", printerId);
        }
        if (cateId != null) {
            params.put("cateId", cateId);
        }
        params.put("merchantId", accountInfo.getMerchantId());
        params.put("storeId", accountInfo.getStoreId());

        List<MtPrinterCate> dataList = printerCateService.queryPrinterCateListByParams(params);

        Map<String, Object> result = new HashMap<>();
        result.put("dataList", dataList);

        return getSuccessResult(result);
    }

    /**
     * 保存打印机分类关联
     */
    @ApiOperation(value = "保存打印机分类关联")
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    @CrossOrigin
    public ResponseObject save(HttpServletRequest request, @RequestBody Map<String, Object> params) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Integer printerId = params.get("printerId") != null ? Integer.parseInt(params.get("printerId").toString()) : null;
        List<Integer> cateIds = (List<Integer>) params.get("cateIds");

        if (printerId == null) {
            return getFailureResult(3008, "打印机ID不能为空");
        }

        printerCateService.savePrinterCateRelations(printerId, cateIds, accountInfo.getMerchantId(), accountInfo.getStoreId(), accountInfo.getAccountName());

        return getSuccessResult(true);
    }

    /**
     * 获取打印机列表
     */
    @ApiOperation(value = "获取打印机列表")
    @RequestMapping(value = "/printers", method = RequestMethod.GET)
    @CrossOrigin
    public ResponseObject getPrinters(HttpServletRequest request) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("merchantId", accountInfo.getMerchantId());
        params.put("storeId", accountInfo.getStoreId());
        params.put("status", "A");
        params.put("type", "LABEL");

        List<MtPrinter> printers = printerService.queryPrinterListByParams(params);

        return getSuccessResult(printers);
    }

    /**
     * 获取商品分类列表
     */
    @ApiOperation(value = "获取商品分类列表")
    @RequestMapping(value = "/categories", method = RequestMethod.GET)
    @CrossOrigin
    public ResponseObject getCategories(HttpServletRequest request) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("merchantId", accountInfo.getMerchantId());
        params.put("storeId", accountInfo.getStoreId());
        params.put("status", "A");

        List<MtGoodsCate> categories = cateService.queryCateListByParams(params);

        return getSuccessResult(categories);
    }

    /**
     * 根据打印机ID获取关联的分类
     */
    @ApiOperation(value = "根据打印机ID获取关联的分类")
    @RequestMapping(value = "/printer/{printerId}/categories", method = RequestMethod.GET)
    @CrossOrigin
    public ResponseObject getPrinterCategories(HttpServletRequest request, @PathVariable Integer printerId) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");
        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Map<String, Object> params = new HashMap<>();
        params.put("printerId", printerId);
        params.put("merchantId", accountInfo.getMerchantId());
        params.put("storeId", accountInfo.getStoreId());

        List<MtPrinterCate> printerCates = printerCateService.queryPrinterCateListByParams(params);

        return getSuccessResult(printerCates);
    }

}
