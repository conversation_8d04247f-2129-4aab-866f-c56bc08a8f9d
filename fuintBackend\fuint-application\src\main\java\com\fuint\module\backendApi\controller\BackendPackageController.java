package com.fuint.module.backendApi.controller;

import com.fuint.common.dto.AccountInfo;
import com.fuint.common.dto.PackageGroupDto;
import com.fuint.common.enums.StatusEnum;
import com.fuint.common.service.AccountService;
import com.fuint.common.service.GoodsService;
import com.fuint.common.service.PackageService;
import com.fuint.common.util.TokenUtil;
import com.fuint.framework.exception.BusinessCheckException;
import com.fuint.framework.web.BaseController;
import com.fuint.framework.web.ResponseObject;
import com.fuint.repository.model.MtPackageGroup;
import com.fuint.repository.model.MtPackageGroupItem;
import com.fuint.repository.model.TAccount;
import com.fuint.utils.StringUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.AllArgsConstructor;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

import java.lang.reflect.InvocationTargetException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 套餐管理controller
 */
@Api(tags="管理端-套餐相关接口")
@RestController
@AllArgsConstructor
@RequestMapping(value = "/backendApi/goods/package")
public class BackendPackageController extends BaseController {

    /**
     * 套餐服务接口
     */
    private PackageService packageService;

    /**
     * 商品服务接口
     */
    private GoodsService goodsService;

    /**
     * 后台账户服务接口
     */
    private AccountService accountService;

    /**
     * 获取套餐项目列表
     *
     * @param request HttpServletRequest对象
     * @param goodsId 套餐商品ID
     * @return
     * @throws IllegalAccessException 
     * @throws InvocationTargetException 
     */
    @ApiOperation(value = "获取套餐项目列表")
    @RequestMapping(value = "/list/{goodsId}", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:index')")
    public ResponseObject list(HttpServletRequest request, @PathVariable("goodsId") Integer goodsId) throws BusinessCheckException, InvocationTargetException, IllegalAccessException {
        String token = request.getHeader("Access-Token");

        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        // 获取套餐项目分组及商品
        List<PackageGroupDto> groups = packageService.getPackageGroupsByGoodsId(goodsId);

        Map<String, Object> result = new HashMap<>();
        result.put("groups", groups);

        return getSuccessResult(result);
    }

    /**
     * 保存套餐分组
     *
     * @param request HttpServletRequest对象
     * @return
     */
    @ApiOperation(value = "保存套餐分组")
    @RequestMapping(value = "/saveGroup", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:add')")
    public ResponseObject saveGroup(HttpServletRequest request, @RequestBody Map<String, Object> param) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");

        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Integer goodsId = param.get("goodsId") == null ? 0 : Integer.parseInt(param.get("goodsId").toString());
        Integer id = param.get("id") == null ? 0 : Integer.parseInt(param.get("id").toString());
        String groupName = param.get("groupName") == null ? "" : param.get("groupName").toString();
        String groupType = param.get("groupType") == null ? "R" : param.get("groupType").toString();
        Integer selectCount = param.get("selectCount") == null ? 1 : Integer.parseInt(param.get("selectCount").toString());
        Integer sort = param.get("sort") == null ? 0 : Integer.parseInt(param.get("sort").toString());

        MtPackageGroup packageGroup = new MtPackageGroup();
        if (id > 0) {
            packageGroup = packageService.getPackageGroupById(id);
        }

        packageGroup.setGoodsId(goodsId);
        packageGroup.setGroupName(groupName);
        packageGroup.setGroupType(groupType);
        packageGroup.setSelectCount(selectCount);
        packageGroup.setSort(sort);

        packageGroup = packageService.savePackageGroup(packageGroup);

        Map<String, Object> result = new HashMap<>();
        result.put("packageGroup", packageGroup);

        return getSuccessResult(result);
    }

    /**
     * 保存套餐分组商品
     *
     * @param request HttpServletRequest对象
     * @return
     */
    @ApiOperation(value = "保存套餐分组商品")
    @RequestMapping(value = "/saveGroupItem", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:add')")
    public ResponseObject saveGroupItem(HttpServletRequest request, @RequestBody Map<String, Object> param) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");

        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Integer id = param.get("id") == null ? 0 : Integer.parseInt(param.get("id").toString());
        Integer groupId = param.get("groupId") == null ? 0 : Integer.parseInt(param.get("groupId").toString());
        Integer goodsId = param.get("goodsId") == null ? 0 : Integer.parseInt(param.get("goodsId").toString());
        Integer itemGoodsId = param.get("itemGoodsId") == null ? 0 : Integer.parseInt(param.get("itemGoodsId").toString());
        Integer quantity = param.get("quantity") == null ? 1 : Integer.parseInt(param.get("quantity").toString());
        BigDecimal extraPrice = param.get("extraPrice") == null ? new BigDecimal(0) : new BigDecimal(param.get("extraPrice").toString());
        Integer sort = param.get("sort") == null ? 0 : Integer.parseInt(param.get("sort").toString());

        MtPackageGroupItem groupItem = new MtPackageGroupItem();
        if (id > 0) {
            groupItem = packageService.getPackageGroupItemById(id);
        }

        groupItem.setGroupId(groupId);
        groupItem.setGoodsId(goodsId);
        groupItem.setItemGoodsId(itemGoodsId);
        groupItem.setQuantity(quantity);
        groupItem.setExtraPrice(extraPrice);
        groupItem.setSort(sort);

        groupItem = packageService.savePackageGroupItem(groupItem);

        Map<String, Object> result = new HashMap<>();
        result.put("packageGroupItem", groupItem);

        return getSuccessResult(result);
    }

    /**
     * 删除套餐分组
     *
     * @param request HttpServletRequest对象
     * @param id 分组ID
     * @return
     */
    @ApiOperation(value = "删除套餐分组")
    @RequestMapping(value = "/deleteGroup/{id}", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:edit')")
    public ResponseObject deleteGroup(HttpServletRequest request, @PathVariable("id") Integer id) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");

        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        String operator = accountInfo.getAccountName();
        packageService.deletePackageGroup(id, operator);

        return getSuccessResult(true);
    }

    /**
     * 删除套餐分组商品
     *
     * @param request HttpServletRequest对象
     * @param id 商品ID
     * @return
     */
    @ApiOperation(value = "删除套餐分组商品")
    @RequestMapping(value = "/deleteGroupItem/{id}", method = RequestMethod.GET)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:edit')")
    public ResponseObject deleteGroupItem(HttpServletRequest request, @PathVariable("id") Integer id) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");

        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        String operator = accountInfo.getAccountName();
        packageService.deletePackageGroupItem(id, operator);

        return getSuccessResult(true);
    }

    /**
     * 批量保存套餐数据
     *
     * @param request HttpServletRequest对象
     * @return
     */
    @ApiOperation(value = "批量保存套餐数据")
    @RequestMapping(value = "/batchSave", method = RequestMethod.POST)
    @CrossOrigin
    @PreAuthorize("@pms.hasPermission('goods:goods:add')")
    public ResponseObject batchSave(HttpServletRequest request, @RequestBody Map<String, Object> param) throws BusinessCheckException {
        String token = request.getHeader("Access-Token");

        AccountInfo accountInfo = TokenUtil.getAccountInfoByToken(token);
        if (accountInfo == null) {
            return getFailureResult(1001, "请先登录");
        }

        Integer goodsId = param.get("goodsId") == null ? 0 : Integer.parseInt(param.get("goodsId").toString());
        List<Map<String, Object>> groups = param.get("groups") == null ? new ArrayList<>() : (List<Map<String, Object>>) param.get("groups");

        if (goodsId <= 0) {
            return getFailureResult(201, "套餐商品ID不能为空");
        }

        packageService.batchSavePackageData(goodsId, groups, accountInfo.getAccountName());

        return getSuccessResult(true);
    }
}