
0391cf3e9f96d18bbc5720a6d5f8fe6177dfd03a	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"static\\u002Fjs\\u002F0.js\",\"contentHash\":\"334f55a2aabf80d50b8c2f5d0a552cf4\"}","integrity":"sha512-FSVcyMRogDnwHzBaDWZi15fbfV1znZRViB2C/c3H8GcsD2ypoxEyVVX9alxtWm5re0hdVswVM1exPSvupKjBvw==","time":1755006369287,"size":6676345}