
b1d08906099c1dac1128d7226dcbfe6d6bfa3618	{"key":"{\"nodeVersion\":\"v22.18.0\",\"compression-webpack-plugin\":\"5.0.2\",\"algorithm\":function asyncBufferWrapper(buffer, opts, callback) {\r\n    if (typeof opts === 'function') {\r\n      callback = opts;\r\n      opts = {};\r\n    }\r\n    return zlibBuffer(new ctor(opts), buffer, callback);\r\n  },\"originalAlgorithm\":\"gzip\",\"compressionOptions\":{\"level\":9},\"assetName\":\"0.82f1720a358ae07d6fb1.hot-update.js\",\"contentHash\":\"0c274b7359c4057d2a7dcfad300c3ae7\"}","integrity":"sha512-vIp6yto+fqtOVftkl3QCVLtODFKDhV8OHkqBm3Oq1vcscGNDPClP/WQiwBBYT6gfCRuSE5dkOWzXReXmPhBSIQ==","time":1755006351907,"size":30223}