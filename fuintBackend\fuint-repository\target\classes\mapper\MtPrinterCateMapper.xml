<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.fuint.repository.mapper.MtPrinterCateMapper">

    <!-- 结果映射 -->
    <resultMap type="com.fuint.repository.model.MtPrinterCate" id="MtPrinterCateResult">
        <result property="id" column="id"/>
        <result property="printerId" column="printer_id"/>
        <result property="cateId" column="cate_id"/>
        <result property="merchantId" column="merchant_id"/>
        <result property="storeId" column="store_id"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="operator" column="operator"/>
    </resultMap>

    <!-- 根据参数查询打印机分类关联列表 -->
    <select id="selectByParams" parameterType="java.util.Map" resultMap="MtPrinterCateResult">
        SELECT *
        FROM mt_printer_cate
        WHERE status = 'A'
        <if test="params.printerId != null and params.printerId != ''">
            AND printer_id = #{params.printerId}
        </if>
        <if test="params.cateId != null and params.cateId != ''">
            AND cate_id = #{params.cateId}
        </if>
        <if test="params.merchantId != null and params.merchantId != ''">
            AND merchant_id = #{params.merchantId}
        </if>
        <if test="params.storeId != null and params.storeId != ''">
            AND store_id = #{params.storeId}
        </if>
        ORDER BY id ASC
    </select>

    <!-- 根据打印机ID删除关联 -->
    <delete id="deleteByPrinterId" parameterType="java.lang.Integer">
        UPDATE mt_printer_cate
        SET status = 'D', update_time = NOW()
        WHERE printer_id = #{printerId} AND status = 'A'
    </delete>

    <!-- 根据分类ID删除关联 -->
    <delete id="deleteByCateId" parameterType="java.lang.Integer">
        UPDATE mt_printer_cate
        SET status = 'D', update_time = NOW()
        WHERE cate_id = #{cateId} AND status = 'A'
    </delete>

    <!-- 根据商品分类ID查询关联的打印机ID列表 -->
    <select id="selectPrinterIdsByCateId" resultType="java.lang.Integer">
        SELECT DISTINCT printer_id
        FROM mt_printer_cate
        WHERE cate_id = #{cateId}
          AND store_id = #{storeId}
          AND status = 'A'
        ORDER BY printer_id ASC
    </select>

</mapper>
