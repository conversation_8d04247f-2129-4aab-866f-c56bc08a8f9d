package com.fuint.common.enums;

/**
 * 取餐码状态枚举
 */
public enum PickupCodeStatusEnum {
    PREPARING("PREPARING", "制作中"),
    COMPLETED("COMPLETED", "已完成"),
    PICKED("PICKED", "已取走");

    private String key;
    private String value;

    PickupCodeStatusEnum(String key, String value) {
        this.key = key;
        this.value = value;
    }

    
    public String getKey() {
        return key;
    } 

    public String getValue() {
        return value;
    }
}
