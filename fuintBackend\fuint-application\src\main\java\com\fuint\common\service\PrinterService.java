package com.fuint.common.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.fuint.common.dto.UserOrderDto;
import com.fuint.framework.pagination.PaginationRequest;
import com.fuint.framework.pagination.PaginationResponse;
import com.fuint.repository.model.MtPrinter;
import com.fuint.framework.exception.BusinessCheckException;
import java.util.List;
import java.util.Map;
import java.util.Date;

/**
 * 打印机业务接口
 *

 */
public interface PrinterService extends IService<MtPrinter> {

    /**
     * 分页查询列表
     *
     * @param paginationRequest
     * @return
     */
    PaginationResponse<MtPrinter> queryPrinterListByPagination(PaginationRequest paginationRequest) throws BusinessCheckException;

    /**
     * 添加打印机
     *
     * @param  mtPrinter
     * @throws BusinessCheckException
     * @return
     */
    MtPrinter addPrinter(MtPrinter mtPrinter) throws BusinessCheckException;

    /**
     * 打印订单
     *
     * @param orderInfo 订单信息
     * @param autoPrint 自动打印
     * @param beforePay 支付前打印
     * @param afterPay 支付后打印
     * @param goodsIds 打印的商品Id
     * @return
     * */
    Boolean printOrder(UserOrderDto orderInfo, boolean autoPrint, boolean beforePay, boolean afterPay, List<Integer> goodsIds) throws Exception;

    /**
     * 打印订单（仅小票）
     *
     * @param orderInfo 订单信息
     * @param autoPrint 自动打印
     * @param beforePay 支付前打印
     * @param afterPay 支付后打印
     * @param goodsIds 打印的商品Id
     * @param printLabel 是否同时打印标签
     * @return
     * */
    Boolean printOrder(UserOrderDto orderInfo, boolean autoPrint, boolean beforePay, boolean afterPay, List<Integer> goodsIds, boolean printLabel) throws Exception;

    /**
     * 根据ID获取打印机信息
     *
     * @param id ID
     * @throws BusinessCheckException
     * @return
     */
    MtPrinter queryPrinterById(Integer id) throws BusinessCheckException;

    /**
     * 根据ID删除打印机
     *
     * @param id ID
     * @param operator 操作人
     * @throws BusinessCheckException
     * @return
     */
    void deletePrinter(Integer id, String operator) throws BusinessCheckException;

    /**
     * 更新打印机
     * @param  mtPrinter
     * @throws BusinessCheckException
     * @return
     * */
    MtPrinter updatePrinter(MtPrinter mtPrinter) throws BusinessCheckException;

    /**
     * 根据条件搜索打印机
     *
     * @param params 查询参数
     * @throws BusinessCheckException
     * @return
     * */
    List<MtPrinter> queryPrinterListByParams(Map<String, Object> params) throws BusinessCheckException;

    /**
     * 打印订单标签
     *
     * @param orderInfo 订单信息
     * @return
     * */
    Boolean printOrderLabel(UserOrderDto orderInfo) throws Exception;

    /**
     * 打印有效期标签
     *
     * @param name 商品名称
     * @param productDate 生产日期
     * @param expiryDate 过期日期
     * @param printerId printerId
     * @return
     * */
    Boolean printExpiryLabel(String name, Date productDate, Date expiryDate, String remark, Integer printerId, Integer merchantId) throws Exception;

    /**
     * 根据商品分类查找标签打印机
     *
     * @param cateId 商品分类ID
     * @param storeId 店铺ID
     * @return 打印机列表
     * */
    List<MtPrinter> queryLabelPrintersByCateId(Integer cateId, Integer storeId) throws BusinessCheckException;
}
